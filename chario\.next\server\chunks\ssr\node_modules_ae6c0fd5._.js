module.exports = {

"[project]/node_modules/@react-email/render/dist/node/index.mjs [app-rsc] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_next_dist_compiled_react-dom_server_2e3405a5.js",
  "server/chunks/ssr/node_modules_b6c05be9._.js",
  "server/chunks/ssr/[root-of-the-server]__02325f31._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@react-email/render/dist/node/index.mjs [app-rsc] (ecmascript)");
    });
});
}}),
"[project]/node_modules/better-auth/dist/chunks/bun-sqlite-dialect.mjs [app-rsc] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_better-auth_dist_chunks_bun-sqlite-dialect_mjs_7bc57080._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/better-auth/dist/chunks/bun-sqlite-dialect.mjs [app-rsc] (ecmascript)");
    });
});
}}),
"[project]/node_modules/next/headers.js [app-rsc] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/next/headers.js [app-rsc] (ecmascript)");
    });
});
}}),

};