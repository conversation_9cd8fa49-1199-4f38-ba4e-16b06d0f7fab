"use client"
import { useForm } from 'react-hook-form'
import CardWrapper from '@/components/md/card-wrapper'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { z } from 'zod'
import { zodR<PERSON>olver } from '@hookform/resolvers/zod'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { FormError, FormSuccess } from "@/components/sm/form-status";
import { useAuthState } from '@/hooks/useAuthState'
import { authClient } from '@/lib/auth-client'
import { ForgotPasswordSchema } from '@/helpers/zod/forgot-password-schema'


const ForgotPassword = () => {
    const { error, success, loading, setError, setSuccess, setLoading, resetState } = useAuthState()

    const form = useForm({
        resolver: zodResolver(ForgotPasswordSchema),
        defaultValues: {
            email: '',
        }
    })

    const onSubmit = async (values) => {
        try {
            await authClient.forgetPassword({
                email: values.email,
                redirectTo: "/reset-password"
            }, {
                onResponse: () => {
                    setLoading(false)
                },
                onRequest: () => {
                    resetState()
                    setLoading(true)
                },
                onSuccess: () => {
                    setSuccess("Reset password link has been sent")
                },
                onError: (ctx) => {
                    setError(ctx.error.message);
                },
            });

        } catch (error) {
            console.log(error)
            setError("Something went wrong")
        }
    }

    return (
        <CardWrapper
            cardTitle='Forgot Password'
            cardDescription='Enter your email to send link to reset password'
            cardFooterDescription="Remember your password?"
            cardFooterLink='/signin'
            cardFooterLinkTitle='Signin'
        >
            <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                    <FormField
                        control={form.control}
                        name="email"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Email</FormLabel>
                                <FormControl>
                                    <Input
                                        disabled={loading}
                                        type="email"
                                        placeholder='<EMAIL>'
                                        {...field}
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                    <FormError message={error} />
                    <FormSuccess message={success} />
                    <Button disabled={loading} type="submit" className='w-full cursor-pointer'>Submit</Button>
                </form>
            </Form>

        </CardWrapper>
    )
}

export default ForgotPassword