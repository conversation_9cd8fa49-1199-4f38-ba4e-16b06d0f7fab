(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/secp256k1.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/9de0f_@noble_08514a45._.js",
  "static/chunks/9de0f_@noble_curves_esm_secp256k1_b1fd015a.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/secp256k1.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit/dist/esm/exports/core.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_47b1565a._.js",
  "static/chunks/node_modules_@reown_f9da6588._.js",
  "static/chunks/node_modules_@reown_cf9f4b9c._.js",
  "static/chunks/node_modules_@reown_527d1b46._.js",
  "static/chunks/node_modules_@reown_389cdfb5._.js",
  "static/chunks/node_modules_@reown_appkit-controllers_dist_esm_src_fb1c7a3c._.js",
  "static/chunks/node_modules_@reown_appkit_dist_esm_bc92fc23._.js",
  "static/chunks/node_modules_cda593ef._.js",
  "static/chunks/node_modules_@reown_appkit_dist_esm_exports_core_7316b8e0.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit/dist/esm/exports/core.js [app-client] (ecmascript)");
    });
});
}}),
}]);