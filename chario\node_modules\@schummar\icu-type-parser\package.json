{"name": "@schummar/icu-type-parser", "repository": "schummar/schummar-translate", "version": "1.21.5", "description": "TypeScript powered ICU message parser.", "keywords": ["typescript", "i18n", "localization", "icu", "message", "parser", "type checking", "autocomplete", "IntelliSense"], "type": "module", "sideEffects": false, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "exports": {".": {"types": "./dist/index.d.ts"}}, "typesVersions": {".": {"*": ["dist/index.d.ts"]}}, "types": "dist/index.d.ts", "scripts": {"build": "tsup", "publint": "publint", "prepublishOnly": "runp build", "release": "release-it --ci -c ../../.release-it-package.ts"}, "publishConfig": {"access": "public"}, "files": ["dist"]}