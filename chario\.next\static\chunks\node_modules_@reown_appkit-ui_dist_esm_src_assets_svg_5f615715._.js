(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/add.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_add_f682d271.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_add_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/add.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/all-wallets.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_all-wallets_a988b02c.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_all-wallets_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/all-wallets.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/arrow-bottom-circle.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/dd92d_modules_@reown_appkit-ui_dist_esm_src_assets_svg_arrow-bottom-circle_27e67b43.js",
  "static/chunks/dd92d_modules_@reown_appkit-ui_dist_esm_src_assets_svg_arrow-bottom-circle_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/arrow-bottom-circle.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/app-store.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_app-store_284c216e.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_app-store_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/app-store.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/apple.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_apple_f0dc23a9.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_apple_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/apple.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/arrow-bottom.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_arrow-bottom_dc473fd4.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_arrow-bottom_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/arrow-bottom.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/arrow-left.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_arrow-left_9fc62db2.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_arrow-left_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/arrow-left.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/arrow-right.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_arrow-right_9eac166a.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_arrow-right_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/arrow-right.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/arrow-top.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_arrow-top_167f6bb6.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_arrow-top_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/arrow-top.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/bank.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_bank_9a0afe7a.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_bank_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/bank.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/browser.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_browser_af8bc36c.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_browser_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/browser.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/card.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_card_c4d768af.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_card_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/card.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/checkmark.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_checkmark_0e554a7d.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_checkmark_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/checkmark.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/checkmark-bold.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_checkmark-bold_b3175261.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_checkmark-bold_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/checkmark-bold.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/chevron-bottom.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_chevron-bottom_490da78f.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_chevron-bottom_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/chevron-bottom.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/chevron-left.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_chevron-left_e54a321f.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_chevron-left_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/chevron-left.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/chevron-right.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_chevron-right_1d2b89f7.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_chevron-right_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/chevron-right.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/chevron-top.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_chevron-top_d82411cd.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_chevron-top_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/chevron-top.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/chrome-store.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_chrome-store_bd9d6c50.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_chrome-store_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/chrome-store.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/clock.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_clock_022c9d45.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_clock_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/clock.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/close.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_close_992c368f.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_close_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/close.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/compass.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_compass_9542df14.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_compass_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/compass.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/coinPlaceholder.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_coinPlaceholder_bda51f3c.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_coinPlaceholder_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/coinPlaceholder.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/copy.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_copy_703c3021.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_copy_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/copy.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/cursor.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_cursor_61747c81.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_cursor_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/cursor.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/cursor-transparent.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/dd92d_modules_@reown_appkit-ui_dist_esm_src_assets_svg_cursor-transparent_9e7d99fa.js",
  "static/chunks/dd92d_modules_@reown_appkit-ui_dist_esm_src_assets_svg_cursor-transparent_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/cursor-transparent.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/desktop.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_desktop_37b51b24.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_desktop_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/desktop.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/disconnect.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_disconnect_1f40c127.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_disconnect_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/disconnect.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/discord.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_discord_deaa4052.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_discord_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/discord.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/etherscan.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_etherscan_1f751068.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_etherscan_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/etherscan.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/extension.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_extension_dba28bb0.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_extension_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/extension.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/external-link.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_external-link_90e704ca.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_external-link_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/external-link.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/facebook.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_facebook_534e09e2.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_facebook_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/facebook.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/farcaster.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_farcaster_3622b3e0.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_farcaster_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/farcaster.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/filters.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_filters_836db873.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_filters_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/filters.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/github.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_github_2cc31fe7.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_github_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/github.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/google.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_google_77154c9c.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_google_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/google.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/help-circle.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_help-circle_f9c947ac.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_help-circle_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/help-circle.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/image.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_image_2d594a43.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_image_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/image.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/id.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_id_b06b4763.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_id_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/id.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/info-circle.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_info-circle_db9b1ce2.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_info-circle_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/info-circle.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/lightbulb.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_lightbulb_3f6d83ae.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_lightbulb_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/lightbulb.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/mail.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_mail_1e35ae21.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_mail_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/mail.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/mobile.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_mobile_7a76821b.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_mobile_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/mobile.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/more.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_more_69ed5c19.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_more_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/more.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/network-placeholder.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/dd92d_modules_@reown_appkit-ui_dist_esm_src_assets_svg_network-placeholder_ac5fffa0.js",
  "static/chunks/dd92d_modules_@reown_appkit-ui_dist_esm_src_assets_svg_network-placeholder_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/network-placeholder.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/nftPlaceholder.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_nftPlaceholder_f294e77c.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_nftPlaceholder_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/nftPlaceholder.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/off.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_off_8ca536b3.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_off_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/off.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/play-store.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_play-store_99db88df.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_play-store_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/play-store.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/plus.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_plus_a199cba0.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_plus_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/plus.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/qr-code.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_qr-code_9a0c6f56.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_qr-code_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/qr-code.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/recycle-horizontal.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/dd92d_modules_@reown_appkit-ui_dist_esm_src_assets_svg_recycle-horizontal_d4e46a1d.js",
  "static/chunks/dd92d_modules_@reown_appkit-ui_dist_esm_src_assets_svg_recycle-horizontal_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/recycle-horizontal.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/refresh.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_refresh_c4e2d965.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_refresh_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/refresh.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/search.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_search_42a622f4.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_search_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/search.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/send.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_send_ab755599.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_send_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/send.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/swapHorizontal.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_swapHorizontal_5f731c00.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_swapHorizontal_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/swapHorizontal.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/swapHorizontalMedium.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/dd92d_modules_@reown_appkit-ui_dist_esm_src_assets_svg_swapHorizontalMedium_0ea040ec.js",
  "static/chunks/dd92d_modules_@reown_appkit-ui_dist_esm_src_assets_svg_swapHorizontalMedium_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/swapHorizontalMedium.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/swapHorizontalBold.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/dd92d_modules_@reown_appkit-ui_dist_esm_src_assets_svg_swapHorizontalBold_48458280.js",
  "static/chunks/dd92d_modules_@reown_appkit-ui_dist_esm_src_assets_svg_swapHorizontalBold_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/swapHorizontalBold.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/swapHorizontalRoundedBold.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/8069e_@reown_appkit-ui_dist_esm_src_assets_svg_swapHorizontalRoundedBold_0e9118e4.js",
  "static/chunks/8069e_@reown_appkit-ui_dist_esm_src_assets_svg_swapHorizontalRoundedBold_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/swapHorizontalRoundedBold.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/swapVertical.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_swapVertical_f65777df.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_swapVertical_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/swapVertical.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/telegram.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_telegram_0009516c.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_telegram_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/telegram.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/three-dots.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_three-dots_63afc010.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_three-dots_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/three-dots.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/twitch.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_twitch_1c528228.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_twitch_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/twitch.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/x.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_x_8de50a5c.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_x_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/x.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/twitterIcon.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_twitterIcon_c215d451.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_twitterIcon_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/twitterIcon.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/verify.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_verify_0183f841.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_verify_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/verify.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/verify-filled.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_verify-filled_403e09b7.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_verify-filled_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/verify-filled.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/wallet.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_wallet_44945c56.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_wallet_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/wallet.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/walletconnect.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_walletconnect_abc30747.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_walletconnect_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/walletconnect.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/wallet-placeholder.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/dd92d_modules_@reown_appkit-ui_dist_esm_src_assets_svg_wallet-placeholder_a8ce3179.js",
  "static/chunks/dd92d_modules_@reown_appkit-ui_dist_esm_src_assets_svg_wallet-placeholder_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/wallet-placeholder.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/warning-circle.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_warning-circle_615eecc5.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_warning-circle_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/warning-circle.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/info.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_info_ca11e3a1.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_info_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/info.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/exclamation-triangle.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/dd92d_modules_@reown_appkit-ui_dist_esm_src_assets_svg_exclamation-triangle_00b22906.js",
  "static/chunks/dd92d_modules_@reown_appkit-ui_dist_esm_src_assets_svg_exclamation-triangle_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/exclamation-triangle.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/reown-logo.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_reown-logo_ac42a59e.js",
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_reown-logo_2b06ceb6.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/reown-logo.js [app-client] (ecmascript)");
    });
});
}}),
}]);