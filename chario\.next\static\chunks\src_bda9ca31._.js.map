{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/js/web3/packages/chario/src/components/ui/dropdown-menu.jsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\r\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction DropdownMenu({\r\n  ...props\r\n}) {\r\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />;\r\n}\r\n\r\nfunction DropdownMenuPortal({\r\n  ...props\r\n}) {\r\n  return (<DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />);\r\n}\r\n\r\nfunction DropdownMenuTrigger({\r\n  ...props\r\n}) {\r\n  return (<DropdownMenuPrimitive.Trigger data-slot=\"dropdown-menu-trigger\" {...props} />);\r\n}\r\n\r\nfunction DropdownMenuContent({\r\n  className,\r\n  sideOffset = 4,\r\n  ...props\r\n}) {\r\n  return (\r\n    (<DropdownMenuPrimitive.Portal>\r\n      <DropdownMenuPrimitive.Content\r\n        data-slot=\"dropdown-menu-content\"\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\r\n          className\r\n        )}\r\n        {...props} />\r\n    </DropdownMenuPrimitive.Portal>)\r\n  );\r\n}\r\n\r\nfunction DropdownMenuGroup({\r\n  ...props\r\n}) {\r\n  return (<DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />);\r\n}\r\n\r\nfunction DropdownMenuItem({\r\n  className,\r\n  inset,\r\n  variant = \"default\",\r\n  ...props\r\n}) {\r\n  return (\r\n    (<DropdownMenuPrimitive.Item\r\n      data-slot=\"dropdown-menu-item\"\r\n      data-inset={inset}\r\n      data-variant={variant}\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props} />)\r\n  );\r\n}\r\n\r\nfunction DropdownMenuCheckboxItem({\r\n  className,\r\n  children,\r\n  checked,\r\n  ...props\r\n}) {\r\n  return (\r\n    (<DropdownMenuPrimitive.CheckboxItem\r\n      data-slot=\"dropdown-menu-checkbox-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      checked={checked}\r\n      {...props}>\r\n      <span\r\n        className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\r\n        <DropdownMenuPrimitive.ItemIndicator>\r\n          <CheckIcon className=\"size-4\" />\r\n        </DropdownMenuPrimitive.ItemIndicator>\r\n      </span>\r\n      {children}\r\n    </DropdownMenuPrimitive.CheckboxItem>)\r\n  );\r\n}\r\n\r\nfunction DropdownMenuRadioGroup({\r\n  ...props\r\n}) {\r\n  return (<DropdownMenuPrimitive.RadioGroup data-slot=\"dropdown-menu-radio-group\" {...props} />);\r\n}\r\n\r\nfunction DropdownMenuRadioItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}) {\r\n  return (\r\n    (<DropdownMenuPrimitive.RadioItem\r\n      data-slot=\"dropdown-menu-radio-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}>\r\n      <span\r\n        className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\r\n        <DropdownMenuPrimitive.ItemIndicator>\r\n          <CircleIcon className=\"size-2 fill-current\" />\r\n        </DropdownMenuPrimitive.ItemIndicator>\r\n      </span>\r\n      {children}\r\n    </DropdownMenuPrimitive.RadioItem>)\r\n  );\r\n}\r\n\r\nfunction DropdownMenuLabel({\r\n  className,\r\n  inset,\r\n  ...props\r\n}) {\r\n  return (\r\n    (<DropdownMenuPrimitive.Label\r\n      data-slot=\"dropdown-menu-label\"\r\n      data-inset={inset}\r\n      className={cn(\"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\", className)}\r\n      {...props} />)\r\n  );\r\n}\r\n\r\nfunction DropdownMenuSeparator({\r\n  className,\r\n  ...props\r\n}) {\r\n  return (\r\n    (<DropdownMenuPrimitive.Separator\r\n      data-slot=\"dropdown-menu-separator\"\r\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\r\n      {...props} />)\r\n  );\r\n}\r\n\r\nfunction DropdownMenuShortcut({\r\n  className,\r\n  ...props\r\n}) {\r\n  return (\r\n    (<span\r\n      data-slot=\"dropdown-menu-shortcut\"\r\n      className={cn(\"text-muted-foreground ml-auto text-xs tracking-widest\", className)}\r\n      {...props} />)\r\n  );\r\n}\r\n\r\nfunction DropdownMenuSub({\r\n  ...props\r\n}) {\r\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />;\r\n}\r\n\r\nfunction DropdownMenuSubTrigger({\r\n  className,\r\n  inset,\r\n  children,\r\n  ...props\r\n}) {\r\n  return (\r\n    (<DropdownMenuPrimitive.SubTrigger\r\n      data-slot=\"dropdown-menu-sub-trigger\"\r\n      data-inset={inset}\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\r\n        className\r\n      )}\r\n      {...props}>\r\n      {children}\r\n      <ChevronRightIcon className=\"ml-auto size-4\" />\r\n    </DropdownMenuPrimitive.SubTrigger>)\r\n  );\r\n}\r\n\r\nfunction DropdownMenuSubContent({\r\n  className,\r\n  ...props\r\n}) {\r\n  return (\r\n    (<DropdownMenuPrimitive.SubContent\r\n      data-slot=\"dropdown-menu-sub-content\"\r\n      className={cn(\r\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\r\n        className\r\n      )}\r\n      {...props} />)\r\n  );\r\n}\r\n\r\nexport {\r\n  DropdownMenu,\r\n  DropdownMenuPortal,\r\n  DropdownMenuTrigger,\r\n  DropdownMenuContent,\r\n  DropdownMenuGroup,\r\n  DropdownMenuLabel,\r\n  DropdownMenuItem,\r\n  DropdownMenuCheckboxItem,\r\n  DropdownMenuRadioGroup,\r\n  DropdownMenuRadioItem,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuShortcut,\r\n  DropdownMenuSub,\r\n  DropdownMenuSubTrigger,\r\n  DropdownMenuSubContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACJ;IACC,qBAAO,6LAAC,+KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;KAJS;AAMT,SAAS,mBAAmB,EAC1B,GAAG,OACJ;IACC,qBAAQ,6LAAC,+KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAClF;MAJS;AAMT,SAAS,oBAAoB,EAC3B,GAAG,OACJ;IACC,qBAAQ,6LAAC,+KAAA,CAAA,UAA6B;QAAC,aAAU;QAAyB,GAAG,KAAK;;;;;;AACpF;MAJS;AAMT,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACJ;IACC,qBACG,6LAAC,+KAAA,CAAA,SAA4B;kBAC5B,cAAA,6LAAC,+KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAGjB;MAjBS;AAmBT,SAAS,kBAAkB,EACzB,GAAG,OACJ;IACC,qBAAQ,6LAAC,+KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAChF;MAJS;AAMT,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OACJ;IACC,qBACG,6LAAC,+KAAA,CAAA,OAA0B;QAC1B,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAEf;MAjBS;AAmBT,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OACJ;IACC,qBACG,6LAAC,+KAAA,CAAA,eAAkC;QAClC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BACT,6LAAC;gBACC,WAAU;0BACV,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;MAxBS;AA0BT,SAAS,uBAAuB,EAC9B,GAAG,OACJ;IACC,qBAAQ,6LAAC,+KAAA,CAAA,aAAgC;QAAC,aAAU;QAA6B,GAAG,KAAK;;;;;;AAC3F;MAJS;AAMT,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OACJ;IACC,qBACG,6LAAC,+KAAA,CAAA,YAA+B;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BACT,6LAAC;gBACC,WAAU;0BACV,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,6MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;MAtBS;AAwBT,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OACJ;IACC,qBACG,6LAAC,+KAAA,CAAA,QAA2B;QAC3B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,qDAAqD;QAClE,GAAG,KAAK;;;;;;AAEf;MAZS;AAcT,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OACJ;IACC,qBACG,6LAAC,+KAAA,CAAA,YAA+B;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAEf;OAVS;AAYT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACJ;IACC,qBACG,6LAAC;QACA,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yDAAyD;QACtE,GAAG,KAAK;;;;;;AAEf;OAVS;AAYT,SAAS,gBAAgB,EACvB,GAAG,OACJ;IACC,qBAAO,6LAAC,+KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;OAJS;AAMT,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OACJ;IACC,qBACG,6LAAC,+KAAA,CAAA,aAAgC;QAChC,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YACR;0BACD,6LAAC,6NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;OAnBS;AAqBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OACJ;IACC,qBACG,6LAAC,+KAAA,CAAA,aAAgC;QAChC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAEf;OAbS", "debugId": null}}, {"offset": {"line": 305, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/js/web3/packages/chario/src/components/ui/table.jsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Table({\r\n  className,\r\n  ...props\r\n}) {\r\n  return (\r\n    (<div data-slot=\"table-container\" className=\"relative w-full overflow-x-auto\">\r\n      <table\r\n        data-slot=\"table\"\r\n        className={cn(\"w-full caption-bottom text-sm\", className)}\r\n        {...props} />\r\n    </div>)\r\n  );\r\n}\r\n\r\nfunction TableHeader({\r\n  className,\r\n  ...props\r\n}) {\r\n  return (\r\n    (<thead\r\n      data-slot=\"table-header\"\r\n      className={cn(\"[&_tr]:border-b\", className)}\r\n      {...props} />)\r\n  );\r\n}\r\n\r\nfunction TableBody({\r\n  className,\r\n  ...props\r\n}) {\r\n  return (\r\n    (<tbody\r\n      data-slot=\"table-body\"\r\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\r\n      {...props} />)\r\n  );\r\n}\r\n\r\nfunction TableFooter({\r\n  className,\r\n  ...props\r\n}) {\r\n  return (\r\n    (<tfoot\r\n      data-slot=\"table-footer\"\r\n      className={cn(\"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\", className)}\r\n      {...props} />)\r\n  );\r\n}\r\n\r\nfunction TableRow({\r\n  className,\r\n  ...props\r\n}) {\r\n  return (\r\n    (<tr\r\n      data-slot=\"table-row\"\r\n      className={cn(\r\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\r\n        className\r\n      )}\r\n      {...props} />)\r\n  );\r\n}\r\n\r\nfunction TableHead({\r\n  className,\r\n  ...props\r\n}) {\r\n  return (\r\n    (<th\r\n      data-slot=\"table-head\"\r\n      className={cn(\r\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\r\n        className\r\n      )}\r\n      {...props} />)\r\n  );\r\n}\r\n\r\nfunction TableCell({\r\n  className,\r\n  ...props\r\n}) {\r\n  return (\r\n    (<td\r\n      data-slot=\"table-cell\"\r\n      className={cn(\r\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\r\n        className\r\n      )}\r\n      {...props} />)\r\n  );\r\n}\r\n\r\nfunction TableCaption({\r\n  className,\r\n  ...props\r\n}) {\r\n  return (\r\n    (<caption\r\n      data-slot=\"table-caption\"\r\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\r\n      {...props} />)\r\n  );\r\n}\r\n\r\nexport {\r\n  Table,\r\n  TableHeader,\r\n  TableBody,\r\n  TableFooter,\r\n  TableHead,\r\n  TableRow,\r\n  TableCell,\r\n  TableCaption,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AAEA;AAJA;;;;AAMA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OACJ;IACC,qBACG,6LAAC;QAAI,aAAU;QAAkB,WAAU;kBAC1C,cAAA,6LAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAGjB;KAZS;AAcT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACJ;IACC,qBACG,6LAAC;QACA,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAEf;MAVS;AAYT,SAAS,UAAU,EACjB,SAAS,EACT,GAAG,OACJ;IACC,qBACG,6LAAC;QACA,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAEf;MAVS;AAYT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACJ;IACC,qBACG,6LAAC;QACA,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2DAA2D;QACxE,GAAG,KAAK;;;;;;AAEf;MAVS;AAYT,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACJ;IACC,qBACG,6LAAC;QACA,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAEf;MAbS;AAeT,SAAS,UAAU,EACjB,SAAS,EACT,GAAG,OACJ;IACC,qBACG,6LAAC;QACA,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAEf;MAbS;AAeT,SAAS,UAAU,EACjB,SAAS,EACT,GAAG,OACJ;IACC,qBACG,6LAAC;QACA,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAEf;MAbS;AAeT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACJ;IACC,qBACG,6LAAC;QACA,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAEf;MAVS", "debugId": null}}, {"offset": {"line": 445, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/js/web3/packages/chario/src/components/ui/select.jsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\r\nimport { CheckI<PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Select({\r\n  ...props\r\n}) {\r\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />;\r\n}\r\n\r\nfunction SelectGroup({\r\n  ...props\r\n}) {\r\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />;\r\n}\r\n\r\nfunction SelectValue({\r\n  ...props\r\n}) {\r\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />;\r\n}\r\n\r\nfunction SelectTrigger({\r\n  className,\r\n  size = \"default\",\r\n  children,\r\n  ...props\r\n}) {\r\n  return (\r\n    (<SelectPrimitive.Trigger\r\n      data-slot=\"select-trigger\"\r\n      data-size={size}\r\n      className={cn(\r\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}>\r\n      {children}\r\n      <SelectPrimitive.Icon asChild>\r\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\r\n      </SelectPrimitive.Icon>\r\n    </SelectPrimitive.Trigger>)\r\n  );\r\n}\r\n\r\nfunction SelectContent({\r\n  className,\r\n  children,\r\n  position = \"popper\",\r\n  ...props\r\n}) {\r\n  return (\r\n    (<SelectPrimitive.Portal>\r\n      <SelectPrimitive.Content\r\n        data-slot=\"select-content\"\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\r\n          position === \"popper\" &&\r\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\r\n          className\r\n        )}\r\n        position={position}\r\n        {...props}>\r\n        <SelectScrollUpButton />\r\n        <SelectPrimitive.Viewport\r\n          className={cn(\"p-1\", position === \"popper\" &&\r\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\")}>\r\n          {children}\r\n        </SelectPrimitive.Viewport>\r\n        <SelectScrollDownButton />\r\n      </SelectPrimitive.Content>\r\n    </SelectPrimitive.Portal>)\r\n  );\r\n}\r\n\r\nfunction SelectLabel({\r\n  className,\r\n  ...props\r\n}) {\r\n  return (\r\n    (<SelectPrimitive.Label\r\n      data-slot=\"select-label\"\r\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\r\n      {...props} />)\r\n  );\r\n}\r\n\r\nfunction SelectItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}) {\r\n  return (\r\n    (<SelectPrimitive.Item\r\n      data-slot=\"select-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\r\n        className\r\n      )}\r\n      {...props}>\r\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\r\n        <SelectPrimitive.ItemIndicator>\r\n          <CheckIcon className=\"size-4\" />\r\n        </SelectPrimitive.ItemIndicator>\r\n      </span>\r\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n    </SelectPrimitive.Item>)\r\n  );\r\n}\r\n\r\nfunction SelectSeparator({\r\n  className,\r\n  ...props\r\n}) {\r\n  return (\r\n    (<SelectPrimitive.Separator\r\n      data-slot=\"select-separator\"\r\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\r\n      {...props} />)\r\n  );\r\n}\r\n\r\nfunction SelectScrollUpButton({\r\n  className,\r\n  ...props\r\n}) {\r\n  return (\r\n    (<SelectPrimitive.ScrollUpButton\r\n      data-slot=\"select-scroll-up-button\"\r\n      className={cn(\"flex cursor-default items-center justify-center py-1\", className)}\r\n      {...props}>\r\n      <ChevronUpIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollUpButton>)\r\n  );\r\n}\r\n\r\nfunction SelectScrollDownButton({\r\n  className,\r\n  ...props\r\n}) {\r\n  return (\r\n    (<SelectPrimitive.ScrollDownButton\r\n      data-slot=\"select-scroll-down-button\"\r\n      className={cn(\"flex cursor-default items-center justify-center py-1\", className)}\r\n      {...props}>\r\n      <ChevronDownIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollDownButton>)\r\n  );\r\n}\r\n\r\nexport {\r\n  Select,\r\n  SelectContent,\r\n  SelectGroup,\r\n  SelectItem,\r\n  SelectLabel,\r\n  SelectScrollDownButton,\r\n  SelectScrollUpButton,\r\n  SelectSeparator,\r\n  SelectTrigger,\r\n  SelectValue,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OACJ;IACC,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACJ;IACC,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACJ;IACC,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OACJ;IACC,qBACG,6LAAC,qKAAA,CAAA,UAAuB;QACvB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YACR;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MArBS;AAuBT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACJ;IACC,qBACG,6LAAC,qKAAA,CAAA,SAAsB;kBACtB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BACT,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,OAAO,aAAa,YAChC;8BACD;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MA5BS;AA8BT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACJ;IACC,qBACG,6LAAC,qKAAA,CAAA,QAAqB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAEf;MAVS;AAYT,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OACJ;IACC,qBACG,6LAAC,qKAAA,CAAA,OAAoB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BACT,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MArBS;AAuBT,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACJ;IACC,qBACG,6LAAC,qKAAA,CAAA,YAAyB;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAEf;MAVS;AAYT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACJ;IACC,qBACG,6LAAC,qKAAA,CAAA,iBAA8B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,wDAAwD;QACrE,GAAG,KAAK;kBACT,cAAA,6LAAC,uNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAZS;AAcT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OACJ;IACC,qBACG,6LAAC,qKAAA,CAAA,mBAAgC;QAChC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,wDAAwD;QACrE,GAAG,KAAK;kBACT,cAAA,6LAAC,2NAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAZS", "debugId": null}}, {"offset": {"line": 696, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/js/web3/packages/chario/src/lib/hashing.js"], "sourcesContent": ["// Function to convert a Uint8Array to a Base64 URL encoded string\r\nfunction base64URLEncode(buffer) {\r\n    const base64 = btoa(String.fromCharCode.apply(null, buffer));\r\n    return base64.replace(/\\+/g, '-').replace(/\\//g, '_').replace(/=+$/, '');\r\n}\r\n\r\n// Function to convert a Base64 URL encoded string back to a Uint8Array\r\nfunction base64URLDecode(base64URL) {\r\n    let base64 = base64URL.replace(/-/g, '+').replace(/_/g, '/');\r\n    while (base64.length % 4) {\r\n        base64 += '=';\r\n    }\r\n    const binaryString = atob(base64);\r\n    return Uint8Array.from(binaryString, c => c.charCodeAt(0));\r\n}\r\n\r\n/**\r\n * Encodes an integer into a compact Base64 URL safe string.\r\n * This function handles integers up to 2^53 - 1 safely in JavaScript (Number.MAX_SAFE_INTEGER).\r\n * For larger integers, consider using BigInt and a more robust byte conversion.\r\n * @param {number} integer The integer to encode.\r\n * @returns {string} The Base64 URL encoded string.\r\n */\r\nexport function maskId(integer) {\r\n    if (!Number.isInteger(integer) || integer < 0) {\r\n        throw new Error(\"Input must be a non-negative integer.\");\r\n    }\r\n\r\n    const byteLength = Math.ceil(Math.log2(integer + 1) / 8) || 1; // Calculate minimum bytes needed, at least 1 for 0\r\n    const buffer = new Uint8Array(byteLength);\r\n\r\n    for (let i = 0; i < byteLength; i++) {\r\n        buffer[byteLength - 1 - i] = (integer >> (8 * i)) & 0xFF;\r\n    }\r\n\r\n    return base64URLEncode(buffer);\r\n}\r\n\r\n/**\r\n * Decodes a Base64 URL safe string back into an integer.\r\n * @param {string} encodedString The Base64 URL encoded string.\r\n * @returns {number} The decoded integer.\r\n */\r\nexport function unmaskId(encodedString) {\r\n    const buffer = base64URLDecode(encodedString);\r\n    let integer = 0;\r\n\r\n    for (let i = 0; i < buffer.length; i++) {\r\n        integer = (integer << 8) | buffer[i];\r\n    }\r\n\r\n    return integer;\r\n}"], "names": [], "mappings": "AAAA,kEAAkE;;;;;AAClE,SAAS,gBAAgB,MAAM;IAC3B,MAAM,SAAS,KAAK,OAAO,YAAY,CAAC,KAAK,CAAC,MAAM;IACpD,OAAO,OAAO,OAAO,CAAC,OAAO,KAAK,OAAO,CAAC,OAAO,KAAK,OAAO,CAAC,OAAO;AACzE;AAEA,uEAAuE;AACvE,SAAS,gBAAgB,SAAS;IAC9B,IAAI,SAAS,UAAU,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM;IACxD,MAAO,OAAO,MAAM,GAAG,EAAG;QACtB,UAAU;IACd;IACA,MAAM,eAAe,KAAK;IAC1B,OAAO,WAAW,IAAI,CAAC,cAAc,CAAA,IAAK,EAAE,UAAU,CAAC;AAC3D;AASO,SAAS,OAAO,OAAO;IAC1B,IAAI,CAAC,OAAO,SAAS,CAAC,YAAY,UAAU,GAAG;QAC3C,MAAM,IAAI,MAAM;IACpB;IAEA,MAAM,aAAa,KAAK,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,KAAK,MAAM,GAAG,mDAAmD;IAClH,MAAM,SAAS,IAAI,WAAW;IAE9B,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,IAAK;QACjC,MAAM,CAAC,aAAa,IAAI,EAAE,GAAG,AAAC,WAAY,IAAI,IAAM;IACxD;IAEA,OAAO,gBAAgB;AAC3B;AAOO,SAAS,SAAS,aAAa;IAClC,MAAM,SAAS,gBAAgB;IAC/B,IAAI,UAAU;IAEd,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;QACpC,UAAU,AAAC,WAAW,IAAK,MAAM,CAAC,EAAE;IACxC;IAEA,OAAO;AACX", "debugId": null}}, {"offset": {"line": 742, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/js/web3/packages/chario/src/components/md/donation-table.jsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport {\r\n    CaretSortIcon,\r\n    ChevronDownIcon,\r\n    DotsHorizontalIcon,\r\n} from '@radix-ui/react-icons';\r\nimport {\r\n    ColumnDef,\r\n    ColumnFiltersState,\r\n    SortingState,\r\n    VisibilityState,\r\n    flexRender,\r\n    getCoreRowModel,\r\n    getFilteredRowModel,\r\n    getPaginationRowModel,\r\n    getSortedRowModel,\r\n    useReactTable,\r\n} from '@tanstack/react-table';\r\n\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n    DropdownMenu,\r\n    DropdownMenuCheckboxItem,\r\n    DropdownMenuContent,\r\n    DropdownMenuItem,\r\n    DropdownMenuLabel,\r\n    DropdownMenuSeparator,\r\n    DropdownMenuTrigger,\r\n} from '@/components/ui/dropdown-menu';\r\nimport { Input } from '@/components/ui/input';\r\nimport {\r\n    Table,\r\n    TableBody,\r\n    TableCell,\r\n    TableHead,\r\n    TableHeader,\r\n    TableRow,\r\n} from '@/components/ui/table';\r\nimport {\r\n    Select,\r\n    SelectContent,\r\n    SelectItem,\r\n    SelectTrigger,\r\n    SelectValue,\r\n} from '@/components/ui/select';\r\nimport Link from 'next/link';\r\nimport { maskId } from '@/lib/hashing';\r\nimport { toast } from 'sonner';\r\nimport { useTranslations } from '@/contexts/LanguageContext';\r\n\r\n// Utility function to get date ranges (same as used by DonationCharts)\r\nfunction getDateRangeForTable(timeRange) {\r\n    const now = new Date();\r\n    let startDate = new Date(now);\r\n    let endDate = new Date(now);\r\n\r\n    startDate.setHours(0, 0, 0, 0); // Start of current day\r\n    endDate.setHours(23, 59, 59, 999); // End of current day\r\n\r\n    switch (timeRange) {\r\n        case 'yesterday':\r\n            startDate.setDate(startDate.getDate() - 1);\r\n            endDate.setDate(endDate.getDate() - 1);\r\n            break;\r\n        case 'thisWeek':\r\n            const dayOfWeek = (startDate.getDay() + 6) % 7; // Monday = 0, Sunday = 6\r\n            startDate.setDate(startDate.getDate() - dayOfWeek);\r\n            startDate.setHours(0, 0, 0, 0);\r\n            endDate = new Date(startDate);\r\n            endDate.setDate(startDate.getDate() + 6);\r\n            endDate.setHours(23, 59, 59, 999);\r\n            break;\r\n        case 'thisMonth':\r\n            startDate.setDate(1);\r\n            startDate.setHours(0, 0, 0, 0);\r\n            endDate = new Date(startDate);\r\n            endDate.setMonth(startDate.getMonth() + 1);\r\n            endDate.setDate(0); // Last day of the previous month (which is the current month)\r\n            endDate.setHours(23, 59, 59, 999);\r\n            break;\r\n        case 'thisYear':\r\n            startDate.setMonth(0, 1); // January 1st\r\n            startDate.setHours(0, 0, 0, 0);\r\n            endDate.setMonth(11, 31); // December 31st\r\n            endDate.setHours(23, 59, 59, 999);\r\n            break;\r\n        case 'allTime':\r\n            startDate = new Date(0); // Epoch, effectively beginning of time\r\n            endDate = new Date();\r\n            endDate.setFullYear(endDate.getFullYear() + 100); // Far future\r\n            break;\r\n        // 'today' is handled by default initialization\r\n    }\r\n    return { startDate, endDate };\r\n}\r\n\r\nfunction useClipboard() {\r\n    const [copied, setCopied] = React.useState(false);\r\n\r\n    const copyToClipboard = async (text) => {\r\n        try {\r\n            await navigator.clipboard.writeText(text);\r\n            toast.success('Copied to clipboard');\r\n            setCopied(true);\r\n            setTimeout(() => {\r\n                setCopied(false);\r\n            }, 1000);\r\n        } catch (err) {\r\n            console.error('Failed to copy text: ', err);\r\n        }\r\n    };\r\n\r\n    return { copied, copyToClipboard };\r\n}\r\n\r\n// Define your table columns\r\n// The crucial change is accessorKey: 'charity.title'\r\nexport const getColumns = (t) => [\r\n    {\r\n        accessorKey: 'charity.title', // Accesses the title property within the nested charity object\r\n        header: ({ column }) => (\r\n            <Button\r\n                variant=\"ghost\"\r\n                className='cursor-pointer'\r\n                onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}\r\n            >\r\n                {t('charity.title')}\r\n                <CaretSortIcon className=\"ml-2 h-4 w-4\" />\r\n            </Button>\r\n        ),\r\n        // Render the charity title, with a fallback if it's missing\r\n        cell: ({ row }) => <div className=\"capitalize\">{row.original.charity?.title || 'N/A'}</div>,\r\n        filterFn: 'includesString', // Essential for string-based searching on this column\r\n    },\r\n    {\r\n        accessorKey: 'amountEth',\r\n        header: ({ column }) => (\r\n            <Button\r\n                variant=\"ghost\"\r\n                className='cursor-pointer'\r\n                onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}\r\n            >\r\n                {t('charity.amount')} (ETH)\r\n                <CaretSortIcon className=\"ml-2 h-4 w-4\" />\r\n            </Button>\r\n        ),\r\n        cell: ({ row }) => {\r\n            const amount = parseFloat(row.getValue('amountEth'));\r\n            const formatted = new Intl.NumberFormat('en-US', {\r\n                minimumFractionDigits: 2,\r\n                maximumFractionDigits: 6,\r\n            }).format(amount);\r\n            return <div className=\"font-medium\">{formatted}</div>;\r\n        },\r\n    },\r\n    {\r\n        accessorKey: 'createdAt',\r\n        header: ({ column }) => (\r\n            <Button\r\n                variant=\"ghost\"\r\n                className='cursor-pointer'\r\n                onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}\r\n            >\r\n                {t('charity.donationDate')}\r\n                <CaretSortIcon className=\"ml-2 h-4 w-4\" />\r\n            </Button>\r\n        ),\r\n        cell: ({ row }) => {\r\n            const date = new Date(row.getValue('createdAt'));\r\n            // Format date for display\r\n            return <div className=\"text-left\">{date.toLocaleDateString()} {date.toLocaleTimeString()}</div>;\r\n        },\r\n    },\r\n    {\r\n        id: 'actions',\r\n        enableHiding: false, // Prevents this column from being hidden by the user\r\n        cell: ({ row }) => {\r\n            const donation = row.original;\r\n            const { copyToClipboard, copied } = useClipboard();\r\n\r\n            return (\r\n                <DropdownMenu>\r\n                    <DropdownMenuTrigger asChild>\r\n                        <Button variant=\"ghost\" className=\"h-8 w-8 p-0 cursor-pointer\">\r\n                            <span className=\"sr-only\">Open menu</span>\r\n                            <DotsHorizontalIcon className=\"h-4 w-4\" />\r\n                        </Button>\r\n                    </DropdownMenuTrigger>\r\n                    <DropdownMenuContent align=\"end\">\r\n                        {/* <DropdownMenuLabel>Actions</DropdownMenuLabel> */}\r\n                        <DropdownMenuItem\r\n                            className='cursor-pointer'\r\n                            onClick={() => copyToClipboard(donation?.txHash)}\r\n                        >\r\n                            Copy Transaction Hash\r\n                        </DropdownMenuItem>\r\n                        {/* Add more actions, e.g., navigate to charity page */}\r\n                        <Link\r\n                            target='_blank'\r\n                            href={`https://sepolia.etherscan.io/tx/${donation?.txHash}#internal`}\r\n                            className='cursor-pointer'\r\n                        >\r\n                            <DropdownMenuItem\r\n                                className='cursor-pointer'\r\n                            >\r\n                                View Charity\r\n                            </DropdownMenuItem>\r\n                        </Link>\r\n                    </DropdownMenuContent>\r\n                </DropdownMenu>\r\n            );\r\n        },\r\n    },\r\n];\r\n\r\nexport function DonationsTable({ donations: rawDonations }) {\r\n    const t = useTranslations();\r\n    const [donations, setDonations] = React.useState([]);\r\n    const [sorting, setSorting] = React.useState([]);\r\n    const [columnFilters, setColumnFilters] = React.useState([]);\r\n    const [columnVisibility, setColumnVisibility] = React.useState({});\r\n    const [rowSelection, setRowSelection] = React.useState({});\r\n    const [timeFilter, setTimeFilter] = React.useState('allTime'); // State for date range filtering\r\n\r\n    React.useEffect(() => {\r\n        if (rawDonations) {\r\n            // Parse raw JSON string and ensure createdAt is a Date object\r\n            const parsedDonations = rawDonations.map(d => ({\r\n                ...d,\r\n                createdAt: new Date(d.createdAt)\r\n            }));\r\n            setDonations(parsedDonations);\r\n        }\r\n    }, [rawDonations]);\r\n\r\n    // Memoized filtered data based on the selected time range\r\n    const filteredDonations = React.useMemo(() => {\r\n        if (!donations || donations.length === 0) return [];\r\n\r\n        const { startDate, endDate } = getDateRangeForTable(timeFilter);\r\n\r\n        if (timeFilter === 'allTime') {\r\n            return donations;\r\n        }\r\n\r\n        return donations.filter(donation => {\r\n            const createdAt = new Date(donation.createdAt);\r\n            return createdAt >= startDate && createdAt <= endDate;\r\n        });\r\n    }, [donations, timeFilter]);\r\n\r\n    // Initialize the react-table instance\r\n    const table = useReactTable({\r\n        data: filteredDonations, // Use the time-filtered data as the source\r\n        columns: getColumns(t),\r\n        onSortingChange: setSorting,\r\n        onColumnFiltersChange: setColumnFilters,\r\n        getCoreRowModel: getCoreRowModel(),\r\n        getPaginationRowModel: getPaginationRowModel(),\r\n        getSortedRowModel: getSortedRowModel(),\r\n        getFilteredRowModel: getFilteredRowModel(),\r\n        onColumnVisibilityChange: setColumnVisibility,\r\n        onRowSelectionChange: setRowSelection,\r\n        state: {\r\n            sorting,\r\n            columnFilters,\r\n            columnVisibility,\r\n            rowSelection,\r\n        },\r\n    });\r\n\r\n    return (\r\n        <div className=\"w-full\">\r\n            <div className=\"flex items-center py-4 gap-2\">\r\n                {/* <Input\r\n                    placeholder=\"Search by charity name...\"\r\n                    // Corrected: Ensure the value is always a string for the controlled input\r\n                    value={(table.getColumn('charity.title')?.getFilterValue()) ?? ''}\r\n                    onChange={(event) =>\r\n                        table.getColumn('charity.title')?.setFilterValue(event.target.value)\r\n                    }\r\n                    className=\"max-w-sm\"\r\n                /> */}\r\n                <Select value={timeFilter} onValueChange={setTimeFilter}>\r\n                    <SelectTrigger className=\"w-[180px]\">\r\n                        <SelectValue placeholder=\"Filter by date\" />\r\n                    </SelectTrigger>\r\n                    <SelectContent>\r\n                        <SelectItem value=\"allTime\">All Time</SelectItem>\r\n                        <SelectItem value=\"today\">Today</SelectItem>\r\n                        <SelectItem value=\"yesterday\">Yesterday</SelectItem>\r\n                        <SelectItem value=\"thisWeek\">This Week</SelectItem>\r\n                        <SelectItem value=\"thisMonth\">This Month</SelectItem>\r\n                        <SelectItem value=\"thisYear\">This Year</SelectItem>\r\n                    </SelectContent>\r\n                </Select>\r\n                <DropdownMenu>\r\n                    <DropdownMenuTrigger asChild>\r\n                        <Button variant=\"outline\" className=\"ml-auto\">\r\n                            Columns <ChevronDownIcon className=\"ml-2 h-4 w-4\" />\r\n                        </Button>\r\n                    </DropdownMenuTrigger>\r\n                    <DropdownMenuContent align=\"end\">\r\n                        {table\r\n                            .getAllColumns()\r\n                            .filter((column) => column.getCanHide())\r\n                            .map((column) => {\r\n                                return (\r\n                                    <DropdownMenuCheckboxItem\r\n                                        key={column.id}\r\n                                        className=\"capitalize\"\r\n                                        checked={column.getIsVisible()}\r\n                                        onCheckedChange={(value) =>\r\n                                            column.toggleVisibility(!!value)\r\n                                        }\r\n                                    >\r\n                                        {/* Display a more user-friendly name for the charity column */}\r\n                                        {column.id === 'charity.title' ? 'Charity Name' : column.id}\r\n                                    </DropdownMenuCheckboxItem>\r\n                                );\r\n                            })}\r\n                    </DropdownMenuContent>\r\n                </DropdownMenu>\r\n            </div>\r\n            <div className=\"rounded-md border\">\r\n                <Table>\r\n                    <TableHeader>\r\n                        {table.getHeaderGroups().map((headerGroup) => (\r\n                            <TableRow key={headerGroup.id}>\r\n                                {headerGroup.headers.map((header) => {\r\n                                    return (\r\n                                        <TableHead key={header.id}>\r\n                                            {header.isPlaceholder\r\n                                                ? null\r\n                                                : flexRender(\r\n                                                    header.column.columnDef.header,\r\n                                                    header.getContext()\r\n                                                )}\r\n                                        </TableHead>\r\n                                    );\r\n                                })}\r\n                            </TableRow>\r\n                        ))}\r\n                    </TableHeader>\r\n                    <TableBody>\r\n                        {table.getRowModel().rows?.length ? (\r\n                            table.getRowModel().rows.map((row) => (\r\n                                <TableRow\r\n                                    key={row.id}\r\n                                    data-state={row.getIsSelected() && 'selected'}\r\n                                >\r\n                                    {row.getVisibleCells().map((cell) => (\r\n                                        <TableCell key={cell.id}>\r\n                                            {flexRender(cell.column.columnDef.cell, cell.getContext())}\r\n                                        </TableCell>\r\n                                    ))}\r\n                                </TableRow>\r\n                            ))\r\n                        ) : (\r\n                            <TableRow>\r\n                                <TableCell colSpan={columns.length} className=\"h-24 text-center\">\r\n                                    No results.\r\n                                </TableCell>\r\n                            </TableRow>\r\n                        )}\r\n                    </TableBody>\r\n                </Table>\r\n            </div>\r\n            <div className=\"flex items-center justify-end space-x-2 py-4\">\r\n                <div className=\"flex-1 text-sm text-muted-foreground\">\r\n                    {table.getFilteredRowModel().rows.length} total donations.\r\n                </div>\r\n                <div className=\"space-x-2\">\r\n                    <Button\r\n                        variant=\"outline\"\r\n                        size=\"sm\"\r\n                        onClick={() => table.previousPage()}\r\n                        disabled={!table.getCanPreviousPage()}\r\n                    >\r\n                        Previous\r\n                    </Button>\r\n                    <Button\r\n                        variant=\"outline\"\r\n                        size=\"sm\"\r\n                        onClick={() => table.nextPage()}\r\n                        disabled={!table.getCanNextPage()}\r\n                    >\r\n                        Next\r\n                    </Button>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    );\r\n}"], "names": [], "mappings": ";;;;;AAEA;AACA;AAKA;AAAA;AAaA;AACA;AASA;AACA;AAQA;AAOA;AACA;AACA;AACA;;;AAlDA;;;;;;;;;;;;;AAoDA,uEAAuE;AACvE,SAAS,qBAAqB,SAAS;IACnC,MAAM,MAAM,IAAI;IAChB,IAAI,YAAY,IAAI,KAAK;IACzB,IAAI,UAAU,IAAI,KAAK;IAEvB,UAAU,QAAQ,CAAC,GAAG,GAAG,GAAG,IAAI,uBAAuB;IACvD,QAAQ,QAAQ,CAAC,IAAI,IAAI,IAAI,MAAM,qBAAqB;IAExD,OAAQ;QACJ,KAAK;YACD,UAAU,OAAO,CAAC,UAAU,OAAO,KAAK;YACxC,QAAQ,OAAO,CAAC,QAAQ,OAAO,KAAK;YACpC;QACJ,KAAK;YACD,MAAM,YAAY,CAAC,UAAU,MAAM,KAAK,CAAC,IAAI,GAAG,yBAAyB;YACzE,UAAU,OAAO,CAAC,UAAU,OAAO,KAAK;YACxC,UAAU,QAAQ,CAAC,GAAG,GAAG,GAAG;YAC5B,UAAU,IAAI,KAAK;YACnB,QAAQ,OAAO,CAAC,UAAU,OAAO,KAAK;YACtC,QAAQ,QAAQ,CAAC,IAAI,IAAI,IAAI;YAC7B;QACJ,KAAK;YACD,UAAU,OAAO,CAAC;YAClB,UAAU,QAAQ,CAAC,GAAG,GAAG,GAAG;YAC5B,UAAU,IAAI,KAAK;YACnB,QAAQ,QAAQ,CAAC,UAAU,QAAQ,KAAK;YACxC,QAAQ,OAAO,CAAC,IAAI,8DAA8D;YAClF,QAAQ,QAAQ,CAAC,IAAI,IAAI,IAAI;YAC7B;QACJ,KAAK;YACD,UAAU,QAAQ,CAAC,GAAG,IAAI,cAAc;YACxC,UAAU,QAAQ,CAAC,GAAG,GAAG,GAAG;YAC5B,QAAQ,QAAQ,CAAC,IAAI,KAAK,gBAAgB;YAC1C,QAAQ,QAAQ,CAAC,IAAI,IAAI,IAAI;YAC7B;QACJ,KAAK;YACD,YAAY,IAAI,KAAK,IAAI,uCAAuC;YAChE,UAAU,IAAI;YACd,QAAQ,WAAW,CAAC,QAAQ,WAAW,KAAK,MAAM,aAAa;YAC/D;IAER;IACA,OAAO;QAAE;QAAW;IAAQ;AAChC;AAEA,SAAS;;IACL,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IAE3C,MAAM,kBAAkB,OAAO;QAC3B,IAAI;YACA,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,UAAU;YACV,WAAW;gBACP,UAAU;YACd,GAAG;QACP,EAAE,OAAO,KAAK;YACV,QAAQ,KAAK,CAAC,yBAAyB;QAC3C;IACJ;IAEA,OAAO;QAAE;QAAQ;IAAgB;AACrC;GAjBS;AAqBF,MAAM,aAAa,CAAC,IAAM;QAC7B;YACI,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE,iBACf,6LAAC,qIAAA,CAAA,SAAM;oBACH,SAAQ;oBACR,WAAU;oBACV,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;;wBAE5D,EAAE;sCACH,6LAAC,mLAAA,CAAA,gBAAa;4BAAC,WAAU;;;;;;;;;;;;YAGjC,4DAA4D;YAC5D,MAAM,CAAC,EAAE,GAAG,EAAE,iBAAK,6LAAC;oBAAI,WAAU;8BAAc,IAAI,QAAQ,CAAC,OAAO,EAAE,SAAS;;;;;;YAC/E,UAAU;QACd;QACA;YACI,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE,iBACf,6LAAC,qIAAA,CAAA,SAAM;oBACH,SAAQ;oBACR,WAAU;oBACV,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;;wBAE5D,EAAE;wBAAkB;sCACrB,6LAAC,mLAAA,CAAA,gBAAa;4BAAC,WAAU;;;;;;;;;;;;YAGjC,MAAM,CAAC,EAAE,GAAG,EAAE;gBACV,MAAM,SAAS,WAAW,IAAI,QAAQ,CAAC;gBACvC,MAAM,YAAY,IAAI,KAAK,YAAY,CAAC,SAAS;oBAC7C,uBAAuB;oBACvB,uBAAuB;gBAC3B,GAAG,MAAM,CAAC;gBACV,qBAAO,6LAAC;oBAAI,WAAU;8BAAe;;;;;;YACzC;QACJ;QACA;YACI,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE,iBACf,6LAAC,qIAAA,CAAA,SAAM;oBACH,SAAQ;oBACR,WAAU;oBACV,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;;wBAE5D,EAAE;sCACH,6LAAC,mLAAA,CAAA,gBAAa;4BAAC,WAAU;;;;;;;;;;;;YAGjC,MAAM,CAAC,EAAE,GAAG,EAAE;gBACV,MAAM,OAAO,IAAI,KAAK,IAAI,QAAQ,CAAC;gBACnC,0BAA0B;gBAC1B,qBAAO,6LAAC;oBAAI,WAAU;;wBAAa,KAAK,kBAAkB;wBAAG;wBAAE,KAAK,kBAAkB;;;;;;;YAC1F;QACJ;QACA;YACI,IAAI;YACJ,cAAc;YACd,IAAI,MAAE,CAAC,EAAE,GAAG,EAAE;;gBACV,MAAM,WAAW,IAAI,QAAQ;gBAC7B,MAAM,EAAE,eAAe,EAAE,MAAM,EAAE,GAAG;gBAEpC,qBACI,6LAAC,+IAAA,CAAA,eAAY;;sCACT,6LAAC,+IAAA,CAAA,sBAAmB;4BAAC,OAAO;sCACxB,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,WAAU;;kDAC9B,6LAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,6LAAC,mLAAA,CAAA,qBAAkB;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAGtC,6LAAC,+IAAA,CAAA,sBAAmB;4BAAC,OAAM;;8CAEvB,6LAAC,+IAAA,CAAA,mBAAgB;oCACb,WAAU;oCACV,SAAS,IAAM,gBAAgB,UAAU;8CAC5C;;;;;;8CAID,6LAAC,+JAAA,CAAA,UAAI;oCACD,QAAO;oCACP,MAAM,CAAC,gCAAgC,EAAE,UAAU,OAAO,SAAS,CAAC;oCACpE,WAAU;8CAEV,cAAA,6LAAC,+IAAA,CAAA,mBAAgB;wCACb,WAAU;kDACb;;;;;;;;;;;;;;;;;;;;;;;YAOrB;;oBAjCwC;;;QAkC5C;KACH;AAEM,SAAS,eAAe,EAAE,WAAW,YAAY,EAAE;;IACtD,MAAM,IAAI,CAAA,GAAA,sIAAA,CAAA,kBAAe,AAAD;IACxB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE,EAAE;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE,EAAE;IAC3D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE,CAAC;IAChE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE,CAAC;IACxD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE,YAAY,iCAAiC;IAEhG,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;oCAAE;YACZ,IAAI,cAAc;gBACd,8DAA8D;gBAC9D,MAAM,kBAAkB,aAAa,GAAG;gEAAC,CAAA,IAAK,CAAC;4BAC3C,GAAG,CAAC;4BACJ,WAAW,IAAI,KAAK,EAAE,SAAS;wBACnC,CAAC;;gBACD,aAAa;YACjB;QACJ;mCAAG;QAAC;KAAa;IAEjB,0DAA0D;IAC1D,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;qDAAE;YACpC,IAAI,CAAC,aAAa,UAAU,MAAM,KAAK,GAAG,OAAO,EAAE;YAEnD,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,qBAAqB;YAEpD,IAAI,eAAe,WAAW;gBAC1B,OAAO;YACX;YAEA,OAAO,UAAU,MAAM;6DAAC,CAAA;oBACpB,MAAM,YAAY,IAAI,KAAK,SAAS,SAAS;oBAC7C,OAAO,aAAa,aAAa,aAAa;gBAClD;;QACJ;oDAAG;QAAC;QAAW;KAAW;IAE1B,sCAAsC;IACtC,MAAM,QAAQ,CAAA,GAAA,yLAAA,CAAA,gBAAa,AAAD,EAAE;QACxB,MAAM;QACN,SAAS,WAAW;QACpB,iBAAiB;QACjB,uBAAuB;QACvB,iBAAiB,CAAA,GAAA,wKAAA,CAAA,kBAAe,AAAD;QAC/B,uBAAuB,CAAA,GAAA,wKAAA,CAAA,wBAAqB,AAAD;QAC3C,mBAAmB,CAAA,GAAA,wKAAA,CAAA,oBAAiB,AAAD;QACnC,qBAAqB,CAAA,GAAA,wKAAA,CAAA,sBAAmB,AAAD;QACvC,0BAA0B;QAC1B,sBAAsB;QACtB,OAAO;YACH;YACA;YACA;YACA;QACJ;IACJ;IAEA,qBACI,6LAAC;QAAI,WAAU;;0BACX,6LAAC;gBAAI,WAAU;;kCAUX,6LAAC,qIAAA,CAAA,SAAM;wBAAC,OAAO;wBAAY,eAAe;;0CACtC,6LAAC,qIAAA,CAAA,gBAAa;gCAAC,WAAU;0CACrB,cAAA,6LAAC,qIAAA,CAAA,cAAW;oCAAC,aAAY;;;;;;;;;;;0CAE7B,6LAAC,qIAAA,CAAA,gBAAa;;kDACV,6LAAC,qIAAA,CAAA,aAAU;wCAAC,OAAM;kDAAU;;;;;;kDAC5B,6LAAC,qIAAA,CAAA,aAAU;wCAAC,OAAM;kDAAQ;;;;;;kDAC1B,6LAAC,qIAAA,CAAA,aAAU;wCAAC,OAAM;kDAAY;;;;;;kDAC9B,6LAAC,qIAAA,CAAA,aAAU;wCAAC,OAAM;kDAAW;;;;;;kDAC7B,6LAAC,qIAAA,CAAA,aAAU;wCAAC,OAAM;kDAAY;;;;;;kDAC9B,6LAAC,qIAAA,CAAA,aAAU;wCAAC,OAAM;kDAAW;;;;;;;;;;;;;;;;;;kCAGrC,6LAAC,+IAAA,CAAA,eAAY;;0CACT,6LAAC,+IAAA,CAAA,sBAAmB;gCAAC,OAAO;0CACxB,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,WAAU;;wCAAU;sDAClC,6LAAC,mLAAA,CAAA,kBAAe;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAG3C,6LAAC,+IAAA,CAAA,sBAAmB;gCAAC,OAAM;0CACtB,MACI,aAAa,GACb,MAAM,CAAC,CAAC,SAAW,OAAO,UAAU,IACpC,GAAG,CAAC,CAAC;oCACF,qBACI,6LAAC,+IAAA,CAAA,2BAAwB;wCAErB,WAAU;wCACV,SAAS,OAAO,YAAY;wCAC5B,iBAAiB,CAAC,QACd,OAAO,gBAAgB,CAAC,CAAC,CAAC;kDAI7B,OAAO,EAAE,KAAK,kBAAkB,iBAAiB,OAAO,EAAE;uCARtD,OAAO,EAAE;;;;;gCAW1B;;;;;;;;;;;;;;;;;;0BAIhB,6LAAC;gBAAI,WAAU;0BACX,cAAA,6LAAC,oIAAA,CAAA,QAAK;;sCACF,6LAAC,oIAAA,CAAA,cAAW;sCACP,MAAM,eAAe,GAAG,GAAG,CAAC,CAAC,4BAC1B,6LAAC,oIAAA,CAAA,WAAQ;8CACJ,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC;wCACtB,qBACI,6LAAC,oIAAA,CAAA,YAAS;sDACL,OAAO,aAAa,GACf,OACA,CAAA,GAAA,yLAAA,CAAA,aAAU,AAAD,EACP,OAAO,MAAM,CAAC,SAAS,CAAC,MAAM,EAC9B,OAAO,UAAU;2CALb,OAAO,EAAE;;;;;oCASjC;mCAZW,YAAY,EAAE;;;;;;;;;;sCAgBrC,6LAAC,oIAAA,CAAA,YAAS;sCACL,MAAM,WAAW,GAAG,IAAI,EAAE,SACvB,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,oBAC1B,6LAAC,oIAAA,CAAA,WAAQ;oCAEL,cAAY,IAAI,aAAa,MAAM;8CAElC,IAAI,eAAe,GAAG,GAAG,CAAC,CAAC,qBACxB,6LAAC,oIAAA,CAAA,YAAS;sDACL,CAAA,GAAA,yLAAA,CAAA,aAAU,AAAD,EAAE,KAAK,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,UAAU;2CAD3C,KAAK,EAAE;;;;;mCAJtB,IAAI,EAAE;;;;0DAWnB,6LAAC,oIAAA,CAAA,WAAQ;0CACL,cAAA,6LAAC,oIAAA,CAAA,YAAS;oCAAC,SAAS,QAAQ,MAAM;oCAAE,WAAU;8CAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQrF,6LAAC;gBAAI,WAAU;;kCACX,6LAAC;wBAAI,WAAU;;4BACV,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM;4BAAC;;;;;;;kCAE7C,6LAAC;wBAAI,WAAU;;0CACX,6LAAC,qIAAA,CAAA,SAAM;gCACH,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,MAAM,YAAY;gCACjC,UAAU,CAAC,MAAM,kBAAkB;0CACtC;;;;;;0CAGD,6LAAC,qIAAA,CAAA,SAAM;gCACH,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,MAAM,QAAQ;gCAC7B,UAAU,CAAC,MAAM,cAAc;0CAClC;;;;;;;;;;;;;;;;;;;;;;;;AAOrB;IAlLgB;;QACF,sIAAA,CAAA,kBAAe;QAoCX,yLAAA,CAAA,gBAAa;;;KArCf", "debugId": null}}, {"offset": {"line": 1392, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/js/web3/packages/chario/src/components/ui/chart.jsx"], "sourcesContent": ["\"use client\";\r\nimport * as React from \"react\"\r\nimport * as RechartsPrimitive from \"recharts\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\n// Format: { THEME_NAME: CSS_SELECTOR }\r\nconst THEMES = {\r\n  light: \"\",\r\n  dark: \".dark\"\r\n}\r\n\r\nconst ChartContext = React.createContext(null)\r\n\r\nfunction useChart() {\r\n  const context = React.useContext(ChartContext)\r\n\r\n  if (!context) {\r\n    throw new Error(\"useChart must be used within a <ChartContainer />\")\r\n  }\r\n\r\n  return context\r\n}\r\n\r\nfunction ChartContainer({\r\n  id,\r\n  className,\r\n  children,\r\n  config,\r\n  ...props\r\n}) {\r\n  const uniqueId = React.useId()\r\n  const chartId = `chart-${id || uniqueId.replace(/:/g, \"\")}`\r\n\r\n  return (\r\n    (<ChartContext.Provider value={{ config }}>\r\n      <div\r\n        data-slot=\"chart\"\r\n        data-chart={chartId}\r\n        className={cn(\r\n          \"[&_.recharts-cartesian-axis-tick_text]:fill-muted-foreground [&_.recharts-cartesian-grid_line[stroke='#ccc']]:stroke-border/50 [&_.recharts-curve.recharts-tooltip-cursor]:stroke-border [&_.recharts-polar-grid_[stroke='#ccc']]:stroke-border [&_.recharts-radial-bar-background-sector]:fill-muted [&_.recharts-rectangle.recharts-tooltip-cursor]:fill-muted [&_.recharts-reference-line_[stroke='#ccc']]:stroke-border flex aspect-video justify-center text-xs [&_.recharts-dot[stroke='#fff']]:stroke-transparent [&_.recharts-layer]:outline-hidden [&_.recharts-sector]:outline-hidden [&_.recharts-sector[stroke='#fff']]:stroke-transparent [&_.recharts-surface]:outline-hidden\",\r\n          className\r\n        )}\r\n        {...props}>\r\n        <ChartStyle id={chartId} config={config} />\r\n        <RechartsPrimitive.ResponsiveContainer>\r\n          {children}\r\n        </RechartsPrimitive.ResponsiveContainer>\r\n      </div>\r\n    </ChartContext.Provider>)\r\n  );\r\n}\r\n\r\nconst ChartStyle = ({\r\n  id,\r\n  config\r\n}) => {\r\n  const colorConfig = Object.entries(config).filter(([, config]) => config.theme || config.color)\r\n\r\n  if (!colorConfig.length) {\r\n    return null\r\n  }\r\n\r\n  return (\r\n    (<style\r\n      dangerouslySetInnerHTML={{\r\n        __html: Object.entries(THEMES)\r\n          .map(([theme, prefix]) => `\r\n${prefix} [data-chart=${id}] {\r\n${colorConfig\r\n.map(([key, itemConfig]) => {\r\nconst color =\r\n  itemConfig.theme?.[theme] ||\r\n  itemConfig.color\r\nreturn color ? `  --color-${key}: ${color};` : null\r\n})\r\n.join(\"\\n\")}\r\n}\r\n`)\r\n          .join(\"\\n\"),\r\n      }} />)\r\n  );\r\n}\r\n\r\nconst ChartTooltip = RechartsPrimitive.Tooltip\r\n\r\nfunction ChartTooltipContent({\r\n  active,\r\n  payload,\r\n  className,\r\n  indicator = \"dot\",\r\n  hideLabel = false,\r\n  hideIndicator = false,\r\n  label,\r\n  labelFormatter,\r\n  labelClassName,\r\n  formatter,\r\n  color,\r\n  nameKey,\r\n  labelKey\r\n}) {\r\n  const { config } = useChart()\r\n\r\n  const tooltipLabel = React.useMemo(() => {\r\n    if (hideLabel || !payload?.length) {\r\n      return null\r\n    }\r\n\r\n    const [item] = payload\r\n    const key = `${labelKey || item?.dataKey || item?.name || \"value\"}`\r\n    const itemConfig = getPayloadConfigFromPayload(config, item, key)\r\n    const value =\r\n      !labelKey && typeof label === \"string\"\r\n        ? config[label]?.label || label\r\n        : itemConfig?.label\r\n\r\n    if (labelFormatter) {\r\n      return (\r\n        (<div className={cn(\"font-medium\", labelClassName)}>\r\n          {labelFormatter(value, payload)}\r\n        </div>)\r\n      );\r\n    }\r\n\r\n    if (!value) {\r\n      return null\r\n    }\r\n\r\n    return <div className={cn(\"font-medium\", labelClassName)}>{value}</div>;\r\n  }, [\r\n    label,\r\n    labelFormatter,\r\n    payload,\r\n    hideLabel,\r\n    labelClassName,\r\n    config,\r\n    labelKey,\r\n  ])\r\n\r\n  if (!active || !payload?.length) {\r\n    return null\r\n  }\r\n\r\n  const nestLabel = payload.length === 1 && indicator !== \"dot\"\r\n\r\n  return (\r\n    (<div\r\n      className={cn(\r\n        \"border-border/50 bg-background grid min-w-[8rem] items-start gap-1.5 rounded-lg border px-2.5 py-1.5 text-xs shadow-xl\",\r\n        className\r\n      )}>\r\n      {!nestLabel ? tooltipLabel : null}\r\n      <div className=\"grid gap-1.5\">\r\n        {payload.map((item, index) => {\r\n          const key = `${nameKey || item.name || item.dataKey || \"value\"}`\r\n          const itemConfig = getPayloadConfigFromPayload(config, item, key)\r\n          const indicatorColor = color || item.payload.fill || item.color\r\n\r\n          return (\r\n            (<div\r\n              key={item.dataKey}\r\n              className={cn(\r\n                \"[&>svg]:text-muted-foreground flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5\",\r\n                indicator === \"dot\" && \"items-center\"\r\n              )}>\r\n              {formatter && item?.value !== undefined && item.name ? (\r\n                formatter(item.value, item.name, item, index, item.payload)\r\n              ) : (\r\n                <>\r\n                  {itemConfig?.icon ? (\r\n                    <itemConfig.icon />\r\n                  ) : (\r\n                    !hideIndicator && (\r\n                      <div\r\n                        className={cn(\"shrink-0 rounded-[2px] border-(--color-border) bg-(--color-bg)\", {\r\n                          \"h-2.5 w-2.5\": indicator === \"dot\",\r\n                          \"w-1\": indicator === \"line\",\r\n                          \"w-0 border-[1.5px] border-dashed bg-transparent\":\r\n                            indicator === \"dashed\",\r\n                          \"my-0.5\": nestLabel && indicator === \"dashed\",\r\n                        })}\r\n                        style={\r\n                          {\r\n                            \"--color-bg\": indicatorColor,\r\n                            \"--color-border\": indicatorColor\r\n                          }\r\n                        } />\r\n                    )\r\n                  )}\r\n                  <div\r\n                    className={cn(\r\n                      \"flex flex-1 justify-between leading-none\",\r\n                      nestLabel ? \"items-end\" : \"items-center\"\r\n                    )}>\r\n                    <div className=\"grid gap-1.5\">\r\n                      {nestLabel ? tooltipLabel : null}\r\n                      <span className=\"text-muted-foreground\">\r\n                        {itemConfig?.label || item.name}\r\n                      </span>\r\n                    </div>\r\n                    {item.value && (\r\n                      <span className=\"text-foreground font-mono font-medium tabular-nums\">\r\n                        {item.value.toLocaleString()}\r\n                      </span>\r\n                    )}\r\n                  </div>\r\n                </>\r\n              )}\r\n            </div>)\r\n          );\r\n        })}\r\n      </div>\r\n    </div>)\r\n  );\r\n}\r\n\r\nconst ChartLegend = RechartsPrimitive.Legend\r\n\r\nfunction ChartLegendContent({\r\n  className,\r\n  hideIcon = false,\r\n  payload,\r\n  verticalAlign = \"bottom\",\r\n  nameKey\r\n}) {\r\n  const { config } = useChart()\r\n\r\n  if (!payload?.length) {\r\n    return null\r\n  }\r\n\r\n  return (\r\n    (<div\r\n      className={cn(\r\n        \"flex items-center justify-center gap-4\",\r\n        verticalAlign === \"top\" ? \"pb-3\" : \"pt-3\",\r\n        className\r\n      )}>\r\n      {payload.map((item) => {\r\n        const key = `${nameKey || item.dataKey || \"value\"}`\r\n        const itemConfig = getPayloadConfigFromPayload(config, item, key)\r\n\r\n        return (\r\n          (<div\r\n            key={item.value}\r\n            className={cn(\r\n              \"[&>svg]:text-muted-foreground flex items-center gap-1.5 [&>svg]:h-3 [&>svg]:w-3\"\r\n            )}>\r\n            {itemConfig?.icon && !hideIcon ? (\r\n              <itemConfig.icon />\r\n            ) : (\r\n              <div\r\n                className=\"h-2 w-2 shrink-0 rounded-[2px]\"\r\n                style={{\r\n                  backgroundColor: item.color,\r\n                }} />\r\n            )}\r\n            {itemConfig?.label}\r\n          </div>)\r\n        );\r\n      })}\r\n    </div>)\r\n  );\r\n}\r\n\r\n// Helper to extract item config from a payload.\r\nfunction getPayloadConfigFromPayload(\r\n  config,\r\n  payload,\r\n  key\r\n) {\r\n  if (typeof payload !== \"object\" || payload === null) {\r\n    return undefined\r\n  }\r\n\r\n  const payloadPayload =\r\n    \"payload\" in payload &&\r\n    typeof payload.payload === \"object\" &&\r\n    payload.payload !== null\r\n      ? payload.payload\r\n      : undefined\r\n\r\n  let configLabelKey = key\r\n\r\n  if (\r\n    key in payload &&\r\n    typeof payload[key] === \"string\"\r\n  ) {\r\n    configLabelKey = payload[key]\r\n  } else if (\r\n    payloadPayload &&\r\n    key in payloadPayload &&\r\n    typeof payloadPayload[key] === \"string\"\r\n  ) {\r\n    configLabelKey = payloadPayload[key]\r\n  }\r\n\r\n  return configLabelKey in config\r\n    ? config[configLabelKey]\r\n    : config[key];\r\n}\r\n\r\nexport {\r\n  ChartContainer,\r\n  ChartTooltip,\r\n  ChartTooltipContent,\r\n  ChartLegend,\r\n  ChartLegendContent,\r\n  ChartStyle,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;AACA;AACA;AAAA;AAAA;AAEA;;;AAJA;;;;AAMA,uCAAuC;AACvC,MAAM,SAAS;IACb,OAAO;IACP,MAAM;AACR;AAEA,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE;AAEzC,SAAS;;IACP,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;IAEjC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;GARS;AAUT,SAAS,eAAe,EACtB,EAAE,EACF,SAAS,EACT,QAAQ,EACR,MAAM,EACN,GAAG,OACJ;;IACC,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,QAAW,AAAD;IAC3B,MAAM,UAAU,CAAC,MAAM,EAAE,MAAM,SAAS,OAAO,CAAC,MAAM,KAAK;IAE3D,qBACG,6LAAC,aAAa,QAAQ;QAAC,OAAO;YAAE;QAAO;kBACtC,cAAA,6LAAC;YACC,aAAU;YACV,cAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+pBACA;YAED,GAAG,KAAK;;8BACT,6LAAC;oBAAW,IAAI;oBAAS,QAAQ;;;;;;8BACjC,6LAAC,sKAAA,CAAA,sBAAqC;8BACnC;;;;;;;;;;;;;;;;;AAKX;IA3BS;KAAA;AA6BT,MAAM,aAAa,CAAC,EAClB,EAAE,EACF,MAAM,EACP;IACC,MAAM,cAAc,OAAO,OAAO,CAAC,QAAQ,MAAM,CAAC,CAAC,GAAG,OAAO,GAAK,OAAO,KAAK,IAAI,OAAO,KAAK;IAE9F,IAAI,CAAC,YAAY,MAAM,EAAE;QACvB,OAAO;IACT;IAEA,qBACG,6LAAC;QACA,yBAAyB;YACvB,QAAQ,OAAO,OAAO,CAAC,QACpB,GAAG,CAAC,CAAC,CAAC,OAAO,OAAO,GAAK,CAAC;AACrC,EAAE,OAAO,aAAa,EAAE,GAAG;AAC3B,EAAE,YACD,GAAG,CAAC,CAAC,CAAC,KAAK,WAAW;oBACvB,MAAM,QACJ,WAAW,KAAK,EAAE,CAAC,MAAM,IACzB,WAAW,KAAK;oBAClB,OAAO,QAAQ,CAAC,UAAU,EAAE,IAAI,EAAE,EAAE,MAAM,CAAC,CAAC,GAAG;gBAC/C,GACC,IAAI,CAAC,MAAM;;AAEZ,CAAC,EACU,IAAI,CAAC;QACV;;;;;;AAEN;MA7BM;AA+BN,MAAM,eAAe,0JAAA,CAAA,UAAyB;AAE9C,SAAS,oBAAoB,EAC3B,MAAM,EACN,OAAO,EACP,SAAS,EACT,YAAY,KAAK,EACjB,YAAY,KAAK,EACjB,gBAAgB,KAAK,EACrB,KAAK,EACL,cAAc,EACd,cAAc,EACd,SAAS,EACT,KAAK,EACL,OAAO,EACP,QAAQ,EACT;;IACC,MAAM,EAAE,MAAM,EAAE,GAAG;IAEnB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;qDAAE;YACjC,IAAI,aAAa,CAAC,SAAS,QAAQ;gBACjC,OAAO;YACT;YAEA,MAAM,CAAC,KAAK,GAAG;YACf,MAAM,MAAM,GAAG,YAAY,MAAM,WAAW,MAAM,QAAQ,SAAS;YACnE,MAAM,aAAa,4BAA4B,QAAQ,MAAM;YAC7D,MAAM,QACJ,CAAC,YAAY,OAAO,UAAU,WAC1B,MAAM,CAAC,MAAM,EAAE,SAAS,QACxB,YAAY;YAElB,IAAI,gBAAgB;gBAClB,qBACG,6LAAC;oBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;8BAChC,eAAe,OAAO;;;;;;YAG7B;YAEA,IAAI,CAAC,OAAO;gBACV,OAAO;YACT;YAEA,qBAAO,6LAAC;gBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;0BAAkB;;;;;;QAC7D;oDAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,IAAI,CAAC,UAAU,CAAC,SAAS,QAAQ;QAC/B,OAAO;IACT;IAEA,MAAM,YAAY,QAAQ,MAAM,KAAK,KAAK,cAAc;IAExD,qBACG,6LAAC;QACA,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0HACA;;YAED,CAAC,YAAY,eAAe;0BAC7B,6LAAC;gBAAI,WAAU;0BACZ,QAAQ,GAAG,CAAC,CAAC,MAAM;oBAClB,MAAM,MAAM,GAAG,WAAW,KAAK,IAAI,IAAI,KAAK,OAAO,IAAI,SAAS;oBAChE,MAAM,aAAa,4BAA4B,QAAQ,MAAM;oBAC7D,MAAM,iBAAiB,SAAS,KAAK,OAAO,CAAC,IAAI,IAAI,KAAK,KAAK;oBAE/D,qBACG,6LAAC;wBAEA,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uGACA,cAAc,SAAS;kCAExB,aAAa,MAAM,UAAU,aAAa,KAAK,IAAI,GAClD,UAAU,KAAK,KAAK,EAAE,KAAK,IAAI,EAAE,MAAM,OAAO,KAAK,OAAO,kBAE1D;;gCACG,YAAY,qBACX,6LAAC,WAAW,IAAI;;;;2CAEhB,CAAC,+BACC,6LAAC;oCACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kEAAkE;wCAC9E,eAAe,cAAc;wCAC7B,OAAO,cAAc;wCACrB,mDACE,cAAc;wCAChB,UAAU,aAAa,cAAc;oCACvC;oCACA,OACE;wCACE,cAAc;wCACd,kBAAkB;oCACpB;;;;;;8CAIR,6LAAC;oCACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4CACA,YAAY,cAAc;;sDAE5B,6LAAC;4CAAI,WAAU;;gDACZ,YAAY,eAAe;8DAC5B,6LAAC;oDAAK,WAAU;8DACb,YAAY,SAAS,KAAK,IAAI;;;;;;;;;;;;wCAGlC,KAAK,KAAK,kBACT,6LAAC;4CAAK,WAAU;sDACb,KAAK,KAAK,CAAC,cAAc;;;;;;;;;;;;;;uBA1C/B,KAAK,OAAO;;;;;gBAkDvB;;;;;;;;;;;;AAIR;IAhIS;;QAeY;;;MAfZ;AAkIT,MAAM,cAAc,yJAAA,CAAA,SAAwB;AAE5C,SAAS,mBAAmB,EAC1B,SAAS,EACT,WAAW,KAAK,EAChB,OAAO,EACP,gBAAgB,QAAQ,EACxB,OAAO,EACR;;IACC,MAAM,EAAE,MAAM,EAAE,GAAG;IAEnB,IAAI,CAAC,SAAS,QAAQ;QACpB,OAAO;IACT;IAEA,qBACG,6LAAC;QACA,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0CACA,kBAAkB,QAAQ,SAAS,QACnC;kBAED,QAAQ,GAAG,CAAC,CAAC;YACZ,MAAM,MAAM,GAAG,WAAW,KAAK,OAAO,IAAI,SAAS;YACnD,MAAM,aAAa,4BAA4B,QAAQ,MAAM;YAE7D,qBACG,6LAAC;gBAEA,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV;;oBAED,YAAY,QAAQ,CAAC,yBACpB,6LAAC,WAAW,IAAI;;;;6CAEhB,6LAAC;wBACC,WAAU;wBACV,OAAO;4BACL,iBAAiB,KAAK,KAAK;wBAC7B;;;;;;oBAEH,YAAY;;eAbR,KAAK,KAAK;;;;;QAgBrB;;;;;;AAGN;IA7CS;;QAOY;;;MAPZ;AA+CT,gDAAgD;AAChD,SAAS,4BACP,MAAM,EACN,OAAO,EACP,GAAG;IAEH,IAAI,OAAO,YAAY,YAAY,YAAY,MAAM;QACnD,OAAO;IACT;IAEA,MAAM,iBACJ,aAAa,WACb,OAAO,QAAQ,OAAO,KAAK,YAC3B,QAAQ,OAAO,KAAK,OAChB,QAAQ,OAAO,GACf;IAEN,IAAI,iBAAiB;IAErB,IACE,OAAO,WACP,OAAO,OAAO,CAAC,IAAI,KAAK,UACxB;QACA,iBAAiB,OAAO,CAAC,IAAI;IAC/B,OAAO,IACL,kBACA,OAAO,kBACP,OAAO,cAAc,CAAC,IAAI,KAAK,UAC/B;QACA,iBAAiB,cAAc,CAAC,IAAI;IACtC;IAEA,OAAO,kBAAkB,SACrB,MAAM,CAAC,eAAe,GACtB,MAAM,CAAC,IAAI;AACjB", "debugId": null}}, {"offset": {"line": 1714, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/js/web3/packages/chario/src/lib/chart-data-processing.js"], "sourcesContent": ["// src/lib/chart-data-processing.js\r\n\r\n/**\r\n * Calculates the start and end dates for a given time range.\r\n * @param {string} timeRange - The time range (e.g., \"today\", \"yesterday\", \"thisWeek\", \"thisMonth\", \"thisYear\").\r\n * @returns {{startDate: Date, endDate: Date}} - Object with start and end Date objects.\r\n */\r\nfunction getDateRange(timeRange) {\r\n    const now = new Date();\r\n    let startDate = new Date(now);\r\n    let endDate = new Date(now);\r\n\r\n    startDate.setHours(0, 0, 0, 0); // Start of current day\r\n    endDate.setHours(23, 59, 59, 999); // End of current day\r\n\r\n    switch (timeRange) {\r\n        case 'yesterday':\r\n            startDate.setDate(startDate.getDate() - 1);\r\n            endDate.setDate(endDate.getDate() - 1);\r\n            break;\r\n        case 'thisWeek':\r\n            // Get Monday of the current week (assuming Monday is the first day of the week)\r\n            const dayOfWeek = (startDate.getDay() + 6) % 7; // Adjust to make Monday 0, Sunday 6\r\n            startDate.setDate(startDate.getDate() - dayOfWeek);\r\n            startDate.setHours(0, 0, 0, 0); // Ensure start of the day\r\n            endDate = new Date(startDate);\r\n            endDate.setDate(startDate.getDate() + 6); // End of Sunday\r\n            endDate.setHours(23, 59, 59, 999);\r\n            break;\r\n        case 'thisMonth':\r\n            startDate.setDate(1); // First day of current month\r\n            startDate.setHours(0, 0, 0, 0);\r\n            endDate = new Date(startDate);\r\n            endDate.setMonth(startDate.getMonth() + 1);\r\n            endDate.setDate(0); // Last day of current month\r\n            endDate.setHours(23, 59, 59, 999);\r\n            break;\r\n        case 'thisYear':\r\n            startDate.setMonth(0, 1); // January 1st of current year\r\n            startDate.setHours(0, 0, 0, 0);\r\n            endDate.setMonth(11, 31); // December 31st of current year\r\n            endDate.setHours(23, 59, 59, 999);\r\n            break;\r\n        // 'today' is handled by default initialization\r\n    }\r\n    return { startDate, endDate };\r\n}\r\n\r\n/**\r\n * Processes an array of donations into chart data based on the specified time range.\r\n * @param {Array<Object>} donations An array of donation objects, each with `createdAt` (Date) and `amountEth` (Decimal/String).\r\n * @param {string} timeRange - The desired time range for aggregation (e.g., \"today\", \"yesterday\", \"thisWeek\", \"thisMonth\", \"thisYear\").\r\n * @returns {Array<Object>} An array of objects suitable for Recharts, with aggregated totals.\r\n */\r\nexport function getChartDataForTimeRange(donations, timeRange) {\r\n    const { startDate, endDate } = getDateRange(timeRange);\r\n\r\n    // Filter donations to only include those within the calculated date range\r\n    const filteredDonations = donations.filter(donation => {\r\n        const createdAt = new Date(donation.createdAt); // Ensure it's a Date object\r\n        return createdAt >= startDate && createdAt <= endDate;\r\n    });\r\n\r\n    const dataMap = new Map(); // Map<key, totalEth>\r\n    let initialDataPoints = []; // To ensure all periods are represented, even with no data\r\n    let formatKey = (date) => date.toISOString(); // Default key formatter\r\n\r\n    // Determine aggregation granularity based on timeRange\r\n    if (timeRange === 'today' || timeRange === 'yesterday') {\r\n        // 30-minute intervals\r\n        const dayStart = new Date(startDate);\r\n        dayStart.setHours(0, 0, 0, 0);\r\n\r\n        for (let i = 0; i < 24 * 2; i++) { // 48 intervals in a day (24 hours * 2 per half hour)\r\n            const currentTime = new Date(dayStart.getTime() + i * 30 * 60 * 1000);\r\n            const hours = currentTime.getHours().toString().padStart(2, '0');\r\n            const minutes = currentTime.getMinutes().toString().padStart(2, '0');\r\n            const timeKey = `${hours}:${minutes}`;\r\n            initialDataPoints.push({ time: timeKey, totalDonationsEth: 0 });\r\n            dataMap.set(timeKey, 0);\r\n        }\r\n        formatKey = (date) => {\r\n            let minutes = date.getMinutes();\r\n            if (minutes >= 30) minutes = 30;\r\n            else minutes = 0;\r\n            return `${date.getHours().toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;\r\n        };\r\n    } else if (timeRange === 'thisWeek' || timeRange === 'thisMonth') {\r\n        // Daily aggregation\r\n        let currentDay = new Date(startDate);\r\n        while (currentDay <= endDate) {\r\n            const dateKey = currentDay.toISOString().split('T')[0]; // YYYY-MM-DD\r\n            initialDataPoints.push({ time: dateKey, totalDonationsEth: 0 });\r\n            dataMap.set(dateKey, 0);\r\n            currentDay.setDate(currentDay.getDate() + 1);\r\n            currentDay.setHours(0, 0, 0, 0); // Reset hours to avoid daylight saving issues\r\n        }\r\n        formatKey = (date) => date.toISOString().split('T')[0]; // YYYY-MM-DD\r\n    } else if (timeRange === 'thisYear') {\r\n        // Monthly aggregation\r\n        let currentMonth = new Date(startDate);\r\n        currentMonth.setDate(1); // Ensure it starts at the first of the month\r\n        currentMonth.setHours(0, 0, 0, 0);\r\n\r\n        while (currentMonth <= endDate) {\r\n            const monthKey = currentMonth.toISOString().substring(0, 7); // YYYY-MM\r\n            initialDataPoints.push({ time: monthKey, totalDonationsEth: 0 });\r\n            dataMap.set(monthKey, 0);\r\n            currentMonth.setMonth(currentMonth.getMonth() + 1);\r\n            // If we cross into the next year, ensure it stays within the current year's months\r\n            if (currentMonth.getFullYear() > endDate.getFullYear() && currentMonth.getMonth() > endDate.getMonth()) break;\r\n        }\r\n        formatKey = (date) => date.toISOString().substring(0, 7); // YYYY-MM\r\n    }\r\n\r\n    // Aggregate donations into the data map\r\n    for (const donation of filteredDonations) {\r\n        const createdAt = new Date(donation.createdAt);\r\n        const key = formatKey(createdAt);\r\n\r\n        const currentTotal = dataMap.get(key) || 0;\r\n\r\n        let donationAmount;\r\n        // Handle Decimal type from Prisma or string conversion\r\n        if (typeof donation.amountEth === 'object' && donation.amountEth !== null && donation.amountEth.constructor && donation.amountEth.constructor.name === 'Decimal') {\r\n            donationAmount = donation.amountEth.toNumber();\r\n        } else if (typeof donation.amountEth === 'string') {\r\n            donationAmount = parseFloat(donation.amountEth);\r\n        } else {\r\n            donationAmount = donation.amountEth; // Assume it's already a number\r\n        }\r\n\r\n        dataMap.set(key, currentTotal + donationAmount);\r\n    }\r\n\r\n    // Convert the map to an array of objects and ensure all initial points are included\r\n    const chartData = initialDataPoints.map(item => ({\r\n        ...item,\r\n        totalDonationsEth: dataMap.get(item.time) || 0 // Use aggregated value or default to 0\r\n    }));\r\n\r\n    // Sort the data by time key\r\n    chartData.sort((a, b) => {\r\n        if (timeRange === 'today' || timeRange === 'yesterday') {\r\n            const [hA, mA] = a.time.split(':').map(Number);\r\n            const [hB, mB] = b.time.split(':').map(Number);\r\n            return (hA * 60 + mA) - (hB * 60 + mB);\r\n        } else {\r\n            // Lexicographical sort works for YYYY-MM-DD and YYYY-MM\r\n            return a.time.localeCompare(b.time);\r\n        }\r\n    });\r\n\r\n    return chartData;\r\n}"], "names": [], "mappings": "AAAA,mCAAmC;AAEnC;;;;CAIC;;;AACD,SAAS,aAAa,SAAS;IAC3B,MAAM,MAAM,IAAI;IAChB,IAAI,YAAY,IAAI,KAAK;IACzB,IAAI,UAAU,IAAI,KAAK;IAEvB,UAAU,QAAQ,CAAC,GAAG,GAAG,GAAG,IAAI,uBAAuB;IACvD,QAAQ,QAAQ,CAAC,IAAI,IAAI,IAAI,MAAM,qBAAqB;IAExD,OAAQ;QACJ,KAAK;YACD,UAAU,OAAO,CAAC,UAAU,OAAO,KAAK;YACxC,QAAQ,OAAO,CAAC,QAAQ,OAAO,KAAK;YACpC;QACJ,KAAK;YACD,gFAAgF;YAChF,MAAM,YAAY,CAAC,UAAU,MAAM,KAAK,CAAC,IAAI,GAAG,oCAAoC;YACpF,UAAU,OAAO,CAAC,UAAU,OAAO,KAAK;YACxC,UAAU,QAAQ,CAAC,GAAG,GAAG,GAAG,IAAI,0BAA0B;YAC1D,UAAU,IAAI,KAAK;YACnB,QAAQ,OAAO,CAAC,UAAU,OAAO,KAAK,IAAI,gBAAgB;YAC1D,QAAQ,QAAQ,CAAC,IAAI,IAAI,IAAI;YAC7B;QACJ,KAAK;YACD,UAAU,OAAO,CAAC,IAAI,6BAA6B;YACnD,UAAU,QAAQ,CAAC,GAAG,GAAG,GAAG;YAC5B,UAAU,IAAI,KAAK;YACnB,QAAQ,QAAQ,CAAC,UAAU,QAAQ,KAAK;YACxC,QAAQ,OAAO,CAAC,IAAI,4BAA4B;YAChD,QAAQ,QAAQ,CAAC,IAAI,IAAI,IAAI;YAC7B;QACJ,KAAK;YACD,UAAU,QAAQ,CAAC,GAAG,IAAI,8BAA8B;YACxD,UAAU,QAAQ,CAAC,GAAG,GAAG,GAAG;YAC5B,QAAQ,QAAQ,CAAC,IAAI,KAAK,gCAAgC;YAC1D,QAAQ,QAAQ,CAAC,IAAI,IAAI,IAAI;YAC7B;IAER;IACA,OAAO;QAAE;QAAW;IAAQ;AAChC;AAQO,SAAS,yBAAyB,SAAS,EAAE,SAAS;IACzD,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,aAAa;IAE5C,0EAA0E;IAC1E,MAAM,oBAAoB,UAAU,MAAM,CAAC,CAAA;QACvC,MAAM,YAAY,IAAI,KAAK,SAAS,SAAS,GAAG,4BAA4B;QAC5E,OAAO,aAAa,aAAa,aAAa;IAClD;IAEA,MAAM,UAAU,IAAI,OAAO,qBAAqB;IAChD,IAAI,oBAAoB,EAAE,EAAE,2DAA2D;IACvF,IAAI,YAAY,CAAC,OAAS,KAAK,WAAW,IAAI,wBAAwB;IAEtE,uDAAuD;IACvD,IAAI,cAAc,WAAW,cAAc,aAAa;QACpD,sBAAsB;QACtB,MAAM,WAAW,IAAI,KAAK;QAC1B,SAAS,QAAQ,CAAC,GAAG,GAAG,GAAG;QAE3B,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,GAAG,IAAK;YAC7B,MAAM,cAAc,IAAI,KAAK,SAAS,OAAO,KAAK,IAAI,KAAK,KAAK;YAChE,MAAM,QAAQ,YAAY,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG;YAC5D,MAAM,UAAU,YAAY,UAAU,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG;YAChE,MAAM,UAAU,GAAG,MAAM,CAAC,EAAE,SAAS;YACrC,kBAAkB,IAAI,CAAC;gBAAE,MAAM;gBAAS,mBAAmB;YAAE;YAC7D,QAAQ,GAAG,CAAC,SAAS;QACzB;QACA,YAAY,CAAC;YACT,IAAI,UAAU,KAAK,UAAU;YAC7B,IAAI,WAAW,IAAI,UAAU;iBACxB,UAAU;YACf,OAAO,GAAG,KAAK,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;QAClG;IACJ,OAAO,IAAI,cAAc,cAAc,cAAc,aAAa;QAC9D,oBAAoB;QACpB,IAAI,aAAa,IAAI,KAAK;QAC1B,MAAO,cAAc,QAAS;YAC1B,MAAM,UAAU,WAAW,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,aAAa;YACrE,kBAAkB,IAAI,CAAC;gBAAE,MAAM;gBAAS,mBAAmB;YAAE;YAC7D,QAAQ,GAAG,CAAC,SAAS;YACrB,WAAW,OAAO,CAAC,WAAW,OAAO,KAAK;YAC1C,WAAW,QAAQ,CAAC,GAAG,GAAG,GAAG,IAAI,8CAA8C;QACnF;QACA,YAAY,CAAC,OAAS,KAAK,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,aAAa;IACzE,OAAO,IAAI,cAAc,YAAY;QACjC,sBAAsB;QACtB,IAAI,eAAe,IAAI,KAAK;QAC5B,aAAa,OAAO,CAAC,IAAI,6CAA6C;QACtE,aAAa,QAAQ,CAAC,GAAG,GAAG,GAAG;QAE/B,MAAO,gBAAgB,QAAS;YAC5B,MAAM,WAAW,aAAa,WAAW,GAAG,SAAS,CAAC,GAAG,IAAI,UAAU;YACvE,kBAAkB,IAAI,CAAC;gBAAE,MAAM;gBAAU,mBAAmB;YAAE;YAC9D,QAAQ,GAAG,CAAC,UAAU;YACtB,aAAa,QAAQ,CAAC,aAAa,QAAQ,KAAK;YAChD,mFAAmF;YACnF,IAAI,aAAa,WAAW,KAAK,QAAQ,WAAW,MAAM,aAAa,QAAQ,KAAK,QAAQ,QAAQ,IAAI;QAC5G;QACA,YAAY,CAAC,OAAS,KAAK,WAAW,GAAG,SAAS,CAAC,GAAG,IAAI,UAAU;IACxE;IAEA,wCAAwC;IACxC,KAAK,MAAM,YAAY,kBAAmB;QACtC,MAAM,YAAY,IAAI,KAAK,SAAS,SAAS;QAC7C,MAAM,MAAM,UAAU;QAEtB,MAAM,eAAe,QAAQ,GAAG,CAAC,QAAQ;QAEzC,IAAI;QACJ,uDAAuD;QACvD,IAAI,OAAO,SAAS,SAAS,KAAK,YAAY,SAAS,SAAS,KAAK,QAAQ,SAAS,SAAS,CAAC,WAAW,IAAI,SAAS,SAAS,CAAC,WAAW,CAAC,IAAI,KAAK,WAAW;YAC9J,iBAAiB,SAAS,SAAS,CAAC,QAAQ;QAChD,OAAO,IAAI,OAAO,SAAS,SAAS,KAAK,UAAU;YAC/C,iBAAiB,WAAW,SAAS,SAAS;QAClD,OAAO;YACH,iBAAiB,SAAS,SAAS,EAAE,+BAA+B;QACxE;QAEA,QAAQ,GAAG,CAAC,KAAK,eAAe;IACpC;IAEA,oFAAoF;IACpF,MAAM,YAAY,kBAAkB,GAAG,CAAC,CAAA,OAAQ,CAAC;YAC7C,GAAG,IAAI;YACP,mBAAmB,QAAQ,GAAG,CAAC,KAAK,IAAI,KAAK,EAAE,uCAAuC;QAC1F,CAAC;IAED,4BAA4B;IAC5B,UAAU,IAAI,CAAC,CAAC,GAAG;QACf,IAAI,cAAc,WAAW,cAAc,aAAa;YACpD,MAAM,CAAC,IAAI,GAAG,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC;YACvC,MAAM,CAAC,IAAI,GAAG,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC;YACvC,OAAO,AAAC,KAAK,KAAK,KAAM,CAAC,KAAK,KAAK,EAAE;QACzC,OAAO;YACH,wDAAwD;YACxD,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;QACtC;IACJ;IAEA,OAAO;AACX", "debugId": null}}, {"offset": {"line": 1869, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/js/web3/packages/chario/src/components/md/donations-chart.jsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport { Area, AreaChart, CartesianGrid, XAxis, YAxis } from \"recharts\"\r\n\r\nimport {\r\n    Card,\r\n    CardContent,\r\n    CardDescription,\r\n    CardHeader,\r\n    CardTitle,\r\n} from \"@/components/ui/card\"\r\nimport {\r\n    ChartContainer,\r\n    ChartTooltip,\r\n    ChartTooltipContent,\r\n} from \"@/components/ui/chart\"\r\nimport {\r\n    Select,\r\n    SelectContent,\r\n    SelectItem,\r\n    SelectTrigger,\r\n    SelectValue,\r\n} from \"@/components/ui/select\"\r\n// Ensure this path is correct for your project\r\nimport { getChartDataForTimeRange } from \"@/lib/chart-data-processing\"\r\n\r\nexport const description = \"Donations over time\"\r\n\r\n// --- Chart Configuration ---\r\nconst chartConfig = {\r\n    totalDonationsEth: {\r\n        label: \"Donations (ETH)\",\r\n        color: \"hsl(var(--chart-1))\", // Using a CSS variable for consistent theming\r\n    },\r\n};\r\n\r\nexport function DonationCharts({ donations }) { // Removed targetDay prop as timeRange handles it\r\n    const [timeRange, setTimeRange] = React.useState(\"today\");\r\n\r\n    // Process the donations whenever `donations` or `timeRange` changes\r\n    const processedChartData = React.useMemo(() => {\r\n        if (!donations || donations.length === 0) return [];\r\n\r\n        // Important: Convert `createdAt` strings to Date objects if they are not already.\r\n        const donationsWithDates = donations.map(d => ({\r\n            ...d,\r\n            createdAt: new Date(d.createdAt)\r\n        }));\r\n\r\n        return getChartDataForTimeRange(donationsWithDates, timeRange);\r\n    }, [donations, timeRange]);\r\n\r\n    // Dynamic description for the card title\r\n    const chartDescription = React.useMemo(() => {\r\n        const today = new Date();\r\n        const year = today.getFullYear();\r\n        const month = today.toLocaleString('default', { month: 'long' });\r\n\r\n        switch (timeRange) {\r\n            case 'today':\r\n                return `Showing total ETH donated for ${today.toLocaleDateString()} in 30-minute periods.`;\r\n            case 'yesterday':\r\n                const yesterday = new Date(today);\r\n                yesterday.setDate(today.getDate() - 1);\r\n                return `Showing total ETH donated for ${yesterday.toLocaleDateString()} in 30-minute periods.`;\r\n            case 'thisWeek':\r\n                // Calculate the first day of the current week (Monday)\r\n                const startOfWeek = new Date(today);\r\n                const dayOfWeek = (startOfWeek.getDay() + 6) % 7; // Adjust to make Monday 0\r\n                startOfWeek.setDate(startOfWeek.getDate() - dayOfWeek);\r\n                startOfWeek.setHours(0, 0, 0, 0);\r\n                // Calculate the last day of the current week (Sunday)\r\n                const endOfWeek = new Date(startOfWeek);\r\n                endOfWeek.setDate(startOfWeek.getDate() + 6);\r\n                endOfWeek.setHours(23, 59, 59, 999);\r\n                return `Showing total ETH donated per day from ${startOfWeek.toLocaleDateString()} to ${endOfWeek.toLocaleDateString()}.`;\r\n            case 'thisMonth':\r\n                return `Showing total ETH donated per day for ${month} ${year}.`;\r\n            case 'thisYear':\r\n                return `Showing total ETH donated per month for the year ${year}.`;\r\n            default:\r\n                return `Showing total ETH donated for the selected period.`;\r\n        }\r\n    }, [timeRange]);\r\n\r\n    // Dynamic X-Axis formatter based on timeRange for different granularities\r\n    const xAxisTickFormatter = (value) => {\r\n        if (timeRange === 'today' || timeRange === 'yesterday') {\r\n            return value; // \"HH:MM\"\r\n        } else if (timeRange === 'thisWeek' || timeRange === 'thisMonth') {\r\n            const date = new Date(value); // value is YYYY-MM-DD\r\n            return date.toLocaleDateString('en-US', { day: 'numeric', month: 'short' });\r\n        } else if (timeRange === 'thisYear') {\r\n            const date = new Date(value + '-01'); // value is YYYY-MM, append '-01' for valid date\r\n            return date.toLocaleDateString('en-US', { month: 'short', year: '2-digit' });\r\n        }\r\n        return value;\r\n    };\r\n\r\n    // Dynamic X-Axis minTickGap to avoid crowded labels for daily/monthly views\r\n    const xAxisMinTickGap = React.useMemo(() => {\r\n        if (timeRange === 'today' || timeRange === 'yesterday') {\r\n            return 32; // More space for 30-min intervals\r\n        }\r\n        return 1; // Less space needed for daily/monthly aggregation\r\n    }, [timeRange]);\r\n\r\n    return (\r\n        <Card className=\"pt-0 bg-card/50\">\r\n            <CardHeader className=\"flex items-center gap-2 space-y-0 border-b py-5 sm:flex-row\">\r\n                <div className=\"grid flex-1 gap-1\">\r\n                    <CardTitle>Donations Activity</CardTitle> {/* Removed fixed \"30-min intervals\" */}\r\n                    <CardDescription>{chartDescription}</CardDescription>\r\n                </div>\r\n                <Select value={timeRange} onValueChange={setTimeRange}>\r\n                    <SelectTrigger\r\n                        className=\"hidden w-[160px] rounded-lg sm:ml-auto sm:flex\"\r\n                        aria-label=\"Select time range\"\r\n                    >\r\n                        <SelectValue placeholder=\"Select Time Range\" />\r\n                    </SelectTrigger>\r\n                    <SelectContent className=\"rounded-xl\">\r\n                        <SelectItem value=\"today\" className=\"rounded-lg\">\r\n                            Today\r\n                        </SelectItem>\r\n                        <SelectItem value=\"yesterday\" className=\"rounded-lg\">\r\n                            Yesterday\r\n                        </SelectItem>\r\n                        <SelectItem value=\"thisWeek\" className=\"rounded-lg\">\r\n                            This Week\r\n                        </SelectItem>\r\n                        <SelectItem value=\"thisMonth\" className=\"rounded-lg\">\r\n                            This Month\r\n                        </SelectItem>\r\n                        <SelectItem value=\"thisYear\" className=\"rounded-lg\">\r\n                            This Year\r\n                        </SelectItem>\r\n                    </SelectContent>\r\n                </Select>\r\n            </CardHeader>\r\n            <CardContent className=\"px-2 pt-4 sm:px-6 sm:pt-6\">\r\n                <ChartContainer\r\n                    config={chartConfig}\r\n                    className=\"aspect-auto h-[250px] w-full\"\r\n                >\r\n                    <AreaChart data={processedChartData}>\r\n                        <defs>\r\n                            <linearGradient id=\"fillTotalDonationsEth\" x1=\"0\" y1=\"0\" x2=\"0\" y2=\"1\">\r\n                                <stop\r\n                                    offset=\"5%\"\r\n                                    stopColor=\"var(--chart-1)\"\r\n                                    stopOpacity={0.8}\r\n                                />\r\n                                <stop\r\n                                    offset=\"95%\"\r\n                                    stopColor=\"var(--chart-1)\"\r\n                                    stopOpacity={0.1}\r\n                                />\r\n                            </linearGradient>\r\n                        </defs>\r\n\r\n                        <CartesianGrid vertical={false} />\r\n                        <XAxis\r\n                            dataKey=\"time\"\r\n                            tickLine={false}\r\n                            axisLine={false}\r\n                            tickMargin={8}\r\n                            minTickGap={xAxisMinTickGap} // Dynamic gap\r\n                            tickFormatter={xAxisTickFormatter} // Dynamic formatter\r\n                        />\r\n                        <YAxis\r\n                            dataKey=\"totalDonationsEth\"\r\n                            tickLine={false}\r\n                            axisLine={false}\r\n                            tickMargin={8}\r\n                            tickFormatter={(value) => value.toFixed(2)}\r\n                            label={{\r\n                                value: 'ETH',\r\n                                angle: -90,\r\n                                position: 'insideLeft',\r\n                                // offset: -5,\r\n                                style: { textAnchor: 'middle' },\r\n                            }}\r\n                        />\r\n                        <ChartTooltip\r\n                            cursor={false}\r\n                            content={\r\n                                <ChartTooltipContent\r\n                                    className='bg-card ring-1 ring-border'\r\n                                    labelFormatter={(value) => {\r\n                                        if (timeRange === 'today' || timeRange === 'yesterday') {\r\n                                            return `Time: ${value}`; // HH:MM\r\n                                        } else if (timeRange === 'thisWeek' || timeRange === 'thisMonth') {\r\n                                            const date = new Date(value); // YYYY-MM-DD\r\n                                            return `Date: ${date.toLocaleDateString('en-US', { day: 'numeric', month: 'short', year: 'numeric' })}`;\r\n                                        } else if (timeRange === 'thisYear') {\r\n                                            const date = new Date(value + '-01'); // value is YYYY-MM, append -01 for valid date\r\n                                            return `Month: ${date.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}`;\r\n                                        }\r\n                                        return value;\r\n                                    }}\r\n                                    indicator=\"dot\"\r\n                                />\r\n                            }\r\n                        />\r\n                        <Area\r\n                            dataKey=\"totalDonationsEth\"\r\n                            type=\"monotone\"\r\n                            fill=\"url(#fillTotalDonationsEth)\" // Reference the new gradient ID\r\n                            stroke=\"oklch(0.9245 0.0533 67.0855)\" // Use the chart-1 color for the stroke\r\n                            stackId=\"a\"\r\n                            fillOpacity={1} // Fill opacity can be 1 since gradient handles transparency\r\n                        />\r\n                        {/* <Area\r\n                            dataKey=\"totalDonationsEth\"\r\n                            type=\"monotone\"\r\n                            fill=\"url(#fillTotalDonationsEth)\" // Reference the gradient ID\r\n                            stroke=\"hsl(var(--chart-1))\" // Use chart-1 color for stroke consistency\r\n                            stackId=\"a\"\r\n                            fillOpacity={1}\r\n                            dot={{\r\n                                stroke: 'hsl(var(--chart-1))', // Dot border color matches line\r\n                                strokeWidth: 2,\r\n                                fill: 'hsl(var(--background))', // Dot fill color (e.g., your card background)\r\n                                r: 3, // Dot radius\r\n                            }}\r\n                        /> */}\r\n                    </AreaChart>\r\n                </ChartContainer>\r\n            </CardContent>\r\n        </Card>\r\n    );\r\n}"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;AAOA;AAKA;AAOA,+CAA+C;AAC/C;;;AAzBA;;;;;;;AA2BO,MAAM,cAAc;AAE3B,8BAA8B;AAC9B,MAAM,cAAc;IAChB,mBAAmB;QACf,OAAO;QACP,OAAO;IACX;AACJ;AAEO,SAAS,eAAe,EAAE,SAAS,EAAE;;IACxC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IAEjD,oEAAoE;IACpE,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;sDAAE;YACrC,IAAI,CAAC,aAAa,UAAU,MAAM,KAAK,GAAG,OAAO,EAAE;YAEnD,kFAAkF;YAClF,MAAM,qBAAqB,UAAU,GAAG;iFAAC,CAAA,IAAK,CAAC;wBAC3C,GAAG,CAAC;wBACJ,WAAW,IAAI,KAAK,EAAE,SAAS;oBACnC,CAAC;;YAED,OAAO,CAAA,GAAA,4IAAA,CAAA,2BAAwB,AAAD,EAAE,oBAAoB;QACxD;qDAAG;QAAC;QAAW;KAAU;IAEzB,yCAAyC;IACzC,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;oDAAE;YACnC,MAAM,QAAQ,IAAI;YAClB,MAAM,OAAO,MAAM,WAAW;YAC9B,MAAM,QAAQ,MAAM,cAAc,CAAC,WAAW;gBAAE,OAAO;YAAO;YAE9D,OAAQ;gBACJ,KAAK;oBACD,OAAO,CAAC,8BAA8B,EAAE,MAAM,kBAAkB,GAAG,sBAAsB,CAAC;gBAC9F,KAAK;oBACD,MAAM,YAAY,IAAI,KAAK;oBAC3B,UAAU,OAAO,CAAC,MAAM,OAAO,KAAK;oBACpC,OAAO,CAAC,8BAA8B,EAAE,UAAU,kBAAkB,GAAG,sBAAsB,CAAC;gBAClG,KAAK;oBACD,uDAAuD;oBACvD,MAAM,cAAc,IAAI,KAAK;oBAC7B,MAAM,YAAY,CAAC,YAAY,MAAM,KAAK,CAAC,IAAI,GAAG,0BAA0B;oBAC5E,YAAY,OAAO,CAAC,YAAY,OAAO,KAAK;oBAC5C,YAAY,QAAQ,CAAC,GAAG,GAAG,GAAG;oBAC9B,sDAAsD;oBACtD,MAAM,YAAY,IAAI,KAAK;oBAC3B,UAAU,OAAO,CAAC,YAAY,OAAO,KAAK;oBAC1C,UAAU,QAAQ,CAAC,IAAI,IAAI,IAAI;oBAC/B,OAAO,CAAC,uCAAuC,EAAE,YAAY,kBAAkB,GAAG,IAAI,EAAE,UAAU,kBAAkB,GAAG,CAAC,CAAC;gBAC7H,KAAK;oBACD,OAAO,CAAC,sCAAsC,EAAE,MAAM,CAAC,EAAE,KAAK,CAAC,CAAC;gBACpE,KAAK;oBACD,OAAO,CAAC,iDAAiD,EAAE,KAAK,CAAC,CAAC;gBACtE;oBACI,OAAO,CAAC,kDAAkD,CAAC;YACnE;QACJ;mDAAG;QAAC;KAAU;IAEd,0EAA0E;IAC1E,MAAM,qBAAqB,CAAC;QACxB,IAAI,cAAc,WAAW,cAAc,aAAa;YACpD,OAAO,OAAO,UAAU;QAC5B,OAAO,IAAI,cAAc,cAAc,cAAc,aAAa;YAC9D,MAAM,OAAO,IAAI,KAAK,QAAQ,sBAAsB;YACpD,OAAO,KAAK,kBAAkB,CAAC,SAAS;gBAAE,KAAK;gBAAW,OAAO;YAAQ;QAC7E,OAAO,IAAI,cAAc,YAAY;YACjC,MAAM,OAAO,IAAI,KAAK,QAAQ,QAAQ,gDAAgD;YACtF,OAAO,KAAK,kBAAkB,CAAC,SAAS;gBAAE,OAAO;gBAAS,MAAM;YAAU;QAC9E;QACA,OAAO;IACX;IAEA,4EAA4E;IAC5E,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;mDAAE;YAClC,IAAI,cAAc,WAAW,cAAc,aAAa;gBACpD,OAAO,IAAI,kCAAkC;YACjD;YACA,OAAO,GAAG,kDAAkD;QAChE;kDAAG;QAAC;KAAU;IAEd,qBACI,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;;0BACZ,6LAAC,mIAAA,CAAA,aAAU;gBAAC,WAAU;;kCAClB,6LAAC;wBAAI,WAAU;;0CACX,6LAAC,mIAAA,CAAA,YAAS;0CAAC;;;;;;4BAA8B;0CACzC,6LAAC,mIAAA,CAAA,kBAAe;0CAAE;;;;;;;;;;;;kCAEtB,6LAAC,qIAAA,CAAA,SAAM;wBAAC,OAAO;wBAAW,eAAe;;0CACrC,6LAAC,qIAAA,CAAA,gBAAa;gCACV,WAAU;gCACV,cAAW;0CAEX,cAAA,6LAAC,qIAAA,CAAA,cAAW;oCAAC,aAAY;;;;;;;;;;;0CAE7B,6LAAC,qIAAA,CAAA,gBAAa;gCAAC,WAAU;;kDACrB,6LAAC,qIAAA,CAAA,aAAU;wCAAC,OAAM;wCAAQ,WAAU;kDAAa;;;;;;kDAGjD,6LAAC,qIAAA,CAAA,aAAU;wCAAC,OAAM;wCAAY,WAAU;kDAAa;;;;;;kDAGrD,6LAAC,qIAAA,CAAA,aAAU;wCAAC,OAAM;wCAAW,WAAU;kDAAa;;;;;;kDAGpD,6LAAC,qIAAA,CAAA,aAAU;wCAAC,OAAM;wCAAY,WAAU;kDAAa;;;;;;kDAGrD,6LAAC,qIAAA,CAAA,aAAU;wCAAC,OAAM;wCAAW,WAAU;kDAAa;;;;;;;;;;;;;;;;;;;;;;;;0BAMhE,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;0BACnB,cAAA,6LAAC,oIAAA,CAAA,iBAAc;oBACX,QAAQ;oBACR,WAAU;8BAEV,cAAA,6LAAC,wJAAA,CAAA,YAAS;wBAAC,MAAM;;0CACb,6LAAC;0CACG,cAAA,6LAAC;oCAAe,IAAG;oCAAwB,IAAG;oCAAI,IAAG;oCAAI,IAAG;oCAAI,IAAG;;sDAC/D,6LAAC;4CACG,QAAO;4CACP,WAAU;4CACV,aAAa;;;;;;sDAEjB,6LAAC;4CACG,QAAO;4CACP,WAAU;4CACV,aAAa;;;;;;;;;;;;;;;;;0CAKzB,6LAAC,gKAAA,CAAA,gBAAa;gCAAC,UAAU;;;;;;0CACzB,6LAAC,wJAAA,CAAA,QAAK;gCACF,SAAQ;gCACR,UAAU;gCACV,UAAU;gCACV,YAAY;gCACZ,YAAY;gCACZ,eAAe;;;;;;0CAEnB,6LAAC,wJAAA,CAAA,QAAK;gCACF,SAAQ;gCACR,UAAU;gCACV,UAAU;gCACV,YAAY;gCACZ,eAAe,CAAC,QAAU,MAAM,OAAO,CAAC;gCACxC,OAAO;oCACH,OAAO;oCACP,OAAO,CAAC;oCACR,UAAU;oCACV,cAAc;oCACd,OAAO;wCAAE,YAAY;oCAAS;gCAClC;;;;;;0CAEJ,6LAAC,oIAAA,CAAA,eAAY;gCACT,QAAQ;gCACR,uBACI,6LAAC,oIAAA,CAAA,sBAAmB;oCAChB,WAAU;oCACV,gBAAgB,CAAC;wCACb,IAAI,cAAc,WAAW,cAAc,aAAa;4CACpD,OAAO,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ;wCACrC,OAAO,IAAI,cAAc,cAAc,cAAc,aAAa;4CAC9D,MAAM,OAAO,IAAI,KAAK,QAAQ,aAAa;4CAC3C,OAAO,CAAC,MAAM,EAAE,KAAK,kBAAkB,CAAC,SAAS;gDAAE,KAAK;gDAAW,OAAO;gDAAS,MAAM;4CAAU,IAAI;wCAC3G,OAAO,IAAI,cAAc,YAAY;4CACjC,MAAM,OAAO,IAAI,KAAK,QAAQ,QAAQ,8CAA8C;4CACpF,OAAO,CAAC,OAAO,EAAE,KAAK,kBAAkB,CAAC,SAAS;gDAAE,OAAO;gDAAQ,MAAM;4CAAU,IAAI;wCAC3F;wCACA,OAAO;oCACX;oCACA,WAAU;;;;;;;;;;;0CAItB,6LAAC,uJAAA,CAAA,OAAI;gCACD,SAAQ;gCACR,MAAK;gCACL,MAAK,8BAA8B,gCAAgC;;gCACnE,QAAO,+BAA+B,uCAAuC;;gCAC7E,SAAQ;gCACR,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqBzC;GApMgB;KAAA", "debugId": null}}, {"offset": {"line": 2272, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/js/web3/packages/chario/src/app/home/<USER>/donations/_components/my-donations-page.jsx"], "sourcesContent": ["'use client'\r\n\r\nimport { DonationsTable } from '@/components/md/donation-table';\r\nimport { DonationCharts } from '@/components/md/donations-chart';\r\nimport React, { useEffect, useState } from 'react';\r\nimport { useTranslations } from '@/contexts/LanguageContext';\r\n\r\nfunction MyDonationsPage({ donations: rawDonations, user: rawUser }) {\r\n  const t = useTranslations();\r\n  const [donations, setDonations] = useState();\r\n  const [user, setUser] = useState();\r\n\r\n  useEffect(() => {\r\n    // Parse donations once and set the state\r\n    if (rawDonations) {\r\n      setDonations(JSON.parse(rawDonations));\r\n      // console.log(\"Donations:\", JSON.parse(rawDonations)); // Keep this for debugging if needed\r\n    }\r\n    if (rawUser) {\r\n      setUser(JSON.parse(rawUser));\r\n      // console.log(\"User:\", JSON.parse(rawUser)); // Keep this for debugging if needed\r\n    }\r\n  }, [rawDonations]);\r\n\r\n  return (\r\n    <div className='p-2 md:p-6 lg:p-8 pt-10'>\r\n      <div className='mb-6'>\r\n        <div className='text-muted-foreground text-lg cursor-pointer'>\r\n          <span className='text-accent-foreground font-semibold'>{t('charity.totalDonated')}:</span> <span className='text-emerald-400 ml-3'>${user?.amountSentInDollars}</span>\r\n        </div>\r\n        <div className='text-muted-foreground text-lg cursor-pointer'>\r\n          <span className='text-accent-foreground font-semibold'>{t('charity.myDonations')}:</span> <span className='ml-2 text-primary'>{donations?.length}</span>\r\n        </div>\r\n      </div>\r\n      {donations?.length > 0 && donations ? (\r\n        <>\r\n          <DonationCharts\r\n            donations={donations}\r\n          />\r\n          <div className=\"mt-8\"> {/* Add some margin above the table */}\r\n            <DonationsTable\r\n              donations={donations}\r\n            />\r\n          </div>\r\n        </>\r\n      ) : (\r\n        <p className=\"text-center text-muted-foreground\">{t('charity.noDonations')}</p>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default MyDonationsPage;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOA,SAAS,gBAAgB,EAAE,WAAW,YAAY,EAAE,MAAM,OAAO,EAAE;;IACjE,MAAM,IAAI,CAAA,GAAA,sIAAA,CAAA,kBAAe,AAAD;IACxB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;IACzC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;IAE/B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,yCAAyC;YACzC,IAAI,cAAc;gBAChB,aAAa,KAAK,KAAK,CAAC;YACxB,4FAA4F;YAC9F;YACA,IAAI,SAAS;gBACX,QAAQ,KAAK,KAAK,CAAC;YACnB,kFAAkF;YACpF;QACF;oCAAG;QAAC;KAAa;IAEjB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;;oCAAwC,EAAE;oCAAwB;;;;;;;4BAAQ;0CAAC,6LAAC;gCAAK,WAAU;;oCAAwB;oCAAE,MAAM;;;;;;;;;;;;;kCAE7I,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;;oCAAwC,EAAE;oCAAuB;;;;;;;4BAAQ;0CAAC,6LAAC;gCAAK,WAAU;0CAAqB,WAAW;;;;;;;;;;;;;;;;;;YAG7I,WAAW,SAAS,KAAK,0BACxB;;kCACE,6LAAC,iJAAA,CAAA,iBAAc;wBACb,WAAW;;;;;;kCAEb,6LAAC;wBAAI,WAAU;;4BAAO;0CACpB,6LAAC,gJAAA,CAAA,iBAAc;gCACb,WAAW;;;;;;;;;;;;;6CAKjB,6LAAC;gBAAE,WAAU;0BAAqC,EAAE;;;;;;;;;;;;AAI5D;GA3CS;;QACG,sIAAA,CAAA,kBAAe;;;KADlB;uCA6CM", "debugId": null}}]}