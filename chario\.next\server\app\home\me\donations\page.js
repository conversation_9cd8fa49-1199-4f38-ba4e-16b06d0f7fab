const CHUNK_PUBLIC_PATH = "server/app/home/<USER>/donations/page.js";
const runtime = require("../../../../chunks/ssr/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_0962b827._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__4c58c055._.js");
runtime.loadChunk("server/chunks/ssr/src_app_a4430781._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__b581052d._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_96715ba7._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_client_components_forbidden-error_ea7ea172.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_client_components_unauthorized-error_c8949b27.js");
runtime.loadChunk("server/chunks/ssr/_2c5903c8._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_32ddb012._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_zod_v3_020e7f8a._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_better-call_dist_index_21ba3623.js");
runtime.loadChunk("server/chunks/ssr/node_modules_better-auth_dist_shared_856e6e8a._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_better-auth_dist_plugins_7aacf88f._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_better-auth_dist_58a41214._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_jose_dist_webapi_be919571._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_kysely_dist_esm_85f6825d._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_e27ad430._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__2b0b37b2._.js");
runtime.loadChunk("server/chunks/ssr/_13412975._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/home/<USER>/donations/page/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-page.js?page=/home/<USER>/donations/page { METADATA_0 => \"[project]/src/app/favicon.ico.mjs { IMAGE => \\\"[project]/src/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js server component)\", MODULE_1 => \"[project]/src/app/layout.jsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_2 => \"[project]/node_modules/next/dist/client/components/not-found-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_3 => \"[project]/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_4 => \"[project]/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_5 => \"[project]/src/app/home/<USER>", MODULE_6 => \"[project]/src/app/home/<USER>/layout.jsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_7 => \"[project]/src/app/home/<USER>/donations/page.jsx [app-rsc] (ecmascript, Next.js server component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-page.js?page=/home/<USER>/donations/page { METADATA_0 => \"[project]/src/app/favicon.ico.mjs { IMAGE => \\\"[project]/src/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js server component)\", MODULE_1 => \"[project]/src/app/layout.jsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_2 => \"[project]/node_modules/next/dist/client/components/not-found-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_3 => \"[project]/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_4 => \"[project]/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_5 => \"[project]/src/app/home/<USER>", MODULE_6 => \"[project]/src/app/home/<USER>/layout.jsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_7 => \"[project]/src/app/home/<USER>/donations/page.jsx [app-rsc] (ecmascript, Next.js server component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
