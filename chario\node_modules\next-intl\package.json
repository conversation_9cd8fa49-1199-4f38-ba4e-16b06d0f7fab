{"name": "next-intl", "version": "4.3.4", "sideEffects": false, "author": "<PERSON> <<EMAIL>>", "funding": [{"type": "individual", "url": "https://github.com/sponsors/amannn"}], "description": "Internationalization (i18n) for Next.js", "license": "MIT", "homepage": "https://next-intl.dev", "repository": {"type": "git", "url": "https://github.com/amannn/next-intl"}, "scripts": {"build": "rm -rf dist && rollup -c", "test": "TZ=Europe/Berlin vitest", "lint": "pnpm run lint:source && pnpm run lint:package", "lint:source": "eslint src test && tsc --noEmit && pnpm run lint:prettier", "lint:package": "publint && attw --pack --ignore-rules=cjs-resolves-to-esm", "lint:prettier": "prettier src --check", "prepublishOnly": "turbo build && cp ../../README.md .", "postpublish": "git checkout . && rm ./README.md", "size": "size-limit"}, "type": "module", "main": "./dist/esm/production/index.react-client.js", "typings": "./dist/types/index.react-client.d.ts", "exports": {".": {"types": "./dist/types/index.react-client.d.ts", "react-server": {"development": "./dist/esm/development/index.react-server.js", "default": "./dist/esm/production/index.react-server.js"}, "development": "./dist/esm/development/index.react-client.js", "default": "./dist/esm/production/index.react-client.js"}, "./server": {"types": "./dist/types/server.react-server.d.ts", "react-server": {"development": "./dist/esm/development/server.react-server.js", "default": "./dist/esm/production/server.react-server.js"}, "development": "./dist/esm/development/server.react-client.js", "default": "./dist/esm/production/server.react-client.js"}, "./config": {"types": "./dist/types/config.d.ts", "development": "./dist/esm/development/config.js", "default": "./dist/esm/production/config.js"}, "./middleware": {"types": "./dist/types/middleware.d.ts", "development": "./dist/esm/development/middleware.js", "default": "./dist/esm/production/middleware.js"}, "./navigation": {"types": "./dist/types/navigation.react-client.d.ts", "react-server": {"development": "./dist/esm/development/navigation.react-server.js", "default": "./dist/esm/production/navigation.react-server.js"}, "development": "./dist/esm/development/navigation.react-client.js", "default": "./dist/esm/production/navigation.react-client.js"}, "./routing": {"types": "./dist/types/routing.d.ts", "development": "./dist/esm/development/routing.js", "default": "./dist/esm/production/routing.js"}, "./plugin": {"import": {"types": "./dist/types/plugin.d.ts", "development": "./dist/esm/development/plugin.js", "default": "./dist/esm/production/plugin.js"}, "require": {"types": "./plugin.d.cts", "default": "./dist/cjs/development/plugin.cjs"}, "default": "./dist/esm/production/plugin.js"}}, "files": ["dist", "server.d.ts", "navigation.d.ts", "middleware.d.ts", "plugin.d.ts", "plugin.d.cts", "routing.d.ts", "config.d.ts"], "keywords": ["react", "intl", "i18n", "internationalization", "localization", "translate", "translation", "format", "formatting", "next", "next.js"], "dependencies": {"@formatjs/intl-localematcher": "^0.5.4", "negotiator": "^1.0.0", "use-intl": "^4.3.4"}, "peerDependencies": {"next": "^12.0.0 || ^13.0.0 || ^14.0.0 || ^15.0.0", "react": "^16.8.0 || ^17.0.0 || ^18.0.0 || >=19.0.0-rc <19.0.0 || ^19.0.0", "typescript": "^5.0.0"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "44f07b236ad9b1a9d6e99e55507e7a67939efe5a"}