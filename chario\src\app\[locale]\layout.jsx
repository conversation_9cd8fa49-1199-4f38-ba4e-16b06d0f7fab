import { Providers } from "@/providers/web3";
import { ThemeProvider } from "@/providers/theme-provider";
import { Toaster } from "sonner";
import ModalProvider from "@/providers/modal-provider";
import { NextIntlClientProvider } from 'next-intl';
import { getMessages } from 'next-intl/server';
import { notFound } from 'next/navigation';

const locales = ['ru', 'en'];

export async function generateMetadata({ params }) {
  const { locale } = await params;
  const messages = await getMessages({ locale });

  return {
    title: messages.metadata?.title || "Chario",
    description: messages.metadata?.description || "Blockchain Charity Platform",
  };
}

export default async function LocaleLayout({ children, params }) {
  const { locale } = await params;

  // Validate that the incoming `locale` parameter is valid
  if (!locales.includes(locale)) {
    notFound();
  }

  // Providing all messages to the client
  // side is the easiest way to get started
  const messages = await getMessages();

  return (
    <NextIntlClientProvider messages={messages}>
      <ThemeProvider
        attribute="class"
        defaultTheme="light"
        enableSystem
        disableTransitionOnChange
      >
        <Providers>
          {children}
          <Toaster />
          <ModalProvider />
        </Providers>
      </ThemeProvider>
    </NextIntlClientProvider>
  );
}
