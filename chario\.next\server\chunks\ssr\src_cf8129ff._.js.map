{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/js/web3/packages/chario/src/i18n/request.js"], "sourcesContent": ["import { getRequestConfig } from 'next-intl/server';\nimport { notFound } from 'next/navigation';\n\n// Can be imported from a shared config\nconst locales = ['ru', 'en'];\n\nexport default getRequestConfig(async ({ locale }) => {\n  // Validate that the incoming `locale` parameter is valid\n  if (!locales.includes(locale)) notFound();\n\n  return {\n    messages: (await import(`../messages/${locale}.json`)).default\n  };\n});\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;;;AAEA,uCAAuC;AACvC,MAAM,UAAU;IAAC;IAAM;CAAK;uCAEb,CAAA,GAAA,0PAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO,EAAE,MAAM,EAAE;IAC/C,yDAAyD;IACzD,IAAI,CAAC,QAAQ,QAAQ,CAAC,SAAS,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IAEtC,OAAO;QACL,UAAU,CAAC;;;;;;;;;kBAAa,CAAC,YAAY,EAAE,OAAO,KAAK,CAAC,CAAC,EAAE,OAAO;IAChE;AACF", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/js/web3/packages/chario/src/app/%5Blocale%5D/layout.jsx"], "sourcesContent": ["import { Providers } from \"@/providers/web3\";\nimport { ThemeProvider } from \"@/providers/theme-provider\";\nimport { Toaster } from \"sonner\";\nimport ModalProvider from \"@/providers/modal-provider\";\nimport { NextIntlClientProvider } from 'next-intl';\nimport { getMessages } from 'next-intl/server';\nimport { notFound } from 'next/navigation';\n\nconst locales = ['ru', 'en'];\n\nexport async function generateMetadata({ params }) {\n  const { locale } = await params;\n  const messages = await getMessages({ locale });\n\n  return {\n    title: messages.metadata?.title || \"Chario\",\n    description: messages.metadata?.description || \"Blockchain Charity Platform\",\n  };\n}\n\nexport default async function LocaleLayout({ children, params }) {\n  const { locale } = await params;\n\n  // Validate that the incoming `locale` parameter is valid\n  if (!locales.includes(locale)) {\n    notFound();\n  }\n\n  // Providing all messages to the client\n  // side is the easiest way to get started\n  const messages = await getMessages();\n\n  return (\n    <NextIntlClientProvider messages={messages}>\n      <ThemeProvider\n        attribute=\"class\"\n        defaultTheme=\"light\"\n        enableSystem\n        disableTransitionOnChange\n      >\n        <Providers>\n          {children}\n          <Toaster />\n          <ModalProvider />\n        </Providers>\n      </ThemeProvider>\n    </NextIntlClientProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;;;;;;;;;AAEA,MAAM,UAAU;IAAC;IAAM;CAAK;AAErB,eAAe,iBAAiB,EAAE,MAAM,EAAE;IAC/C,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IACzB,MAAM,WAAW,MAAM,CAAA,GAAA,gPAAA,CAAA,cAAW,AAAD,EAAE;QAAE;IAAO;IAE5C,OAAO;QACL,OAAO,SAAS,QAAQ,EAAE,SAAS;QACnC,aAAa,SAAS,QAAQ,EAAE,eAAe;IACjD;AACF;AAEe,eAAe,aAAa,EAAE,QAAQ,EAAE,MAAM,EAAE;IAC7D,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IAEzB,yDAAyD;IACzD,IAAI,CAAC,QAAQ,QAAQ,CAAC,SAAS;QAC7B,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IACT;IAEA,uCAAuC;IACvC,yCAAyC;IACzC,MAAM,WAAW,MAAM,CAAA,GAAA,gPAAA,CAAA,cAAW,AAAD;IAEjC,qBACE,8OAAC,kQAAA,CAAA,yBAAsB;QAAC,UAAU;kBAChC,cAAA,8OAAC,sIAAA,CAAA,gBAAa;YACZ,WAAU;YACV,cAAa;YACb,YAAY;YACZ,yBAAyB;sBAEzB,cAAA,8OAAC,yHAAA,CAAA,YAAS;;oBACP;kCACD,8OAAC,wIAAA,CAAA,UAAO;;;;;kCACR,8OAAC,sIAAA,CAAA,UAAa;;;;;;;;;;;;;;;;;;;;;AAKxB", "debugId": null}}]}