{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/js/web3/packages/chario/src/components/ui/text-shimmer.jsx"], "sourcesContent": ["'use client';\r\nimport React, { useMemo } from 'react';\r\nimport { motion } from 'motion/react';\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction TextShimmerComponent({\r\n    children,\r\n    as: Component = 'p',\r\n    className,\r\n    duration = 2,\r\n    spread = 2,\r\n}) {\r\n    const MotionComponent = motion.create(\r\n        Component\r\n    );\r\n\r\n    const dynamicSpread = useMemo(() => {\r\n        return children.length * spread;\r\n    }, [children, spread]);\r\n\r\n    return (\r\n        <MotionComponent\r\n            className={cn(\r\n                'relative inline-block bg-[length:250%_100%,auto] bg-clip-text',\r\n                'text-transparent [--base-color:#a1a1aa] [--base-gradient-color:#000]',\r\n                '[background-repeat:no-repeat,padding-box] [--bg:linear-gradient(90deg,#0000_calc(50%-var(--spread)),var(--base-gradient-color),#0000_calc(50%+var(--spread)))]',\r\n                'dark:[--base-color:#71717a] dark:[--base-gradient-color:#ffffff] dark:[--bg:linear-gradient(90deg,#0000_calc(50%-var(--spread)),var(--base-gradient-color),#0000_calc(50%+var(--spread)))]',\r\n                className\r\n            )}\r\n            initial={{ backgroundPosition: '100% center' }}\r\n            animate={{ backgroundPosition: '0% center' }}\r\n            transition={{\r\n                repeat: Infinity,\r\n                duration,\r\n                ease: 'linear',\r\n            }}\r\n            style={\r\n                {\r\n                    '--spread': `${dynamicSpread}px`,\r\n                    backgroundImage: `var(--bg), linear-gradient(var(--base-color), var(--base-color))`,\r\n                }\r\n            }\r\n        >\r\n            {children}\r\n        </MotionComponent>\r\n    );\r\n}\r\n\r\nexport const TextShimmer = React.memo(TextShimmerComponent);\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAHA;;;;;AAKA,SAAS,qBAAqB,EAC1B,QAAQ,EACR,IAAI,YAAY,GAAG,EACnB,SAAS,EACT,WAAW,CAAC,EACZ,SAAS,CAAC,EACb;IACG,MAAM,kBAAkB,0LAAA,CAAA,SAAM,CAAC,MAAM,CACjC;IAGJ,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC1B,OAAO,SAAS,MAAM,GAAG;IAC7B,GAAG;QAAC;QAAU;KAAO;IAErB,qBACI,8OAAC;QACG,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACR,iEACA,wEACA,kKACA,8LACA;QAEJ,SAAS;YAAE,oBAAoB;QAAc;QAC7C,SAAS;YAAE,oBAAoB;QAAY;QAC3C,YAAY;YACR,QAAQ;YACR;YACA,MAAM;QACV;QACA,OACI;YACI,YAAY,GAAG,cAAc,EAAE,CAAC;YAChC,iBAAiB,CAAC,gEAAgE,CAAC;QACvF;kBAGH;;;;;;AAGb;AAEO,MAAM,4BAAc,qMAAA,CAAA,UAAK,CAAC,IAAI,CAAC", "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/js/web3/packages/chario/src/components/md/single-image-upload.jsx"], "sourcesContent": ["'use client'\r\n\r\nimport React, { memo, useEffect, useRef, useState, useCallback } from 'react'\r\nimport { Input } from '../ui/input';\r\nimport { CloudUpload, LoaderCircleIcon, RefreshCcw, TrashIcon, X } from 'lucide-react';\r\nimport { cn } from '@/lib/utils';\r\nimport { toast } from 'sonner';\r\n// import { set } from 'zod'; // This import seems unused and might cause issues if not installed\r\nimport { TextShimmer } from '../ui/text-shimmer';\r\nimport { FaFileAlt, FaFileImage } from 'react-icons/fa';\r\n\r\n// Define common image extensions\r\nconst IMAGE_EXTENSIONS = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg', 'bmp', 'tiff', 'tif'];\r\nconst IMAGE_MIME_TYPES = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml', 'image/bmp', 'image/tiff'];\r\n\r\nfunction SingleImageUpload({ file, setFile, fileUploadDetails, className }) { // Removed allowedExtensions prop, will use IMAGE_EXTENSIONS directly\r\n\r\n    const [isDragging, setIsDragging] = useState(false);\r\n    const [uploadProgress, setUploadProgress] = useState(null);\r\n    const xhrRef = useRef(null);\r\n    const fileRef = useRef(file); // Holds the currently processed file for XHR callbacks\r\n    const [previewUrl, setPreviewUrl] = useState(null); // State for image preview URL\r\n\r\n    // Update fileRef and previewUrl when the external 'file' prop changes\r\n    useEffect(() => {\r\n        fileRef.current = file;\r\n        if (file && file.file_type && IMAGE_MIME_TYPES.includes(file.file_type)) {\r\n            // If the file is already a File object (e.g., from an initial prop value)\r\n            if (file.file instanceof File) {\r\n                 const url = URL.createObjectURL(file.file);\r\n                 setPreviewUrl(url);\r\n                 return () => URL.revokeObjectURL(url); // Clean up URL\r\n            }\r\n            // If the file is an object with a file_path (e.g., from previous upload)\r\n            else if (file.file_path) {\r\n                setPreviewUrl(`${process.env.NEXT_PUBLIC_IPFS_GATEWAY}/${file.file_cid}`);\r\n            }\r\n        } else {\r\n            setPreviewUrl(null); // Clear preview if not an image or no file\r\n        }\r\n    }, [file]);\r\n\r\n\r\n    function isValidImageFile(selecetedFile) {\r\n        const extension = selecetedFile.name.split('.').pop()?.toLowerCase();\r\n        const isValidExtension = IMAGE_EXTENSIONS.includes(extension);\r\n        const isImageMimeType = IMAGE_MIME_TYPES.includes(selecetedFile.type);\r\n\r\n        if (!isValidExtension || !isImageMimeType) {\r\n            toast.error(`.${extension} is not an allowed image type.`);\r\n            return false;\r\n        }\r\n\r\n        if (selecetedFile.size > 1024 * 1024 * 15) {\r\n            toast.error(`File ${selecetedFile.name} is too large (max 15 MB).`);\r\n            return false;\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    function handleFileChange(e) {\r\n        const selecetedFile = Array.from(e.target.files)[0];\r\n\r\n        if (!selecetedFile) {\r\n            toast.info(`No file provided.`);\r\n            return;\r\n        }\r\n\r\n        if (!isValidImageFile(selecetedFile)) {\r\n            return;\r\n        }\r\n\r\n        const newFile = {\r\n            file: selecetedFile, // Keep the raw File object\r\n            id: Math.random().toString(36).substring(2, 9),\r\n            file_name: selecetedFile.name,\r\n            file_size: selecetedFile.size,\r\n            file_type: selecetedFile.type,\r\n            // local_preview_url: URL.createObjectURL(selecetedFile) // Add local URL for immediate preview\r\n        }\r\n        handleUpload(newFile);\r\n    }\r\n\r\n    function handleDrop(e) {\r\n        e.preventDefault();\r\n        setIsDragging(false);\r\n\r\n        const droppedFile = Array.from(e.dataTransfer.files)[0];\r\n\r\n        if (!droppedFile) {\r\n            toast.info(`No file provided.`);\r\n            return;\r\n        }\r\n\r\n        if (!isValidImageFile(droppedFile)) {\r\n            return;\r\n        }\r\n\r\n        const newFile = {\r\n            file: droppedFile, // Keep the raw File object\r\n            id: Math.random().toString(36).substring(2, 9),\r\n            file_name: droppedFile.name,\r\n            file_size: droppedFile.size,\r\n            file_type: droppedFile.type,\r\n            // local_preview_url: URL.createObjectURL(droppedFile) // Add local URL for immediate preview\r\n        }\r\n        handleUpload(newFile);\r\n    }\r\n\r\n    async function deleteFile() {\r\n        console.log(\"Deleting file:\", file)\r\n        setFile({\r\n            ...file,\r\n            loading: true // Indicate deletion is in progress\r\n        })\r\n\r\n        // If it's a freshly selected local file (not yet uploaded), just clear it\r\n        if (!file.file_path && file.file instanceof File) {\r\n            setFile(null);\r\n            setPreviewUrl(null); // Clear preview\r\n            toast.success(\"File removed.\");\r\n            return;\r\n        }\r\n\r\n        // Otherwise, proceed with API call for uploaded files\r\n        try {\r\n            const result = await fetch(`/api/v1/files?file_path=${file.file_path}&file_id=${file.id}&details=${JSON.stringify(fileUploadDetails)}`, {\r\n                method: 'DELETE',\r\n            });\r\n\r\n            if (result.ok) {\r\n                setFile(null);\r\n                setPreviewUrl(null); // Clear preview\r\n                toast.success(\"File deleted successfully!\");\r\n            } else {\r\n                const errorData = await result.json();\r\n                toast.error(`Failed to delete file: ${errorData.message || 'Unknown error'}`);\r\n                setFile({\r\n                    ...file,\r\n                    loading: false,\r\n                    failed: true,\r\n                    message: errorData.message || \"Failed to delete file\"\r\n                });\r\n            }\r\n        } catch (error) {\r\n            console.error(\"Delete file API error:\", error);\r\n            toast.error(\"Network error during file deletion.\");\r\n            setFile({\r\n                ...file,\r\n                loading: false,\r\n                failed: true,\r\n                message: \"Network error during deletion\"\r\n            });\r\n        }\r\n    }\r\n\r\n    function handleDragOver(e) {\r\n        e.preventDefault();\r\n        setIsDragging(true);\r\n    };\r\n\r\n    function handleDragLeave(e) {\r\n        e.preventDefault();\r\n        setIsDragging(false);\r\n    };\r\n\r\n    const cancelUpload = (id, fileName) => {\r\n        if (xhrRef.current) {\r\n            xhrRef.current.abort();\r\n            xhrRef.current = null\r\n            setUploadProgress(null);\r\n            setFile(null); // Clear the file on cancellation\r\n            setPreviewUrl(null); // Clear preview on cancellation\r\n            toast.info(`Upload of ${fileName} cancelled`);\r\n        }\r\n    };\r\n\r\n    // Corrected handleRetryUpload - had duplicate definition and missing newFile\r\n    function handleRetryUpload(fileWrapper) {\r\n        // fileWrapper should already contain the original File object\r\n        const newFileState = {\r\n            ...fileWrapper,\r\n            failed: false,\r\n            message: null,\r\n            // Ensure local_preview_url is maintained if it existed\r\n            // local_preview_url: fileWrapper.local_preview_url || (fileWrapper.file instanceof File ? URL.createObjectURL(fileWrapper.file) : null)\r\n        };\r\n        setFile(newFileState);\r\n        handleUpload(newFileState);\r\n    }\r\n\r\n    async function handleUpload(fileWrapper) {\r\n        const { file: actualFile, id } = fileWrapper; // Destructure the actual File object\r\n        const formData = new FormData();\r\n        formData.append('file', actualFile); // Use the actual File object\r\n        formData.append('details', JSON.stringify(fileUploadDetails))\r\n\r\n        // Set the file state immediately, including a local preview URL\r\n        const localUrl = URL.createObjectURL(actualFile);\r\n        setPreviewUrl(localUrl); // Set the preview URL for the component\r\n        setFile({ ...fileWrapper, local_preview_url: localUrl }); // Store it in the file state too\r\n\r\n\r\n        const xhr = new XMLHttpRequest();\r\n        xhrRef.current = xhr;\r\n\r\n        xhr.upload.addEventListener('progress', (event) => {\r\n            if (event.lengthComputable) {\r\n                const percentComplete = Math.round((event.loaded * 100) / event.total);\r\n                setUploadProgress({\r\n                    percentComplete,\r\n                    uploadedBytes: event.loaded,\r\n                })\r\n            }\r\n        });\r\n\r\n        xhr.addEventListener('error', () => {\r\n            toast.error(`Failed to upload ${fileRef.current?.file_name || actualFile.name}`);\r\n\r\n            setFile((prev) => ({\r\n                ...prev, // Use prev state to ensure we modify the latest\r\n                failed: true,\r\n                message: \"Failed to upload\",\r\n                loading: false, // Ensure loading is false on error\r\n            }));\r\n            setUploadProgress(null);\r\n            xhrRef.current = null;\r\n        });\r\n\r\n        xhr.addEventListener('load', () => {\r\n            if (xhr.status === 200) {\r\n                const response = JSON.parse(xhr.response);\r\n\r\n                console.log('Server response:', response);\r\n\r\n                const updatedFile = {\r\n                    ...file,\r\n                    id: response.data.id,\r\n                    file_path: response.data.file_path,\r\n                    file_cid: response.data.file_cid,\r\n                    file_type: response.data.file_type,\r\n                    file_name: response.data.file_name,\r\n                };\r\n\r\n                setFile(updatedFile);\r\n\r\n                // Update previewUrl to the server-hosted URL\r\n                setPreviewUrl(`${process.env.NEXT_PUBLIC_IPFS_GATEWAY}/${response.data.file_path}`);\r\n\r\n                toast.success(`${fileRef.current?.file_name || actualFile.name} uploaded successfully`);\r\n                setUploadProgress(null);\r\n            } else {\r\n                toast.error(`Failed to upload ${fileRef.current?.file_name || actualFile.name}`);\r\n\r\n                setFile((prev) => ({\r\n                    ...prev, // Use prev state\r\n                    failed: true,\r\n                    message: \"Failed to upload\",\r\n                    loading: false, // Ensure loading is false on error\r\n                }));\r\n\r\n                setUploadProgress(null);\r\n            }\r\n            xhrRef.current = null;\r\n        });\r\n\r\n        xhr.open('POST', '/api/v1/files');\r\n        xhr.send(formData);\r\n    }\r\n\r\n\r\n    return (\r\n        <div\r\n            className={cn(\r\n                className,\r\n                \"\"\r\n            )}\r\n        >\r\n            {!file ? ( // Show upload interface when no file is selected\r\n                <div>\r\n                    <div\r\n                        className={`relative border-2 border-dashed p-3 flex gap-4 items-end cursor-pointer rounded-md ${isDragging ? 'border-primary bg-primary/10' : 'border-border'}`}\r\n                        onDragOver={handleDragOver}\r\n                        onDragLeave={handleDragLeave}\r\n                        onDrop={handleDrop}\r\n                    >\r\n                        <CloudUpload size={20} className=\"text-muted-foreground\" />\r\n                        <div className='flex gap-1.5 text-foreground'>\r\n                            <p>Drag & drop or</p>\r\n                            <Input\r\n                                type=\"file\"\r\n                                onChange={handleFileChange}\r\n                                className=\"hidden\"\r\n                                id=\"file-upload\"\r\n                                accept={IMAGE_MIME_TYPES.join(',')} // Accept only image MIME types\r\n                            />\r\n                            <label htmlFor=\"file-upload\" className=\"cursor-pointer text-primary\">\r\n                                choose image\r\n                            </label>\r\n                        </div>\r\n                        <div className='mb-0.5 -ml-2'>\r\n                            <p className=\"text-foreground text-xs\">\r\n                                (Image size max 15 MB)\r\n                            </p>\r\n                        </div>\r\n                        <label className=\"absolute w-full h-full cursor-pointer\" htmlFor=\"file-upload\" />\r\n                    </div>\r\n                    {IMAGE_EXTENSIONS?.length > 0 && (\r\n                        <p className='text-xs text-stone-500 mt-1'>\r\n                            Allowed types: {IMAGE_EXTENSIONS.map(ext => `.${ext}`).join(', ')}\r\n                        </p>\r\n                    )}\r\n                </div>\r\n            ) : (\r\n                <FileItem // Renamed from FileItem to ImageFileItem in thoughts, but keeping FileItem for minimal changes\r\n                    key={file.id}\r\n                    file={file}\r\n                    uploadProgress={uploadProgress}\r\n                    cancelUpload={cancelUpload}\r\n                    handleRetryUpload={handleRetryUpload}\r\n                    deleteFile={deleteFile}\r\n                    previewUrl={previewUrl} // Pass the preview URL\r\n                />\r\n            )}\r\n\r\n        </div>\r\n    )\r\n}\r\n\r\nexport default SingleImageUpload // Renamed default export\r\n\r\nconst FileItem = memo(({ file, uploadProgress, cancelUpload, deleteFile, handleRetryUpload, previewUrl }) => {\r\n\r\n    const [fileSize, setFileSize] = useState(`${file.file_size} b`);\r\n    const [timeRemaining, setTimeRemaining] = useState(null); // Changed default to null for better control\r\n\r\n    const uploadStartTimeRef = useRef(null);\r\n    const prevUploadedBytesRef = useRef(0); // Renamed for clarity\r\n    const uploadTimeIntervalRef = useRef(null); // Renamed for clarity\r\n    const currentUploadProgressRef = useRef(uploadProgress); // Renamed for clarity\r\n\r\n    // Update refs with latest props\r\n    useEffect(() => {\r\n        currentUploadProgressRef.current = uploadProgress;\r\n\r\n        // Logic for starting/stopping time estimation based on uploadProgress\r\n        if (uploadProgress && !uploadStartTimeRef.current && uploadProgress.percentComplete < 100) {\r\n            startContinousTimeEstimation();\r\n        } else if (!uploadProgress && uploadTimeIntervalRef.current) { // Upload finished or cancelled\r\n            reinitialize();\r\n        }\r\n    }, [uploadProgress, file.file_size]);\r\n\r\n\r\n    // Format file size once on mount or when file_size changes\r\n    useEffect(() => {\r\n        const sizeInBytes = file.file_size;\r\n        if (sizeInBytes >= 1024 * 1024 * 1024) {\r\n            setFileSize(`${(sizeInBytes / (1024 * 1024 * 1024)).toFixed(2)} GB`);\r\n        } else if (sizeInBytes >= 1024 * 1024) {\r\n            setFileSize(`${(sizeInBytes / (1024 * 1024)).toFixed(2)} MB`);\r\n        } else if (sizeInBytes >= 1024) {\r\n            setFileSize(`${(sizeInBytes / 1024).toFixed(2)} KB`);\r\n        } else {\r\n            setFileSize(`${sizeInBytes} B`);\r\n        }\r\n    }, [file.file_size]);\r\n\r\n\r\n    const startContinousTimeEstimation = useCallback(() => {\r\n        if (uploadTimeIntervalRef.current) return; // Prevent multiple intervals\r\n\r\n        uploadStartTimeRef.current = Date.now();\r\n        prevUploadedBytesRef.current = currentUploadProgressRef.current?.uploadedBytes || 0; // Initialize with current progress\r\n\r\n        const intervalId = setInterval(() => {\r\n            const currentProgress = currentUploadProgressRef.current;\r\n\r\n            if (!currentProgress || currentProgress.percentComplete >= 100) {\r\n                reinitialize();\r\n                return;\r\n            }\r\n\r\n            const elapsedTime = Date.now() - uploadStartTimeRef.current; // Time since interval started\r\n            const totalBytes = file.file_size;\r\n            const uploadedBytes = currentProgress.uploadedBytes;\r\n\r\n            // Calculate speed based on recent progress\r\n            const bytesSinceLastInterval = uploadedBytes - prevUploadedBytesRef.current;\r\n            const timeSinceLastInterval = 1000; // The interval duration itself (1 second)\r\n\r\n            if (bytesSinceLastInterval <= 0 && elapsedTime > 3000) { // If no progress for 3 seconds\r\n                setTimeRemaining(null);\r\n                return;\r\n            }\r\n\r\n            const currentSpeed = bytesSinceLastInterval / (timeSinceLastInterval / 1000); // bytes per second\r\n            const remainingBytesToUpload = totalBytes - uploadedBytes;\r\n\r\n            if (currentSpeed > 0) { // Avoid division by zero\r\n                const estimatedTime = remainingBytesToUpload / currentSpeed; // seconds\r\n                setTimeRemaining(Math.round(estimatedTime));\r\n            } else {\r\n                setTimeRemaining(null); // No progress, can't estimate\r\n            }\r\n\r\n            prevUploadedBytesRef.current = uploadedBytes; // Update for next interval\r\n        }, 1000);\r\n\r\n        uploadTimeIntervalRef.current = intervalId;\r\n    }, [file.file_size]); // Dependency on file.file_size is important\r\n\r\n    const reinitialize = useCallback(() => {\r\n        console.log(\"reinitialize\");\r\n        if (uploadTimeIntervalRef.current) {\r\n            clearInterval(uploadTimeIntervalRef.current);\r\n            uploadTimeIntervalRef.current = null;\r\n        }\r\n        uploadStartTimeRef.current = null;\r\n        prevUploadedBytesRef.current = 0;\r\n        currentUploadProgressRef.current = null;\r\n        setTimeRemaining(null);\r\n    }, []);\r\n\r\n    // Cleanup interval on unmount\r\n    useEffect(() => {\r\n        return () => {\r\n            reinitialize();\r\n        };\r\n    }, [reinitialize]);\r\n\r\n\r\n    const formatTime = (seconds) => {\r\n        if (seconds === null || seconds < 0) return ''; // Handle null or negative seconds\r\n\r\n        const hrs = Math.floor(seconds / 3600);\r\n        const mins = Math.floor((seconds % 3600) / 60);\r\n        const secs = Math.floor(seconds % 60);\r\n\r\n        if (hrs > 0) {\r\n            return `${hrs}h ${mins}m`;\r\n        } else if (mins > 0) {\r\n            return `${mins}m ${secs}s`;\r\n        } else {\r\n            return `${secs}s`;\r\n        }\r\n    };\r\n\r\n    // Determine if the file is an image based on its type\r\n    const isImage = file?.file_type && IMAGE_MIME_TYPES.includes(file.file_type);\r\n\r\n    const FileIcon = memo(() => {\r\n        const ext = file?.file_name?.split(\".\")?.pop()?.toLowerCase();\r\n        const classname = \"w-6 h-6\";\r\n\r\n        // If it's an image and we have a preview URL, don't show generic file icon\r\n        if (isImage && previewUrl) return null;\r\n\r\n        switch (ext) {\r\n            case \"jpg\":\r\n            case \"jpeg\":\r\n            case \"png\":\r\n            case \"gif\":\r\n            case \"webp\":\r\n            case \"svg\":\r\n            case \"bmp\":\r\n            case \"tiff\":\r\n            case \"tif\":\r\n                return <FaFileImage className={classname} />;\r\n            default:\r\n                return <FaFileAlt className={classname} />;\r\n        }\r\n    });\r\n\r\n    return (\r\n        <div key={file.id} className=\"overflow-hidden ring-1 ring-border rounded-md px-4 py-2\">\r\n            <div className=\"flex items-center justify-between\">\r\n                <div className='flex items-center gap-3'>\r\n                    {/* Image Preview or File Icon */}\r\n                    {previewUrl ? (\r\n                        <img\r\n                            src={previewUrl}\r\n                            alt={file?.file_name}\r\n                            className=\"w-16 h-16 object-cover rounded-md flex-shrink-0\" // Adjust size as needed\r\n                        />\r\n                    ) : (\r\n                        <FileIcon />\r\n                    )}\r\n                    <div>\r\n                        <p className='font-medium '>\r\n                            {file?.file_name.substring(0, 5)}{file?.file_name.substring(file?.file_name.length - 5, file?.file_name.length)}\r\n                        </p>\r\n                        {!file?.failed ? (\r\n                            <p className='text-xs text-neutral-500'>\r\n                                {/* {fileSize} */}\r\n                                {((uploadProgress && uploadProgress.percentComplete !== 100) && ` | ${uploadProgress?.percentComplete}%`)}\r\n                                {((timeRemaining !== null && uploadProgress && uploadProgress.percentComplete < 100) && ` • ${formatTime(timeRemaining)} left`)}\r\n                            </p>\r\n                        ) : (\r\n                            <p className='text-xs text-destructive '>{file?.message ? file.message : 'Failed to upload file'}</p>\r\n                        )}\r\n                    </div>\r\n                </div>\r\n\r\n                {file?.loading ? (\r\n                    <LoaderCircleIcon className=\"animate-spin text-primary \" />\r\n                ) : uploadProgress && uploadProgress.percentComplete !== 100 ? (\r\n                    <button onClick={() => cancelUpload(file.id, file?.file_name)} className=\"cursor-pointer\">\r\n                        <X size={22} className=\"text-amber-700\" />\r\n                    </button>\r\n                ) : uploadProgress ? ( // When uploadProgress is not null but percentComplete is 100\r\n                    <TextShimmer className='font-mono text-sm' duration={1}>\r\n                        Processing...\r\n                    </TextShimmer>\r\n                ) : ( // When upload is complete or failed\r\n                    <div className=\"flex gap-4\">\r\n                        <button onClick={() => deleteFile(file.id)} className=\"cursor-pointer\">\r\n                            <TrashIcon size={22} className=\"text-amber-700\" />\r\n                        </button>\r\n                        {(file?.failed && file?.message === \"Failed to upload\") && (\r\n                            <button onClick={() => handleRetryUpload(file)} className=\"cursor-pointer\">\r\n                                <RefreshCcw size={22} className=\"text-amber-700\" />\r\n                            </button>\r\n                        )}\r\n                    </div>\r\n                )}\r\n\r\n            </div>\r\n            {uploadProgress && (uploadProgress.percentComplete > 0 && uploadProgress.percentComplete < 100) && ( // Show progress bar only during upload\r\n                <div className=\"w-full bg-input rounded-full h-1.5 mt-3\">\r\n                    <div\r\n                        className=\"bg-gradient-to-tr from-amber-400 to-amber-600 h-1.5 rounded-full transition-all duration-300\"\r\n                        style={{ width: `${uploadProgress?.percentComplete}%` }}\r\n                    />\r\n                </div>\r\n            )}\r\n        </div>\r\n    )\r\n})"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA,iGAAiG;AACjG;AACA;AATA;;;;;;;;;AAWA,iCAAiC;AACjC,MAAM,mBAAmB;IAAC;IAAO;IAAQ;IAAO;IAAO;IAAQ;IAAO;IAAO;IAAQ;CAAM;AAC3F,MAAM,mBAAmB;IAAC;IAAc;IAAa;IAAa;IAAc;IAAiB;IAAa;CAAa;AAE3H,SAAS,kBAAkB,EAAE,IAAI,EAAE,OAAO,EAAE,iBAAiB,EAAE,SAAS,EAAE;IAEtE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACtB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,OAAO,uDAAuD;IACrF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,8BAA8B;IAElF,sEAAsE;IACtE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,QAAQ,OAAO,GAAG;QAClB,IAAI,QAAQ,KAAK,SAAS,IAAI,iBAAiB,QAAQ,CAAC,KAAK,SAAS,GAAG;YACrE,0EAA0E;YAC1E,IAAI,KAAK,IAAI,YAAY,MAAM;gBAC1B,MAAM,MAAM,IAAI,eAAe,CAAC,KAAK,IAAI;gBACzC,cAAc;gBACd,OAAO,IAAM,IAAI,eAAe,CAAC,MAAM,eAAe;YAC3D,OAEK,IAAI,KAAK,SAAS,EAAE;gBACrB,cAAc,6FAAwC,CAAC,EAAE,KAAK,QAAQ,EAAE;YAC5E;QACJ,OAAO;YACH,cAAc,OAAO,2CAA2C;QACpE;IACJ,GAAG;QAAC;KAAK;IAGT,SAAS,iBAAiB,aAAa;QACnC,MAAM,YAAY,cAAc,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI;QACvD,MAAM,mBAAmB,iBAAiB,QAAQ,CAAC;QACnD,MAAM,kBAAkB,iBAAiB,QAAQ,CAAC,cAAc,IAAI;QAEpE,IAAI,CAAC,oBAAoB,CAAC,iBAAiB;YACvC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,UAAU,8BAA8B,CAAC;YACzD,OAAO;QACX;QAEA,IAAI,cAAc,IAAI,GAAG,OAAO,OAAO,IAAI;YACvC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,cAAc,IAAI,CAAC,0BAA0B,CAAC;YAClE,OAAO;QACX;QAEA,OAAO;IACX;IAEA,SAAS,iBAAiB,CAAC;QACvB,MAAM,gBAAgB,MAAM,IAAI,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;QAEnD,IAAI,CAAC,eAAe;YAChB,wIAAA,CAAA,QAAK,CAAC,IAAI,CAAC,CAAC,iBAAiB,CAAC;YAC9B;QACJ;QAEA,IAAI,CAAC,iBAAiB,gBAAgB;YAClC;QACJ;QAEA,MAAM,UAAU;YACZ,MAAM;YACN,IAAI,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;YAC5C,WAAW,cAAc,IAAI;YAC7B,WAAW,cAAc,IAAI;YAC7B,WAAW,cAAc,IAAI;QAEjC;QACA,aAAa;IACjB;IAEA,SAAS,WAAW,CAAC;QACjB,EAAE,cAAc;QAChB,cAAc;QAEd,MAAM,cAAc,MAAM,IAAI,CAAC,EAAE,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE;QAEvD,IAAI,CAAC,aAAa;YACd,wIAAA,CAAA,QAAK,CAAC,IAAI,CAAC,CAAC,iBAAiB,CAAC;YAC9B;QACJ;QAEA,IAAI,CAAC,iBAAiB,cAAc;YAChC;QACJ;QAEA,MAAM,UAAU;YACZ,MAAM;YACN,IAAI,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;YAC5C,WAAW,YAAY,IAAI;YAC3B,WAAW,YAAY,IAAI;YAC3B,WAAW,YAAY,IAAI;QAE/B;QACA,aAAa;IACjB;IAEA,eAAe;QACX,QAAQ,GAAG,CAAC,kBAAkB;QAC9B,QAAQ;YACJ,GAAG,IAAI;YACP,SAAS,KAAK,mCAAmC;QACrD;QAEA,0EAA0E;QAC1E,IAAI,CAAC,KAAK,SAAS,IAAI,KAAK,IAAI,YAAY,MAAM;YAC9C,QAAQ;YACR,cAAc,OAAO,gBAAgB;YACrC,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd;QACJ;QAEA,sDAAsD;QACtD,IAAI;YACA,MAAM,SAAS,MAAM,MAAM,CAAC,wBAAwB,EAAE,KAAK,SAAS,CAAC,SAAS,EAAE,KAAK,EAAE,CAAC,SAAS,EAAE,KAAK,SAAS,CAAC,oBAAoB,EAAE;gBACpI,QAAQ;YACZ;YAEA,IAAI,OAAO,EAAE,EAAE;gBACX,QAAQ;gBACR,cAAc,OAAO,gBAAgB;gBACrC,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAClB,OAAO;gBACH,MAAM,YAAY,MAAM,OAAO,IAAI;gBACnC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,uBAAuB,EAAE,UAAU,OAAO,IAAI,iBAAiB;gBAC5E,QAAQ;oBACJ,GAAG,IAAI;oBACP,SAAS;oBACT,QAAQ;oBACR,SAAS,UAAU,OAAO,IAAI;gBAClC;YACJ;QACJ,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,0BAA0B;YACxC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,QAAQ;gBACJ,GAAG,IAAI;gBACP,SAAS;gBACT,QAAQ;gBACR,SAAS;YACb;QACJ;IACJ;IAEA,SAAS,eAAe,CAAC;QACrB,EAAE,cAAc;QAChB,cAAc;IAClB;;IAEA,SAAS,gBAAgB,CAAC;QACtB,EAAE,cAAc;QAChB,cAAc;IAClB;;IAEA,MAAM,eAAe,CAAC,IAAI;QACtB,IAAI,OAAO,OAAO,EAAE;YAChB,OAAO,OAAO,CAAC,KAAK;YACpB,OAAO,OAAO,GAAG;YACjB,kBAAkB;YAClB,QAAQ,OAAO,iCAAiC;YAChD,cAAc,OAAO,gCAAgC;YACrD,wIAAA,CAAA,QAAK,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,SAAS,UAAU,CAAC;QAChD;IACJ;IAEA,6EAA6E;IAC7E,SAAS,kBAAkB,WAAW;QAClC,8DAA8D;QAC9D,MAAM,eAAe;YACjB,GAAG,WAAW;YACd,QAAQ;YACR,SAAS;QAGb;QACA,QAAQ;QACR,aAAa;IACjB;IAEA,eAAe,aAAa,WAAW;QACnC,MAAM,EAAE,MAAM,UAAU,EAAE,EAAE,EAAE,GAAG,aAAa,qCAAqC;QACnF,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ,aAAa,6BAA6B;QAClE,SAAS,MAAM,CAAC,WAAW,KAAK,SAAS,CAAC;QAE1C,gEAAgE;QAChE,MAAM,WAAW,IAAI,eAAe,CAAC;QACrC,cAAc,WAAW,wCAAwC;QACjE,QAAQ;YAAE,GAAG,WAAW;YAAE,mBAAmB;QAAS,IAAI,iCAAiC;QAG3F,MAAM,MAAM,IAAI;QAChB,OAAO,OAAO,GAAG;QAEjB,IAAI,MAAM,CAAC,gBAAgB,CAAC,YAAY,CAAC;YACrC,IAAI,MAAM,gBAAgB,EAAE;gBACxB,MAAM,kBAAkB,KAAK,KAAK,CAAC,AAAC,MAAM,MAAM,GAAG,MAAO,MAAM,KAAK;gBACrE,kBAAkB;oBACd;oBACA,eAAe,MAAM,MAAM;gBAC/B;YACJ;QACJ;QAEA,IAAI,gBAAgB,CAAC,SAAS;YAC1B,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,iBAAiB,EAAE,QAAQ,OAAO,EAAE,aAAa,WAAW,IAAI,EAAE;YAE/E,QAAQ,CAAC,OAAS,CAAC;oBACf,GAAG,IAAI;oBACP,QAAQ;oBACR,SAAS;oBACT,SAAS;gBACb,CAAC;YACD,kBAAkB;YAClB,OAAO,OAAO,GAAG;QACrB;QAEA,IAAI,gBAAgB,CAAC,QAAQ;YACzB,IAAI,IAAI,MAAM,KAAK,KAAK;gBACpB,MAAM,WAAW,KAAK,KAAK,CAAC,IAAI,QAAQ;gBAExC,QAAQ,GAAG,CAAC,oBAAoB;gBAEhC,MAAM,cAAc;oBAChB,GAAG,IAAI;oBACP,IAAI,SAAS,IAAI,CAAC,EAAE;oBACpB,WAAW,SAAS,IAAI,CAAC,SAAS;oBAClC,UAAU,SAAS,IAAI,CAAC,QAAQ;oBAChC,WAAW,SAAS,IAAI,CAAC,SAAS;oBAClC,WAAW,SAAS,IAAI,CAAC,SAAS;gBACtC;gBAEA,QAAQ;gBAER,6CAA6C;gBAC7C,cAAc,6FAAwC,CAAC,EAAE,SAAS,IAAI,CAAC,SAAS,EAAE;gBAElF,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,GAAG,QAAQ,OAAO,EAAE,aAAa,WAAW,IAAI,CAAC,sBAAsB,CAAC;gBACtF,kBAAkB;YACtB,OAAO;gBACH,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,iBAAiB,EAAE,QAAQ,OAAO,EAAE,aAAa,WAAW,IAAI,EAAE;gBAE/E,QAAQ,CAAC,OAAS,CAAC;wBACf,GAAG,IAAI;wBACP,QAAQ;wBACR,SAAS;wBACT,SAAS;oBACb,CAAC;gBAED,kBAAkB;YACtB;YACA,OAAO,OAAO,GAAG;QACrB;QAEA,IAAI,IAAI,CAAC,QAAQ;QACjB,IAAI,IAAI,CAAC;IACb;IAGA,qBACI,8OAAC;QACG,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACR,WACA;kBAGH,CAAC,qBACE,8OAAC;;8BACG,8OAAC;oBACG,WAAW,CAAC,mFAAmF,EAAE,aAAa,iCAAiC,iBAAiB;oBAChK,YAAY;oBACZ,aAAa;oBACb,QAAQ;;sCAER,8OAAC,oNAAA,CAAA,cAAW;4BAAC,MAAM;4BAAI,WAAU;;;;;;sCACjC,8OAAC;4BAAI,WAAU;;8CACX,8OAAC;8CAAE;;;;;;8CACH,8OAAC,iIAAA,CAAA,QAAK;oCACF,MAAK;oCACL,UAAU;oCACV,WAAU;oCACV,IAAG;oCACH,QAAQ,iBAAiB,IAAI,CAAC;;;;;;8CAElC,8OAAC;oCAAM,SAAQ;oCAAc,WAAU;8CAA8B;;;;;;;;;;;;sCAIzE,8OAAC;4BAAI,WAAU;sCACX,cAAA,8OAAC;gCAAE,WAAU;0CAA0B;;;;;;;;;;;sCAI3C,8OAAC;4BAAM,WAAU;4BAAwC,SAAQ;;;;;;;;;;;;gBAEpE,kBAAkB,SAAS,mBACxB,8OAAC;oBAAE,WAAU;;wBAA8B;wBACvB,iBAAiB,GAAG,CAAC,CAAA,MAAO,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;;;;;;;;;;;;iCAKxE,8OAAC,SAAS,+FAA+F;;YAErG,MAAM;YACN,gBAAgB;YAChB,cAAc;YACd,mBAAmB;YACnB,YAAY;YACZ,YAAY;WANP,KAAK,EAAE;;;;;;;;;;AAYhC;uCAEe,kBAAkB,yBAAyB;;AAE1D,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE,CAAC,EAAE,IAAI,EAAE,cAAc,EAAE,YAAY,EAAE,UAAU,EAAE,iBAAiB,EAAE,UAAU,EAAE;IAEpG,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,GAAG,KAAK,SAAS,CAAC,EAAE,CAAC;IAC9D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,6CAA6C;IAEvG,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAClC,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,IAAI,sBAAsB;IAC9D,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,OAAO,sBAAsB;IAClE,MAAM,2BAA2B,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,iBAAiB,sBAAsB;IAE/E,gCAAgC;IAChC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,yBAAyB,OAAO,GAAG;QAEnC,sEAAsE;QACtE,IAAI,kBAAkB,CAAC,mBAAmB,OAAO,IAAI,eAAe,eAAe,GAAG,KAAK;YACvF;QACJ,OAAO,IAAI,CAAC,kBAAkB,sBAAsB,OAAO,EAAE;YACzD;QACJ;IACJ,GAAG;QAAC;QAAgB,KAAK,SAAS;KAAC;IAGnC,2DAA2D;IAC3D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,cAAc,KAAK,SAAS;QAClC,IAAI,eAAe,OAAO,OAAO,MAAM;YACnC,YAAY,GAAG,CAAC,cAAc,CAAC,OAAO,OAAO,IAAI,CAAC,EAAE,OAAO,CAAC,GAAG,GAAG,CAAC;QACvE,OAAO,IAAI,eAAe,OAAO,MAAM;YACnC,YAAY,GAAG,CAAC,cAAc,CAAC,OAAO,IAAI,CAAC,EAAE,OAAO,CAAC,GAAG,GAAG,CAAC;QAChE,OAAO,IAAI,eAAe,MAAM;YAC5B,YAAY,GAAG,CAAC,cAAc,IAAI,EAAE,OAAO,CAAC,GAAG,GAAG,CAAC;QACvD,OAAO;YACH,YAAY,GAAG,YAAY,EAAE,CAAC;QAClC;IACJ,GAAG;QAAC,KAAK,SAAS;KAAC;IAGnB,MAAM,+BAA+B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7C,IAAI,sBAAsB,OAAO,EAAE,QAAQ,6BAA6B;QAExE,mBAAmB,OAAO,GAAG,KAAK,GAAG;QACrC,qBAAqB,OAAO,GAAG,yBAAyB,OAAO,EAAE,iBAAiB,GAAG,mCAAmC;QAExH,MAAM,aAAa,YAAY;YAC3B,MAAM,kBAAkB,yBAAyB,OAAO;YAExD,IAAI,CAAC,mBAAmB,gBAAgB,eAAe,IAAI,KAAK;gBAC5D;gBACA;YACJ;YAEA,MAAM,cAAc,KAAK,GAAG,KAAK,mBAAmB,OAAO,EAAE,8BAA8B;YAC3F,MAAM,aAAa,KAAK,SAAS;YACjC,MAAM,gBAAgB,gBAAgB,aAAa;YAEnD,2CAA2C;YAC3C,MAAM,yBAAyB,gBAAgB,qBAAqB,OAAO;YAC3E,MAAM,wBAAwB,MAAM,0CAA0C;YAE9E,IAAI,0BAA0B,KAAK,cAAc,MAAM;gBACnD,iBAAiB;gBACjB;YACJ;YAEA,MAAM,eAAe,yBAAyB,CAAC,wBAAwB,IAAI,GAAG,mBAAmB;YACjG,MAAM,yBAAyB,aAAa;YAE5C,IAAI,eAAe,GAAG;gBAClB,MAAM,gBAAgB,yBAAyB,cAAc,UAAU;gBACvE,iBAAiB,KAAK,KAAK,CAAC;YAChC,OAAO;gBACH,iBAAiB,OAAO,8BAA8B;YAC1D;YAEA,qBAAqB,OAAO,GAAG,eAAe,2BAA2B;QAC7E,GAAG;QAEH,sBAAsB,OAAO,GAAG;IACpC,GAAG;QAAC,KAAK,SAAS;KAAC,GAAG,4CAA4C;IAElE,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,QAAQ,GAAG,CAAC;QACZ,IAAI,sBAAsB,OAAO,EAAE;YAC/B,cAAc,sBAAsB,OAAO;YAC3C,sBAAsB,OAAO,GAAG;QACpC;QACA,mBAAmB,OAAO,GAAG;QAC7B,qBAAqB,OAAO,GAAG;QAC/B,yBAAyB,OAAO,GAAG;QACnC,iBAAiB;IACrB,GAAG,EAAE;IAEL,8BAA8B;IAC9B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,OAAO;YACH;QACJ;IACJ,GAAG;QAAC;KAAa;IAGjB,MAAM,aAAa,CAAC;QAChB,IAAI,YAAY,QAAQ,UAAU,GAAG,OAAO,IAAI,kCAAkC;QAElF,MAAM,MAAM,KAAK,KAAK,CAAC,UAAU;QACjC,MAAM,OAAO,KAAK,KAAK,CAAC,AAAC,UAAU,OAAQ;QAC3C,MAAM,OAAO,KAAK,KAAK,CAAC,UAAU;QAElC,IAAI,MAAM,GAAG;YACT,OAAO,GAAG,IAAI,EAAE,EAAE,KAAK,CAAC,CAAC;QAC7B,OAAO,IAAI,OAAO,GAAG;YACjB,OAAO,GAAG,KAAK,EAAE,EAAE,KAAK,CAAC,CAAC;QAC9B,OAAO;YACH,OAAO,GAAG,KAAK,CAAC,CAAC;QACrB;IACJ;IAEA,sDAAsD;IACtD,MAAM,UAAU,MAAM,aAAa,iBAAiB,QAAQ,CAAC,KAAK,SAAS;IAE3E,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE;QAClB,MAAM,MAAM,MAAM,WAAW,MAAM,MAAM,OAAO;QAChD,MAAM,YAAY;QAElB,2EAA2E;QAC3E,IAAI,WAAW,YAAY,OAAO;QAElC,OAAQ;YACJ,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACD,qBAAO,8OAAC,8IAAA,CAAA,cAAW;oBAAC,WAAW;;;;;;YACnC;gBACI,qBAAO,8OAAC,8IAAA,CAAA,YAAS;oBAAC,WAAW;;;;;;QACrC;IACJ;IAEA,qBACI,8OAAC;QAAkB,WAAU;;0BACzB,8OAAC;gBAAI,WAAU;;kCACX,8OAAC;wBAAI,WAAU;;4BAEV,2BACG,8OAAC;gCACG,KAAK;gCACL,KAAK,MAAM;gCACX,WAAU,kDAAkD,wBAAwB;;;;;qDAGxF,8OAAC;;;;;0CAEL,8OAAC;;kDACG,8OAAC;wCAAE,WAAU;;4CACR,MAAM,UAAU,UAAU,GAAG;4CAAI,MAAM,UAAU,UAAU,MAAM,UAAU,SAAS,GAAG,MAAM,UAAU;;;;;;;oCAE3G,CAAC,MAAM,uBACJ,8OAAC;wCAAE,WAAU;;4CAEN,kBAAkB,eAAe,eAAe,KAAK,OAAQ,CAAC,GAAG,EAAE,gBAAgB,gBAAgB,CAAC,CAAC;4CACrG,kBAAkB,QAAQ,kBAAkB,eAAe,eAAe,GAAG,OAAQ,CAAC,GAAG,EAAE,WAAW,eAAe,KAAK,CAAC;;;;;;6DAGlI,8OAAC;wCAAE,WAAU;kDAA6B,MAAM,UAAU,KAAK,OAAO,GAAG;;;;;;;;;;;;;;;;;;oBAKpF,MAAM,wBACH,8OAAC,0NAAA,CAAA,mBAAgB;wBAAC,WAAU;;;;;+BAC5B,kBAAkB,eAAe,eAAe,KAAK,oBACrD,8OAAC;wBAAO,SAAS,IAAM,aAAa,KAAK,EAAE,EAAE,MAAM;wBAAY,WAAU;kCACrE,cAAA,8OAAC,4LAAA,CAAA,IAAC;4BAAC,MAAM;4BAAI,WAAU;;;;;;;;;;+BAE3B,+BACA,8OAAC,2IAAA,CAAA,cAAW;wBAAC,WAAU;wBAAoB,UAAU;kCAAG;;;;;6CAIxD,8OAAC;wBAAI,WAAU;;0CACX,8OAAC;gCAAO,SAAS,IAAM,WAAW,KAAK,EAAE;gCAAG,WAAU;0CAClD,cAAA,8OAAC,wMAAA,CAAA,YAAS;oCAAC,MAAM;oCAAI,WAAU;;;;;;;;;;;4BAEjC,MAAM,UAAU,MAAM,YAAY,oCAChC,8OAAC;gCAAO,SAAS,IAAM,kBAAkB;gCAAO,WAAU;0CACtD,cAAA,8OAAC,kNAAA,CAAA,aAAU;oCAAC,MAAM;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;YAOnD,kBAAmB,eAAe,eAAe,GAAG,KAAK,eAAe,eAAe,GAAG,qBACvF,8OAAC;gBAAI,WAAU;0BACX,cAAA,8OAAC;oBACG,WAAU;oBACV,OAAO;wBAAE,OAAO,GAAG,gBAAgB,gBAAgB,CAAC,CAAC;oBAAC;;;;;;;;;;;;OAzD5D,KAAK,EAAE;;;;;AA+DzB", "debugId": null}}, {"offset": {"line": 746, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/js/web3/packages/chario/src/components/ui/textarea.jsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Textarea({\r\n  className,\r\n  ...props\r\n}) {\r\n  return (\r\n    (<textarea\r\n      data-slot=\"textarea\"\r\n      className={cn(\r\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        className\r\n      )}\r\n      {...props} />)\r\n  );\r\n}\r\n\r\nexport { Textarea }\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACJ;IACC,qBACG,8OAAC;QACA,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAEf", "debugId": null}}, {"offset": {"line": 773, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/js/web3/packages/chario/src/components/md/forms/create-charity-form.jsx"], "sourcesContent": ["'use client'\r\n\r\nimport { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'\r\nimport { LoaderCircle } from 'lucide-react';\r\nimport { cn } from '@/lib/utils';\r\nimport React from 'react'\r\nimport { Button } from '@/components/ui/button';\r\nimport SingleImageUpload from '../single-image-upload';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Textarea } from '@/components/ui/textarea';\r\n\r\nfunction CreateCharityFormComponent({ form, onSubmit, className }) {\r\n    return (\r\n        <div className={className}>\r\n            <Form {...form}>\r\n                <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-6\">\r\n                    <FormField\r\n                        control={form.control}\r\n                        name=\"title\"\r\n                        render={({ field }) => (\r\n                            <FormItem>\r\n                                <FormLabel>Title<span className=\"text-red-300 -ml-1\">*</span></FormLabel>\r\n                                <FormControl>\r\n                                    <Input\r\n                                        placeholder='Title'\r\n                                        value={field.value}\r\n                                        onChange={(e) => {\r\n                                            field.onChange(e.target.value)\r\n                                        }}\r\n                                    />\r\n                                </FormControl>\r\n                                <FormMessage />\r\n                            </FormItem>\r\n                        )}\r\n                    />\r\n                    <FormField\r\n                        control={form.control}\r\n                        name=\"description\"\r\n                        render={({ field }) => (\r\n                            <FormItem>\r\n                                <FormLabel>Description<span className=\"text-red-300 -ml-1\">*</span></FormLabel>\r\n                                <FormControl>\r\n                                    <Textarea\r\n                                        placeholder='Tell what your charity is about'\r\n                                        value={field.value}\r\n                                        onChange={(e) => {\r\n                                            field.onChange(e.target.value)\r\n                                        }}\r\n                                    />\r\n                                </FormControl>\r\n                                <FormMessage />\r\n                            </FormItem>\r\n                        )}\r\n                    />\r\n                    <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>\r\n                        <FormField\r\n                            control={form.control}\r\n                            name=\"target\"\r\n                            render={({ field }) => (\r\n                                <FormItem>\r\n                                    <FormLabel>Target Amount (ETH)</FormLabel>\r\n                                    <FormControl>\r\n                                        <Input\r\n                                            placeholder='0.1'\r\n                                            value={field.value}\r\n                                            type={'number'}\r\n                                            onChange={(e) => {\r\n                                                field.onChange(e.target.value)\r\n                                            }}\r\n                                        />\r\n                                    </FormControl>\r\n                                    <FormMessage />\r\n                                </FormItem>\r\n                            )}\r\n                        />\r\n                        <FormField\r\n                            control={form.control}\r\n                            name=\"deadline\"\r\n                            render={({ field }) => (\r\n                                <FormItem>\r\n                                    <FormLabel>Deadline</FormLabel>\r\n                                    <FormControl>\r\n                                        <Input\r\n                                            type={'date'}\r\n                                            value={field.value}\r\n                                            onChange={(e) => {\r\n                                                field.onChange(e.target.value)\r\n                                            }}\r\n                                            min={new Date(Date.now() + 3600000).toISOString().split('T')[0]}\r\n                                        />\r\n                                    </FormControl>\r\n                                    <FormMessage />\r\n                                </FormItem>\r\n                            )}\r\n                        />\r\n                    </div>\r\n                    <FormField\r\n                        control={form.control}\r\n                        name=\"image\"\r\n                        render={({ field }) => (\r\n                            <FormItem>\r\n                                <FormLabel>Image<span className=\"text-red-300 -ml-1\">*</span></FormLabel>\r\n                                <FormControl>\r\n                                    <SingleImageUpload\r\n                                        className='mt-2'\r\n                                        file={field.value}\r\n                                        setFile={(file) => {\r\n                                            field.onChange(file)\r\n                                        }}\r\n                                    />\r\n                                </FormControl>\r\n                                <FormMessage />\r\n                            </FormItem>\r\n                        )}\r\n                    />\r\n                    <div className=\"flex justify-end\">\r\n                        <Button\r\n                            type=\"submit\"\r\n                            disabled={form.formState.isSubmitting}\r\n                            className={cn(\r\n                                \"cursor-pointer\",\r\n                                form.formState.isSubmitting && \"cursor-default opacity-50\"\r\n                            )}\r\n                        >\r\n                            {form.formState.isSubmitting ? (\r\n                                <LoaderCircle className=\"h-4 w-4 animate-spin\" />\r\n                            ) : (\r\n                                \"Continue\"\r\n                            )}\r\n                        </Button>\r\n                    </div>\r\n                </form>\r\n            </Form>\r\n        </div>\r\n    )\r\n}\r\n\r\nexport default CreateCharityFormComponent"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAWA,SAAS,2BAA2B,EAAE,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE;IAC7D,qBACI,8OAAC;QAAI,WAAW;kBACZ,cAAA,8OAAC,gIAAA,CAAA,OAAI;YAAE,GAAG,IAAI;sBACV,cAAA,8OAAC;gBAAK,UAAU,KAAK,YAAY,CAAC;gBAAW,WAAU;;kCACnD,8OAAC,gIAAA,CAAA,YAAS;wBACN,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBACd,8OAAC,gIAAA,CAAA,WAAQ;;kDACL,8OAAC,gIAAA,CAAA,YAAS;;4CAAC;0DAAK,8OAAC;gDAAK,WAAU;0DAAqB;;;;;;;;;;;;kDACrD,8OAAC,gIAAA,CAAA,cAAW;kDACR,cAAA,8OAAC,iIAAA,CAAA,QAAK;4CACF,aAAY;4CACZ,OAAO,MAAM,KAAK;4CAClB,UAAU,CAAC;gDACP,MAAM,QAAQ,CAAC,EAAE,MAAM,CAAC,KAAK;4CACjC;;;;;;;;;;;kDAGR,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;kCAIxB,8OAAC,gIAAA,CAAA,YAAS;wBACN,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBACd,8OAAC,gIAAA,CAAA,WAAQ;;kDACL,8OAAC,gIAAA,CAAA,YAAS;;4CAAC;0DAAW,8OAAC;gDAAK,WAAU;0DAAqB;;;;;;;;;;;;kDAC3D,8OAAC,gIAAA,CAAA,cAAW;kDACR,cAAA,8OAAC,oIAAA,CAAA,WAAQ;4CACL,aAAY;4CACZ,OAAO,MAAM,KAAK;4CAClB,UAAU,CAAC;gDACP,MAAM,QAAQ,CAAC,EAAE,MAAM,CAAC,KAAK;4CACjC;;;;;;;;;;;kDAGR,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;kCAIxB,8OAAC;wBAAI,WAAU;;0CACX,8OAAC,gIAAA,CAAA,YAAS;gCACN,SAAS,KAAK,OAAO;gCACrB,MAAK;gCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBACd,8OAAC,gIAAA,CAAA,WAAQ;;0DACL,8OAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,gIAAA,CAAA,cAAW;0DACR,cAAA,8OAAC,iIAAA,CAAA,QAAK;oDACF,aAAY;oDACZ,OAAO,MAAM,KAAK;oDAClB,MAAM;oDACN,UAAU,CAAC;wDACP,MAAM,QAAQ,CAAC,EAAE,MAAM,CAAC,KAAK;oDACjC;;;;;;;;;;;0DAGR,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;0CAIxB,8OAAC,gIAAA,CAAA,YAAS;gCACN,SAAS,KAAK,OAAO;gCACrB,MAAK;gCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBACd,8OAAC,gIAAA,CAAA,WAAQ;;0DACL,8OAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,gIAAA,CAAA,cAAW;0DACR,cAAA,8OAAC,iIAAA,CAAA,QAAK;oDACF,MAAM;oDACN,OAAO,MAAM,KAAK;oDAClB,UAAU,CAAC;wDACP,MAAM,QAAQ,CAAC,EAAE,MAAM,CAAC,KAAK;oDACjC;oDACA,KAAK,IAAI,KAAK,KAAK,GAAG,KAAK,SAAS,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;;;;;;;;;;;0DAGvE,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;kCAK5B,8OAAC,gIAAA,CAAA,YAAS;wBACN,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBACd,8OAAC,gIAAA,CAAA,WAAQ;;kDACL,8OAAC,gIAAA,CAAA,YAAS;;4CAAC;0DAAK,8OAAC;gDAAK,WAAU;0DAAqB;;;;;;;;;;;;kDACrD,8OAAC,gIAAA,CAAA,cAAW;kDACR,cAAA,8OAAC,qJAAA,CAAA,UAAiB;4CACd,WAAU;4CACV,MAAM,MAAM,KAAK;4CACjB,SAAS,CAAC;gDACN,MAAM,QAAQ,CAAC;4CACnB;;;;;;;;;;;kDAGR,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;kCAIxB,8OAAC;wBAAI,WAAU;kCACX,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BACH,MAAK;4BACL,UAAU,KAAK,SAAS,CAAC,YAAY;4BACrC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACR,kBACA,KAAK,SAAS,CAAC,YAAY,IAAI;sCAGlC,KAAK,SAAS,CAAC,YAAY,iBACxB,8OAAC,sNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;uCAExB;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhC;uCAEe", "debugId": null}}, {"offset": {"line": 1117, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/js/web3/packages/chario/src/helpers/zod/charity-schema.js"], "sourcesContent": ["import { z } from \"zod\";\r\n\r\nexport const createCharitySchema = z.object({\r\n    title: z.string().min(1).max(100),\r\n    description: z.string().min(1).max(1000),\r\n    target: z.string().max(100).optional(),\r\n    deadline: z.string().max(100).optional(),\r\n    image: z.object({\r\n        file_name: z.string(),\r\n        file_cid: z.string(),\r\n        file_path: z.string(),\r\n    }),\r\n})\r\n\r\nexport const editCharitySchema = z.object({\r\n    title: z.string().min(1).max(100),\r\n    description: z.string().min(1).max(1000),\r\n    target: z.string().max(100).optional(),\r\n    deadline: z.string().max(100).optional(),\r\n    image: z.object({\r\n        file_name: z.string(),\r\n        file_cid: z.string(),\r\n        file_path: z.string(),\r\n    }),\r\n})"], "names": [], "mappings": ";;;;AAAA;;AAEO,MAAM,sBAAsB,kKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACxC,OAAO,kKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;IAC7B,aAAa,kKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;IACnC,QAAQ,kKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,QAAQ;IACpC,UAAU,kKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,QAAQ;IACtC,OAAO,kKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACZ,WAAW,kKAAA,CAAA,IAAC,CAAC,MAAM;QACnB,UAAU,kKAAA,CAAA,IAAC,CAAC,MAAM;QAClB,WAAW,kKAAA,CAAA,IAAC,CAAC,MAAM;IACvB;AACJ;AAEO,MAAM,oBAAoB,kKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACtC,OAAO,kKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;IAC7B,aAAa,kKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;IACnC,QAAQ,kKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,QAAQ;IACpC,UAAU,kKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,QAAQ;IACtC,OAAO,kKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACZ,WAAW,kKAAA,CAAA,IAAC,CAAC,MAAM;QACnB,UAAU,kKAAA,CAAA,IAAC,CAAC,MAAM;QAClB,WAAW,kKAAA,CAAA,IAAC,CAAC,MAAM;IACvB;AACJ", "debugId": null}}, {"offset": {"line": 1151, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/js/web3/packages/chario/src/components/ui/badge.jsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst badgeVariants = cva(\r\n    \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\r\n    {\r\n        variants: {\r\n            variant: {\r\n                default:\r\n                    \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\r\n                secondary:\r\n                    \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\r\n                destructive:\r\n                    \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n                outline:\r\n                    \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\r\n            },\r\n        },\r\n        defaultVariants: {\r\n            variant: \"default\",\r\n        },\r\n    }\r\n)\r\n\r\nfunction Badge({\r\n    className,\r\n    variant,\r\n    asChild = false,\r\n    ...props\r\n}) {\r\n    const Comp = asChild ? Slot : \"span\"\r\n\r\n    return (\r\n        <Comp\r\n            data-slot=\"badge\"\r\n            className={cn(badgeVariants({ variant }), className)}\r\n            {...props}\r\n        />\r\n    )\r\n}\r\n\r\nexport { Badge, badgeVariants }\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACpB,kZACA;IACI,UAAU;QACN,SAAS;YACL,SACI;YACJ,WACI;YACJ,aACI;YACJ,SACI;QACR;IACJ;IACA,iBAAiB;QACb,SAAS;IACb;AACJ;AAGJ,SAAS,MAAM,EACX,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OACN;IACG,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACI,8OAAC;QACG,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGrB", "debugId": null}}, {"offset": {"line": 1199, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/js/web3/packages/chario/src/components/ui/progress.jsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Progress({\r\n  className,\r\n  value,\r\n  ...props\r\n}) {\r\n  return (\r\n    (<ProgressPrimitive.Root\r\n      data-slot=\"progress\"\r\n      className={cn(\r\n        \"bg-primary/20 relative h-1 max-h-1 w-full overflow-hidden rounded-full\",\r\n        className\r\n      )}\r\n      {...props}>\r\n      <ProgressPrimitive.Indicator\r\n        data-slot=\"progress-indicator\"\r\n        className=\"bg-emerald-500 dark:bg-emerald-600 h-full w-full flex-1 transition-all\"\r\n        style={{ transform: `translateX(-${100 - (value || 0)}%)` }} />\r\n    </ProgressPrimitive.Root>)\r\n  );\r\n}\r\n\r\nexport { Progress }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,SAAS,SAAS,EAChB,SAAS,EACT,KAAK,EACL,GAAG,OACJ;IACC,qBACG,8OAAC,oKAAA,CAAA,OAAsB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0EACA;QAED,GAAG,KAAK;kBACT,cAAA,8OAAC,oKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAGlE", "debugId": null}}, {"offset": {"line": 1240, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/js/web3/packages/chario/src/components/md/charity-card-md.jsx"], "sourcesContent": ["'use client'\r\nimport React from 'react'\r\nimport { Badge } from '../ui/badge';\r\nimport { Progress } from '../ui/progress';\r\nimport { cn } from '@/lib/utils';\r\nimport Image from 'next/image';\r\nimport Link from 'next/link';\r\n\r\nfunction CharityCardMd({ charity, className }) {\r\n    const [daysLeft, setDaysLeft] = React.useState(0);\r\n\r\n    React.useEffect(() => {\r\n        const newDaysLeft = Math.floor((Number(charity.deadline) - Date.now() / 1000) / 86400);\r\n        setDaysLeft(newDaysLeft);\r\n        console.log(charity.deadline, newDaysLeft);\r\n    }, [charity.deadline]);\r\n\r\n    return (\r\n        <Link\r\n            href={charity?.link ? charity.link : `/home/<USER>/${charity.id}`}\r\n            className={cn(\r\n                'overflow-hidden relative min-w-96 max-w-96 min-h-[24rem] bg-muted dark:bg-card rounded-3xl ring-1 ring-card-foreground/10 transition-all duration-200 hover:ring-2 hover:ring-card-foreground/10 cursor-pointer flex flex-col',\r\n                className\r\n            )}\r\n        >\r\n            <div className=\"relative p-1\">\r\n                {charity?.image ? (\r\n                    <Image\r\n                        width={384}\r\n                        height={192}\r\n                        src={charity.image}\r\n                        alt={charity.title}\r\n                        className=\"w-full h-48 object-cover rounded-t-3xl rounded-b-4xl\"\r\n                    />\r\n                ) : (\r\n                    <div className=\"w-full h-48 bg-accent rounded-t-3xl rounded-b-4xl flex items-center justify-center\">\r\n                        <span className=\"text-zinc-400\">Image preview</span>\r\n                    </div>\r\n                )}\r\n                {daysLeft > 0 && (\r\n                    <Badge className=\"absolute top-4 right-4 bg-primary-foreground text-white font-semibold px-2 ring ring-primary/50 shadow-xl\">\r\n                        {daysLeft}d left\r\n                    </Badge>\r\n                )}\r\n            </div>\r\n            <div className=\"p-4 pt-1 flex flex-col flex-grow\">\r\n                <h3 className='text-xl font-semibold text-accent-foreground mb-2 line-clamp-1'>{charity.title}</h3>\r\n                <p className='text-muted-foreground mb-6 line-clamp-4 text-sm'>{charity.description}</p>\r\n                {/* {(charity.target !== '0' || !charity?.id) ? ( */}\r\n                {(charity.target !== '0' && charity.target) ? (\r\n                    <div className=\"flex flex-col gap-1 mt-auto\">\r\n                        <div className='flex justify-between text-xs'>\r\n                            <span className='text-zinc-400'>Target</span>\r\n                            <span className='text-primary font-sm'>\r\n                                {charity.target} ETH\r\n                            </span>\r\n                        </div>\r\n                        <Progress\r\n                            value={(charity.amountCollected / charity.target) * 100}\r\n                            className='h-2 bg-zinc-700'\r\n                        />\r\n                        <div className='flex justify-between text-xs'>\r\n                            <span className='text-zinc-400'>Raised</span>\r\n                            <span className=' font-sm text-emerald-400'>\r\n                                {charity.amountCollected} ETH\r\n                            </span>\r\n                        </div>\r\n                    </div>\r\n                ) : (\r\n                    <div className='flex justify-between text-xs mt-auto'>\r\n                        <span className='text-zinc-400'>Raised</span>\r\n                        <span className='text-emerald-400 font-sm'>\r\n                            {charity.amountCollected} ETH\r\n                        </span>\r\n                    </div>\r\n                )}\r\n            </div>\r\n        </Link>\r\n    )\r\n}\r\n\r\nexport default CharityCardMd;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AANA;;;;;;;;AAQA,SAAS,cAAc,EAAE,OAAO,EAAE,SAAS,EAAE;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAE/C,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACZ,MAAM,cAAc,KAAK,KAAK,CAAC,CAAC,OAAO,QAAQ,QAAQ,IAAI,KAAK,GAAG,KAAK,IAAI,IAAI;QAChF,YAAY;QACZ,QAAQ,GAAG,CAAC,QAAQ,QAAQ,EAAE;IAClC,GAAG;QAAC,QAAQ,QAAQ;KAAC;IAErB,qBACI,8OAAC,4JAAA,CAAA,UAAI;QACD,MAAM,SAAS,OAAO,QAAQ,IAAI,GAAG,CAAC,gBAAgB,EAAE,QAAQ,EAAE,EAAE;QACpE,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACR,iOACA;;0BAGJ,8OAAC;gBAAI,WAAU;;oBACV,SAAS,sBACN,8OAAC,6HAAA,CAAA,UAAK;wBACF,OAAO;wBACP,QAAQ;wBACR,KAAK,QAAQ,KAAK;wBAClB,KAAK,QAAQ,KAAK;wBAClB,WAAU;;;;;6CAGd,8OAAC;wBAAI,WAAU;kCACX,cAAA,8OAAC;4BAAK,WAAU;sCAAgB;;;;;;;;;;;oBAGvC,WAAW,mBACR,8OAAC,iIAAA,CAAA,QAAK;wBAAC,WAAU;;4BACZ;4BAAS;;;;;;;;;;;;;0BAItB,8OAAC;gBAAI,WAAU;;kCACX,8OAAC;wBAAG,WAAU;kCAAkE,QAAQ,KAAK;;;;;;kCAC7F,8OAAC;wBAAE,WAAU;kCAAmD,QAAQ,WAAW;;;;;;oBAEjF,QAAQ,MAAM,KAAK,OAAO,QAAQ,MAAM,iBACtC,8OAAC;wBAAI,WAAU;;0CACX,8OAAC;gCAAI,WAAU;;kDACX,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,8OAAC;wCAAK,WAAU;;4CACX,QAAQ,MAAM;4CAAC;;;;;;;;;;;;;0CAGxB,8OAAC,oIAAA,CAAA,WAAQ;gCACL,OAAO,AAAC,QAAQ,eAAe,GAAG,QAAQ,MAAM,GAAI;gCACpD,WAAU;;;;;;0CAEd,8OAAC;gCAAI,WAAU;;kDACX,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,8OAAC;wCAAK,WAAU;;4CACX,QAAQ,eAAe;4CAAC;;;;;;;;;;;;;;;;;;6CAKrC,8OAAC;wBAAI,WAAU;;0CACX,8OAAC;gCAAK,WAAU;0CAAgB;;;;;;0CAChC,8OAAC;gCAAK,WAAU;;oCACX,QAAQ,eAAe;oCAAC;;;;;;;;;;;;;;;;;;;;;;;;;AAOrD;uCAEe", "debugId": null}}, {"offset": {"line": 1460, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/js/web3/packages/chario/src/hooks/use-chario.js"], "sourcesContent": ["// \"use client\";\r\n\r\n// import { useState, useCallback, useEffect } from 'react';\r\n// import {\r\n//     useAccount,\r\n//     useReadContract,\r\n//     useWriteContract,\r\n//     useWaitForTransactionReceipt,\r\n// } from \"wagmi\";\r\n// import abi from \"@/abi/chario.json\"; // Update to your contract's ABI\r\n// import { parseEther, formatEther } from 'viem';\r\n// import { toast } from \"sonner\";\r\n\r\n// const CHARIO_ABI = abi\r\n// const CONTRACT_ADDRESS = process.env.NEXT_PUBLIC_CONTRACT_ADDRESS\r\n\r\n// export const useChario = () => {\r\n//     const { address, isConnected } = useAccount();\r\n//     const [isLoading, setIsLoading] = useState(false);\r\n//     const [pendingTxHash, setPendingTxHash] = useState(null);\r\n\r\n//     // Write contract hook\r\n//     const { writeContract, data: txHash, error: writeError, isPending: isWritePending } = useWriteContract();\r\n\r\n//     // Wait for transaction receipt\r\n//     const { isLoading: isConfirming, isSuccess: isConfirmed } = useWaitForTransactionReceipt({\r\n//         hash: txHash,\r\n//     });\r\n\r\n//     // Handle transaction completion\r\n//     useEffect(() => {\r\n//         if (txHash) {\r\n//             console.log('Transaction Hash:', txHash);\r\n//         }\r\n//         if (isConfirmed && pendingTxHash) {\r\n//             toast.success('Transaction confirmed!');\r\n//             setIsLoading(false);\r\n//             setPendingTxHash(null);\r\n//         }\r\n//     }, [isConfirmed, pendingTxHash, txHash]); // Add txHash to dependency array\r\n\r\n//     useEffect(() => {\r\n//         if (writeError) {\r\n//             console.error('Wagmi Write Error:', writeError);\r\n//             toast.error('Transaction failed: ' + writeError.message);\r\n//             setIsLoading(false);\r\n//             setPendingTxHash(null);\r\n//         }\r\n//     }, [writeError]);\r\n\r\n//     // Read contract data\r\n//     const { data: numberOfCharities, refetch: refetchCharityCount } = useReadContract({\r\n//         address: CONTRACT_ADDRESS,\r\n//         abi: CHARIO_ABI,\r\n//         functionName: 'numberOfCharities',\r\n//     });\r\n\r\n//     const { data: userData, refetch: refetchUserData } = useReadContract({\r\n//         address: CONTRACT_ADDRESS,\r\n//         abi: CHARIO_ABI,\r\n//         functionName: 'getUserByWallet',\r\n//         args: [address],\r\n//         query: {\r\n//             enabled: !!address,\r\n//         },\r\n//     });\r\n\r\n//     // Helper function to execute contract writes\r\n//     const executeWrite = useCallback(async (functionName, args, value, successMessage) => {\r\n//         if (!isConnected) {\r\n//             toast.error('Please connect your wallet');\r\n//             return;\r\n//         }\r\n\r\n//         setIsLoading(true);\r\n\r\n//         try {\r\n//             const hash = await writeContract({\r\n//                 address: CONTRACT_ADDRESS,\r\n//                 abi: CHARIO_ABI,\r\n//                 functionName: functionName,\r\n//                 args: args,\r\n//                 value: value,\r\n//             });\r\n\r\n//             setPendingTxHash(hash);\r\n//             toast.success(successMessage || 'Transaction submitted');\r\n//         } catch (error) {\r\n//             console.error(`Error executing ${functionName}:`, error);\r\n//             toast.error(error.message || 'Transaction failed');\r\n//             setIsLoading(false);\r\n//         }\r\n//     }, [writeContract, isConnected]);\r\n\r\n//     // Create Charity\r\n//     const createCharity = useCallback(async (params) => {\r\n//         const { owner, title, description, target, deadline, image } = params;\r\n\r\n//         try {\r\n//             const targetInWei = parseEther(target.toString());\r\n//             await executeWrite(\r\n//                 'createCharity',\r\n//                 [owner, title, description, targetInWei, deadline, image],\r\n//                 undefined,\r\n//                 'Creating charity...'\r\n//             );\r\n//         } catch (error) {\r\n//             console.error('Error creating charity:', error);\r\n//             toast.error('Failed to create charity');\r\n//         }\r\n//     }, [executeWrite]);\r\n\r\n//     // Donate to Charity\r\n//     const donateToCharity = useCallback(async (charityId, amount) => {\r\n//         try {\r\n//             const amountInWei = parseEther(amount.toString());\r\n//             await executeWrite(\r\n//                 'donateToCharity',\r\n//                 [charityId],\r\n//                 amountInWei,\r\n//                 'Processing donation...'\r\n//             );\r\n//         } catch (error) {\r\n//             console.error('Error donating:', error);\r\n//             toast.error('Failed to donate');\r\n//         }\r\n//     }, [executeWrite]);\r\n\r\n//     // Withdraw Funds\r\n//     const withdrawFunds = useCallback(async (charityId) => {\r\n//         await executeWrite(\r\n//             'withdrawFunds',\r\n//             [charityId],\r\n//             undefined,\r\n//             'Withdrawing funds...'\r\n//         );\r\n//     }, [executeWrite]);\r\n\r\n//     // Refund Donation\r\n//     const refundDonation = useCallback(async (charityId) => {\r\n//         await executeWrite(\r\n//             'refundDonation',\r\n//             [charityId],\r\n//             undefined,\r\n//             'Processing refund...'\r\n//         );\r\n//     }, [executeWrite]);\r\n\r\n//     // Cancel Charity\r\n//     const cancelCharity = useCallback(async (charityId) => {\r\n//         await executeWrite(\r\n//             'cancelCharity',\r\n//             [charityId],\r\n//             undefined,\r\n//             'Cancelling charity...'\r\n//         );\r\n//     }, [executeWrite]);\r\n\r\n//     // Update Charity Status\r\n//     const updateCharityStatus = useCallback(async (charityId) => {\r\n//         await executeWrite(\r\n//             'updateCharityStatus',\r\n//             [charityId],\r\n//             undefined,\r\n//             'Updating status...'\r\n//         );\r\n//     }, [executeWrite]);\r\n\r\n//     const loading = isLoading || isWritePending || isConfirming;\r\n\r\n//     return {\r\n//         // Data\r\n//         numberOfCharities,\r\n//         userData,\r\n\r\n//         // Actions\r\n//         createCharity,\r\n//         donateToCharity,\r\n//         withdrawFunds,\r\n//         refundDonation,\r\n//         cancelCharity,\r\n//         updateCharityStatus,\r\n\r\n//         // Refetch functions\r\n//         refetchCharityCount,\r\n//         refetchUserData,\r\n\r\n//         // State\r\n//         loading,\r\n//         isConnected,\r\n//         address,\r\n//         txHash,\r\n\r\n//         // Utilities\r\n//         formatEther,\r\n//         parseEther,\r\n//     };\r\n// };\r\n\r\n// // Hook for fetching multiple charities\r\n// export const useCharities = (limit) => {\r\n//     const [charities, setCharities] = useState([]);\r\n//     const [isLoading, setIsLoading] = useState(false);\r\n\r\n//     const { data: numberOfCharities } = useReadContract({\r\n//         address: CONTRACT_ADDRESS,\r\n//         abi: CHARIO_ABI,\r\n//         functionName: 'numberOfCharities',\r\n//     });\r\n\r\n//     const totalCharities = numberOfCharities ? Number(numberOfCharities) : 0;\r\n//     const charityLimit = limit ? Math.min(limit, totalCharities) : totalCharities;\r\n\r\n//     // Fetch all charities\r\n//     useEffect(() => {\r\n//         if (totalCharities > 0) {\r\n//             setIsLoading(true);\r\n//             const fetchCharities = async () => {\r\n//                 const charityPromises = [];\r\n\r\n//                 for (let i = 0; i < charityLimit; i++) {\r\n//                     charityPromises.push(\r\n//                         fetch(`/api/v1/charity/${i}`).then(res => res.json()) // You'll need to implement this API endpoint\r\n//                     );\r\n//                 }\r\n\r\n//                 try {\r\n//                     const results = await Promise.all(charityPromises);\r\n//                     setCharities(results.map((charity, index) => ({ ...charity, id: index })));\r\n//                 } catch (error) {\r\n//                     console.error('Error fetching charities:', error);\r\n//                 } finally {\r\n//                     setIsLoading(false);\r\n//                 }\r\n//             };\r\n\r\n//             fetchCharities();\r\n//         }\r\n//     }, [totalCharities, charityLimit]);\r\n\r\n//     return {\r\n//         charities,\r\n//         isLoading,\r\n//         totalCharities,\r\n//     };\r\n// };\r\n\r\n// // Hook for a single charity\r\n// export const useCharity = (charityId) => {\r\n//     const { address } = useAccount();\r\n\r\n//     const { data: charity, isLoading: isLoadingCharity, refetch: refetchCharity } = useReadContract({\r\n//         address: CONTRACT_ADDRESS,\r\n//         abi: CHARIO_ABI,\r\n//         functionName: 'getCharity',\r\n//         args: [charityId],\r\n//     });\r\n\r\n//     const { data: donorContribution, isLoading: isLoadingContribution, refetch: refetchContribution } = useReadContract({\r\n//         address: CONTRACT_ADDRESS,\r\n//         abi: CHARIO_ABI,\r\n//         functionName: 'getDonorContribution',\r\n//         args: [charityId, address],\r\n//         query: {\r\n//             enabled: !!address,\r\n//         },\r\n//     });\r\n\r\n//     const { data: escrowBalance, isLoading: isLoadingEscrow, refetch: refetchEscrow } = useReadContract({\r\n//         address: CONTRACT_ADDRESS,\r\n//         abi: CHARIO_ABI,\r\n//         functionName: 'getEscrowBalance',\r\n//         args: [charityId],\r\n//     });\r\n\r\n//     const isLoading = isLoadingCharity || isLoadingContribution || isLoadingEscrow;\r\n//     const isOwner = (charity?.owner === address);\r\n//     const canRefund = donorContribution && donorContribution > 0n;\r\n\r\n//     // Refetch all charity data\r\n//     const refetchAll = useCallback(() => {\r\n//         refetchCharity();\r\n//         refetchContribution();\r\n//         refetchEscrow();\r\n//     }, [refetchCharity, refetchContribution, refetchEscrow]);\r\n\r\n//     return {\r\n//         charity,\r\n//         donorContribution,\r\n//         escrowBalance,\r\n//         isLoading,\r\n//         isOwner,\r\n//         canRefund,\r\n//         refetchAll,\r\n//     };\r\n// };\r\n\r\n// // Utility hook for charity status\r\n// export const useCharityStatus = (status) => {\r\n//     const statusMap = {\r\n//         0: 'Active',\r\n//         1: 'Inactive',\r\n//         2: 'Completed',\r\n//         3: 'Cancelled'\r\n//     };\r\n\r\n//     const statusColors = {\r\n//         0: 'green',\r\n//         1: 'yellow',\r\n//         2: 'blue',\r\n//         3: 'red'\r\n//     };\r\n\r\n//     return {\r\n//         statusText: statusMap[status] || 'Unknown',\r\n//         statusColor: statusColors[status] || 'gray',\r\n//         isActive: status === 0,\r\n//         isCompleted: status === 2,\r\n//         isCancelled: status === 3,\r\n//         isInactive: status === 1,\r\n//     };\r\n// };\r\n\r\n// // Utility hook for formatting dates\r\n// export const useCharityDates = (deadline) => {\r\n//     const deadlineDate = new Date(deadline * 1000);\r\n//     const now = new Date();\r\n//     const timeLeft = deadlineDate - now;\r\n\r\n//     const isExpired = timeLeft <= 0;\r\n//     const daysLeft = Math.ceil(timeLeft / (1000 * 60 * 60 * 24));\r\n\r\n//     return {\r\n//         deadlineDate,\r\n//         isExpired,\r\n//         daysLeft: isExpired ? 0 : daysLeft,\r\n//         formattedDeadline: deadlineDate.toLocaleDateString(),\r\n//         timeLeftText: isExpired ? 'Expired' : `${daysLeft} days left`,\r\n//     };\r\n// };\r\n\r\n\r\n\"use client\";\r\n\r\nimport { useState, useCallback, useEffect } from 'react';\r\nimport {\r\n    useAccount,\r\n    useReadContract,\r\n    useWriteContract,\r\n    useWaitForTransactionReceipt,\r\n} from \"wagmi\";\r\nimport abi from \"@/abi/chario.json\"; // Make sure this ABI is from your LATEST compiled contract\r\nimport { parseEther, formatEther } from 'viem';\r\nimport { toast } from \"sonner\";\r\n\r\n// --- Configuration ---\r\nconst CHARIO_ABI = abi;\r\nconst CONTRACT_ADDRESS = process.env.NEXT_PUBLIC_CONTRACT_ADDRESS;\r\n\r\n// --- Main Hook for Contract Interactions ---\r\nexport const useChario = () => {\r\n    const { address, isConnected } = useAccount();\r\n    const [isLoading, setIsLoading] = useState(false);\r\n\r\n    // Wagmi hooks for writing transactions\r\n    const { data: txHash, writeContractAsync, error: writeError, isPending: isWritePending } = useWriteContract();\r\n\r\n    // Wagmi hook to wait for transaction confirmation\r\n    const { isLoading: isConfirming, isSuccess: isConfirmed } = useWaitForTransactionReceipt({ hash: txHash });\r\n\r\n    // Effect for handling transaction success\r\n    useEffect(() => {\r\n        if (isConfirmed) {\r\n            toast.success('Transaction confirmed!');\r\n            setIsLoading(false);\r\n        }\r\n    }, [isConfirmed]);\r\n\r\n    // Effect for handling transaction errors\r\n    useEffect(() => {\r\n        if (writeError) {\r\n            toast.error(writeError.shortMessage || 'Transaction failed.');\r\n            console.error('Wagmi Write Error:', writeError);\r\n            setIsLoading(false);\r\n        }\r\n    }, [writeError]);\r\n\r\n    // Read contract data\r\n    const { data: numberOfCharities, refetch: refetchCharityCount } = useReadContract({\r\n        address: CONTRACT_ADDRESS,\r\n        abi: CHARIO_ABI,\r\n        functionName: 'numberOfCharities',\r\n    });\r\n\r\n    // Helper function to execute contract writes\r\n    const executeWrite = useCallback(async (functionName, args, value, loadingMessage) => {\r\n        if (!isConnected) {\r\n            return {\r\n                error: 'Please connect your wallet first.',\r\n                walletError: true\r\n            };\r\n        }\r\n        setIsLoading(true);\r\n        toast.info(loadingMessage || 'Submitting transaction...');\r\n\r\n        try {\r\n            await writeContractAsync({\r\n                address: CONTRACT_ADDRESS,\r\n                abi: CHARIO_ABI,\r\n                functionName,\r\n                args,\r\n                value,\r\n            });\r\n        } catch (error) {\r\n            // Error is handled by the useEffect hook for writeError\r\n            console.error(`Error submitting ${functionName}:`, error);\r\n            return {\r\n                error: error.message\r\n            };\r\n        }\r\n    }, [isConnected, writeContractAsync]);\r\n\r\n    /**\r\n     * Creates a new charity campaign.\r\n     */\r\n    const createCharity = useCallback(async (params) => {\r\n        const { owner, title, description, target, deadline, image, userId } = params;\r\n        // const targetInWei = parseEther(target.toString());\r\n        const result = await executeWrite(\r\n            'createCharity',\r\n            // [owner, title, description, targetInWei || 0, deadline || 0, image],\r\n            [owner, title, description, target, 0, image, userId],\r\n            undefined,\r\n            'Creating your charity...'\r\n        );\r\n\r\n        return result;\r\n    }, [executeWrite]);\r\n\r\n    /**\r\n     * Donates a specified amount to a charity.\r\n     */\r\n    const donateToCharity = useCallback(async (charityId, amount, userId) => {\r\n        const amountInWei = parseEther(amount.toString());\r\n        const result = await executeWrite(\r\n            'donateToCharity',\r\n            [charityId, userId],\r\n            amountInWei,\r\n            'Processing your donation...'\r\n        );\r\n        return result\r\n    }, [executeWrite]);\r\n\r\n    /**\r\n     * Sets the status of a charity (callable only by the charity owner).\r\n     * @param {number} charityId The ID of the charity.\r\n     * @param {number} newStatus The new status (0 for ACTIVE, 1 for INACTIVE).\r\n     */\r\n    const setCharityStatus = useCallback(async (charityId, newStatus) => {\r\n        await executeWrite(\r\n            'setCharityStatus',\r\n            [charityId, newStatus],\r\n            undefined,\r\n            'Updating charity status...'\r\n        );\r\n\r\n        return result\r\n    }, [executeWrite]);\r\n\r\n    return {\r\n        // Data\r\n        numberOfCharities,\r\n\r\n        // Actions\r\n        createCharity,\r\n        donateToCharity,\r\n        setCharityStatus, // <-- New function\r\n\r\n        // Refetch functions\r\n        refetchCharityCount,\r\n\r\n        // State\r\n        loading: isLoading || isWritePending || isConfirming,\r\n        isConnected,\r\n        address,\r\n        txHash,\r\n\r\n        // Utilities\r\n        formatEther,\r\n    };\r\n};\r\n\r\n// Hook for fetching multiple charities\r\nexport const useCharities = (limit) => {\r\n    const [charities, setCharities] = useState([]);\r\n    const [isLoading, setIsLoading] = useState(false);\r\n\r\n    const { data: numberOfCharities } = useReadContract({\r\n        address: CONTRACT_ADDRESS,\r\n        abi: CHARIO_ABI,\r\n        functionName: 'numberOfCharities',\r\n    });\r\n\r\n    const totalCharities = numberOfCharities ? Number(numberOfCharities) : 0;\r\n    const charityLimit = limit ? Math.min(limit, totalCharities) : totalCharities;\r\n\r\n    // Fetch all charities\r\n    useEffect(() => {\r\n        if (totalCharities > 0) {\r\n            setIsLoading(true);\r\n            const fetchCharities = async () => {\r\n                const charityPromises = [];\r\n\r\n                for (let i = 0; i < charityLimit; i++) {\r\n                    charityPromises.push(\r\n                        fetch(`/api/v1/charity/${i}`).then(res => res.json()) // You'll need to implement this API endpoint\r\n                    );\r\n                }\r\n\r\n                try {\r\n                    const results = await Promise.all(charityPromises);\r\n                    setCharities(results.map((charity, index) => ({ ...charity, id: index })));\r\n                } catch (error) {\r\n                    console.error('Error fetching charities:', error);\r\n                } finally {\r\n                    setIsLoading(false);\r\n                }\r\n            };\r\n\r\n            fetchCharities();\r\n        }\r\n    }, [totalCharities, charityLimit]);\r\n\r\n    return {\r\n        charities,\r\n        isLoading,\r\n        totalCharities,\r\n    };\r\n};\r\n\r\n\r\n// --- Hook for a Single Charity's Data ---\r\nexport const useCharity = (charityId) => {\r\n    const { address } = useAccount();\r\n\r\n    const { data: charity, isLoading: isLoadingCharity, refetch: refetchCharity } = useReadContract({\r\n        address: CONTRACT_ADDRESS,\r\n        abi: CHARIO_ABI,\r\n        functionName: 'getCharity',\r\n        args: [charityId],\r\n    });\r\n\r\n    const { data: donorContribution, isLoading: isLoadingContribution, refetch: refetchContribution } = useReadContract({\r\n        address: CONTRACT_ADDRESS,\r\n        abi: CHARIO_ABI,\r\n        functionName: 'getDonorContribution',\r\n        args: [charityId, address],\r\n        query: { enabled: !!address },\r\n    });\r\n\r\n    // Refetch all charity data\r\n    const refetchAll = useCallback(() => {\r\n        refetchCharity();\r\n        refetchContribution();\r\n    }, [refetchCharity, refetchContribution]);\r\n\r\n    return {\r\n        charity,\r\n        donorContribution,\r\n        isLoading: isLoadingCharity || isLoadingContribution,\r\n        isOwner: charity?.owner === address,\r\n        refetchAll,\r\n    };\r\n};\r\n\r\n// --- Utility Hook for Displaying Charity Status ---\r\nexport const useCharityStatus = (status) => {\r\n    const statusMap = {\r\n        0: 'Active',\r\n        1: 'Inactive',\r\n    };\r\n\r\n    const statusColors = {\r\n        0: 'green',\r\n        1: 'gray',\r\n    };\r\n\r\n    return {\r\n        statusText: statusMap[status] ?? 'Unknown',\r\n        statusColor: statusColors[status] ?? 'gray',\r\n        isActive: status === 0,\r\n        isInactive: status === 1,\r\n    };\r\n};"], "names": [], "mappings": "AAAA,gBAAgB;AAEhB,4DAA4D;AAC5D,WAAW;AACX,kBAAkB;AAClB,uBAAuB;AACvB,wBAAwB;AACxB,oCAAoC;AACpC,kBAAkB;AAClB,wEAAwE;AACxE,kDAAkD;AAClD,kCAAkC;AAElC,yBAAyB;AACzB,oEAAoE;AAEpE,mCAAmC;AACnC,qDAAqD;AACrD,yDAAyD;AACzD,gEAAgE;AAEhE,6BAA6B;AAC7B,gHAAgH;AAEhH,sCAAsC;AACtC,iGAAiG;AACjG,wBAAwB;AACxB,UAAU;AAEV,uCAAuC;AACvC,wBAAwB;AACxB,wBAAwB;AACxB,wDAAwD;AACxD,YAAY;AACZ,8CAA8C;AAC9C,uDAAuD;AACvD,mCAAmC;AACnC,sCAAsC;AACtC,YAAY;AACZ,kFAAkF;AAElF,wBAAwB;AACxB,4BAA4B;AAC5B,+DAA+D;AAC/D,wEAAwE;AACxE,mCAAmC;AACnC,sCAAsC;AACtC,YAAY;AACZ,wBAAwB;AAExB,4BAA4B;AAC5B,0FAA0F;AAC1F,qCAAqC;AACrC,2BAA2B;AAC3B,6CAA6C;AAC7C,UAAU;AAEV,6EAA6E;AAC7E,qCAAqC;AACrC,2BAA2B;AAC3B,2CAA2C;AAC3C,2BAA2B;AAC3B,mBAAmB;AACnB,kCAAkC;AAClC,aAAa;AACb,UAAU;AAEV,oDAAoD;AACpD,8FAA8F;AAC9F,8BAA8B;AAC9B,yDAAyD;AACzD,sBAAsB;AACtB,YAAY;AAEZ,8BAA8B;AAE9B,gBAAgB;AAChB,iDAAiD;AACjD,6CAA6C;AAC7C,mCAAmC;AACnC,8CAA8C;AAC9C,8BAA8B;AAC9B,gCAAgC;AAChC,kBAAkB;AAElB,sCAAsC;AACtC,wEAAwE;AACxE,4BAA4B;AAC5B,wEAAwE;AACxE,kEAAkE;AAClE,mCAAmC;AACnC,YAAY;AACZ,wCAAwC;AAExC,wBAAwB;AACxB,4DAA4D;AAC5D,iFAAiF;AAEjF,gBAAgB;AAChB,iEAAiE;AACjE,kCAAkC;AAClC,mCAAmC;AACnC,6EAA6E;AAC7E,6BAA6B;AAC7B,wCAAwC;AACxC,iBAAiB;AACjB,4BAA4B;AAC5B,+DAA+D;AAC/D,uDAAuD;AACvD,YAAY;AACZ,0BAA0B;AAE1B,2BAA2B;AAC3B,yEAAyE;AACzE,gBAAgB;AAChB,iEAAiE;AACjE,kCAAkC;AAClC,qCAAqC;AACrC,+BAA+B;AAC/B,+BAA+B;AAC/B,2CAA2C;AAC3C,iBAAiB;AACjB,4BAA4B;AAC5B,uDAAuD;AACvD,+CAA+C;AAC/C,YAAY;AACZ,0BAA0B;AAE1B,wBAAwB;AACxB,+DAA+D;AAC/D,8BAA8B;AAC9B,+BAA+B;AAC/B,2BAA2B;AAC3B,yBAAyB;AACzB,qCAAqC;AACrC,aAAa;AACb,0BAA0B;AAE1B,yBAAyB;AACzB,gEAAgE;AAChE,8BAA8B;AAC9B,gCAAgC;AAChC,2BAA2B;AAC3B,yBAAyB;AACzB,qCAAqC;AACrC,aAAa;AACb,0BAA0B;AAE1B,wBAAwB;AACxB,+DAA+D;AAC/D,8BAA8B;AAC9B,+BAA+B;AAC/B,2BAA2B;AAC3B,yBAAyB;AACzB,sCAAsC;AACtC,aAAa;AACb,0BAA0B;AAE1B,+BAA+B;AAC/B,qEAAqE;AACrE,8BAA8B;AAC9B,qCAAqC;AACrC,2BAA2B;AAC3B,yBAAyB;AACzB,mCAAmC;AACnC,aAAa;AACb,0BAA0B;AAE1B,mEAAmE;AAEnE,eAAe;AACf,kBAAkB;AAClB,6BAA6B;AAC7B,oBAAoB;AAEpB,qBAAqB;AACrB,yBAAyB;AACzB,2BAA2B;AAC3B,yBAAyB;AACzB,0BAA0B;AAC1B,yBAAyB;AACzB,+BAA+B;AAE/B,+BAA+B;AAC/B,+BAA+B;AAC/B,2BAA2B;AAE3B,mBAAmB;AACnB,mBAAmB;AACnB,uBAAuB;AACvB,mBAAmB;AACnB,kBAAkB;AAElB,uBAAuB;AACvB,uBAAuB;AACvB,sBAAsB;AACtB,SAAS;AACT,KAAK;AAEL,0CAA0C;AAC1C,2CAA2C;AAC3C,sDAAsD;AACtD,yDAAyD;AAEzD,4DAA4D;AAC5D,qCAAqC;AACrC,2BAA2B;AAC3B,6CAA6C;AAC7C,UAAU;AAEV,gFAAgF;AAChF,qFAAqF;AAErF,6BAA6B;AAC7B,wBAAwB;AACxB,oCAAoC;AACpC,kCAAkC;AAClC,mDAAmD;AACnD,8CAA8C;AAE9C,2DAA2D;AAC3D,4CAA4C;AAC5C,8HAA8H;AAC9H,yBAAyB;AACzB,oBAAoB;AAEpB,wBAAwB;AACxB,0EAA0E;AAC1E,kGAAkG;AAClG,oCAAoC;AACpC,yEAAyE;AACzE,8BAA8B;AAC9B,2CAA2C;AAC3C,oBAAoB;AACpB,iBAAiB;AAEjB,gCAAgC;AAChC,YAAY;AACZ,0CAA0C;AAE1C,eAAe;AACf,qBAAqB;AACrB,qBAAqB;AACrB,0BAA0B;AAC1B,SAAS;AACT,KAAK;AAEL,+BAA+B;AAC/B,6CAA6C;AAC7C,wCAAwC;AAExC,wGAAwG;AACxG,qCAAqC;AACrC,2BAA2B;AAC3B,sCAAsC;AACtC,6BAA6B;AAC7B,UAAU;AAEV,4HAA4H;AAC5H,qCAAqC;AACrC,2BAA2B;AAC3B,gDAAgD;AAChD,sCAAsC;AACtC,mBAAmB;AACnB,kCAAkC;AAClC,aAAa;AACb,UAAU;AAEV,4GAA4G;AAC5G,qCAAqC;AACrC,2BAA2B;AAC3B,4CAA4C;AAC5C,6BAA6B;AAC7B,UAAU;AAEV,sFAAsF;AACtF,oDAAoD;AACpD,qEAAqE;AAErE,kCAAkC;AAClC,6CAA6C;AAC7C,4BAA4B;AAC5B,iCAAiC;AACjC,2BAA2B;AAC3B,gEAAgE;AAEhE,eAAe;AACf,mBAAmB;AACnB,6BAA6B;AAC7B,yBAAyB;AACzB,qBAAqB;AACrB,mBAAmB;AACnB,qBAAqB;AACrB,sBAAsB;AACtB,SAAS;AACT,KAAK;AAEL,qCAAqC;AACrC,gDAAgD;AAChD,0BAA0B;AAC1B,uBAAuB;AACvB,yBAAyB;AACzB,0BAA0B;AAC1B,yBAAyB;AACzB,SAAS;AAET,6BAA6B;AAC7B,sBAAsB;AACtB,uBAAuB;AACvB,qBAAqB;AACrB,mBAAmB;AACnB,SAAS;AAET,eAAe;AACf,sDAAsD;AACtD,uDAAuD;AACvD,kCAAkC;AAClC,qCAAqC;AACrC,qCAAqC;AACrC,oCAAoC;AACpC,SAAS;AACT,KAAK;AAEL,uCAAuC;AACvC,iDAAiD;AACjD,sDAAsD;AACtD,8BAA8B;AAC9B,2CAA2C;AAE3C,uCAAuC;AACvC,oEAAoE;AAEpE,eAAe;AACf,wBAAwB;AACxB,qBAAqB;AACrB,8CAA8C;AAC9C,gEAAgE;AAChE,yEAAyE;AACzE,SAAS;AACT,KAAK;;;;;;;AAKL;AACA;AAAA;AAAA;AAAA;AAMA,oKAAqC,2DAA2D;AAChG;AAAA;AACA;AAXA;;;;;;AAaA,wBAAwB;AACxB,MAAM,aAAa,4FAAA,CAAA,UAAG;AACtB,MAAM;AAGC,MAAM,YAAY;IACrB,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,2JAAA,CAAA,aAAU,AAAD;IAC1C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,uCAAuC;IACvC,MAAM,EAAE,MAAM,MAAM,EAAE,kBAAkB,EAAE,OAAO,UAAU,EAAE,WAAW,cAAc,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,mBAAgB,AAAD;IAE1G,kDAAkD;IAClD,MAAM,EAAE,WAAW,YAAY,EAAE,WAAW,WAAW,EAAE,GAAG,CAAA,GAAA,6KAAA,CAAA,+BAA4B,AAAD,EAAE;QAAE,MAAM;IAAO;IAExG,0CAA0C;IAC1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,aAAa;YACb,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,aAAa;QACjB;IACJ,GAAG;QAAC;KAAY;IAEhB,yCAAyC;IACzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,YAAY;YACZ,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,WAAW,YAAY,IAAI;YACvC,QAAQ,KAAK,CAAC,sBAAsB;YACpC,aAAa;QACjB;IACJ,GAAG;QAAC;KAAW;IAEf,qBAAqB;IACrB,MAAM,EAAE,MAAM,iBAAiB,EAAE,SAAS,mBAAmB,EAAE,GAAG,CAAA,GAAA,gKAAA,CAAA,kBAAe,AAAD,EAAE;QAC9E,SAAS;QACT,KAAK;QACL,cAAc;IAClB;IAEA,6CAA6C;IAC7C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,cAAc,MAAM,OAAO;QAC/D,IAAI,CAAC,aAAa;YACd,OAAO;gBACH,OAAO;gBACP,aAAa;YACjB;QACJ;QACA,aAAa;QACb,wIAAA,CAAA,QAAK,CAAC,IAAI,CAAC,kBAAkB;QAE7B,IAAI;YACA,MAAM,mBAAmB;gBACrB,SAAS;gBACT,KAAK;gBACL;gBACA;gBACA;YACJ;QACJ,EAAE,OAAO,OAAO;YACZ,wDAAwD;YACxD,QAAQ,KAAK,CAAC,CAAC,iBAAiB,EAAE,aAAa,CAAC,CAAC,EAAE;YACnD,OAAO;gBACH,OAAO,MAAM,OAAO;YACxB;QACJ;IACJ,GAAG;QAAC;QAAa;KAAmB;IAEpC;;KAEC,GACD,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACrC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG;QACvE,qDAAqD;QACrD,MAAM,UAAS,MAAM,aACjB,iBACA,uEAAuE;QACvE;YAAC;YAAO;YAAO;YAAa;YAAQ;YAAG;YAAO;SAAO,EACrD,WACA;QAGJ,OAAO;IACX,GAAG;QAAC;KAAa;IAEjB;;KAEC,GACD,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,WAAW,QAAQ;QAC1D,MAAM,cAAc,CAAA,GAAA,2JAAA,CAAA,aAAU,AAAD,EAAE,OAAO,QAAQ;QAC9C,MAAM,UAAS,MAAM,aACjB,mBACA;YAAC;YAAW;SAAO,EACnB,aACA;QAEJ,OAAO;IACX,GAAG;QAAC;KAAa;IAEjB;;;;KAIC,GACD,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,WAAW;QACnD,MAAM,aACF,oBACA;YAAC;YAAW;SAAU,EACtB,WACA;QAGJ,OAAO;IACX,GAAG;QAAC;KAAa;IAEjB,OAAO;QACH,OAAO;QACP;QAEA,UAAU;QACV;QACA;QACA;QAEA,oBAAoB;QACpB;QAEA,QAAQ;QACR,SAAS,aAAa,kBAAkB;QACxC;QACA;QACA;QAEA,YAAY;QACZ,aAAA,4JAAA,CAAA,cAAW;IACf;AACJ;AAGO,MAAM,eAAe,CAAC;IACzB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,EAAE,MAAM,iBAAiB,EAAE,GAAG,CAAA,GAAA,gKAAA,CAAA,kBAAe,AAAD,EAAE;QAChD,SAAS;QACT,KAAK;QACL,cAAc;IAClB;IAEA,MAAM,iBAAiB,oBAAoB,OAAO,qBAAqB;IACvE,MAAM,eAAe,QAAQ,KAAK,GAAG,CAAC,OAAO,kBAAkB;IAE/D,sBAAsB;IACtB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,iBAAiB,GAAG;YACpB,aAAa;YACb,MAAM,iBAAiB;gBACnB,MAAM,kBAAkB,EAAE;gBAE1B,IAAK,IAAI,IAAI,GAAG,IAAI,cAAc,IAAK;oBACnC,gBAAgB,IAAI,CAChB,MAAM,CAAC,gBAAgB,EAAE,GAAG,EAAE,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI,IAAI,6CAA6C;;gBAE3G;gBAEA,IAAI;oBACA,MAAM,UAAU,MAAM,QAAQ,GAAG,CAAC;oBAClC,aAAa,QAAQ,GAAG,CAAC,CAAC,SAAS,QAAU,CAAC;4BAAE,GAAG,OAAO;4BAAE,IAAI;wBAAM,CAAC;gBAC3E,EAAE,OAAO,OAAO;oBACZ,QAAQ,KAAK,CAAC,6BAA6B;gBAC/C,SAAU;oBACN,aAAa;gBACjB;YACJ;YAEA;QACJ;IACJ,GAAG;QAAC;QAAgB;KAAa;IAEjC,OAAO;QACH;QACA;QACA;IACJ;AACJ;AAIO,MAAM,aAAa,CAAC;IACvB,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,2JAAA,CAAA,aAAU,AAAD;IAE7B,MAAM,EAAE,MAAM,OAAO,EAAE,WAAW,gBAAgB,EAAE,SAAS,cAAc,EAAE,GAAG,CAAA,GAAA,gKAAA,CAAA,kBAAe,AAAD,EAAE;QAC5F,SAAS;QACT,KAAK;QACL,cAAc;QACd,MAAM;YAAC;SAAU;IACrB;IAEA,MAAM,EAAE,MAAM,iBAAiB,EAAE,WAAW,qBAAqB,EAAE,SAAS,mBAAmB,EAAE,GAAG,CAAA,GAAA,gKAAA,CAAA,kBAAe,AAAD,EAAE;QAChH,SAAS;QACT,KAAK;QACL,cAAc;QACd,MAAM;YAAC;YAAW;SAAQ;QAC1B,OAAO;YAAE,SAAS,CAAC,CAAC;QAAQ;IAChC;IAEA,2BAA2B;IAC3B,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC3B;QACA;IACJ,GAAG;QAAC;QAAgB;KAAoB;IAExC,OAAO;QACH;QACA;QACA,WAAW,oBAAoB;QAC/B,SAAS,SAAS,UAAU;QAC5B;IACJ;AACJ;AAGO,MAAM,mBAAmB,CAAC;IAC7B,MAAM,YAAY;QACd,GAAG;QACH,GAAG;IACP;IAEA,MAAM,eAAe;QACjB,GAAG;QACH,GAAG;IACP;IAEA,OAAO;QACH,YAAY,SAAS,CAAC,OAAO,IAAI;QACjC,aAAa,YAAY,CAAC,OAAO,IAAI;QACrC,UAAU,WAAW;QACrB,YAAY,WAAW;IAC3B;AACJ", "debugId": null}}, {"offset": {"line": 2006, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/js/web3/packages/chario/src/app/home/<USER>/new/_components/create-charity-page.jsx"], "sourcesContent": ["'use client'\r\n\r\nimport Create<PERSON><PERSON>tyForm from \"@/components/md/forms/create-charity-form\"\r\nimport { createCharitySchema } from \"@/helpers/zod/charity-schema\";\r\nimport { useParams, useRouter } from \"next/navigation\"\r\nimport { useForm } from \"react-hook-form\";\r\nimport { toast } from \"sonner\";\r\nimport { zodResolver } from \"@hookform/resolvers/zod\";\r\nimport CreateCharityFormComponent from \"@/components/md/forms/create-charity-form\";\r\nimport CharityCardMd from \"@/components/md/charity-card-md\";\r\nimport { useEffect } from \"react\";\r\nimport { useAccount } from \"wagmi\";\r\nimport { useChario } from \"@/hooks/use-chario\";\r\nimport { useSession } from \"@/lib/auth-client\";\r\nimport { useTranslations } from '@/contexts/LanguageContext';\r\n\r\n\r\n\r\nfunction CreateCharityPage() {\r\n    const t = useTranslations();\r\n    const params = useParams();\r\n    const router = useRouter();\r\n    const { address } = useAccount();\r\n    const { createCharity, loading } = useChario();\r\n    const { data: userData } = useSession();\r\n\r\n    const CreateCharityForm = useForm({\r\n        resolver: zodResolver(createCharitySchema),\r\n        defaultValues: {\r\n            title: \"\",\r\n            description: \"\",\r\n            target: \"\",\r\n            deadline: \"\",\r\n            image: null,\r\n        },\r\n    })\r\n\r\n    const watchForm = CreateCharityForm.watch();\r\n\r\n    // useEffect(() => {\r\n    //     console.log(\"Current form values:\", watchForm);\r\n\r\n    //     const result = createCharitySchema.safeParse(watchForm);\r\n\r\n    //     if (!result.success) {\r\n    //         // result.error is a ZodError object containing detailed errors\r\n    //         console.log(\"Validation errors:\", result.error.format());\r\n    //     } else {\r\n    //         console.log(\"No validation errors\");\r\n    //     }\r\n    // }, [watchForm]);\r\n\r\n\r\n    async function onSubmit(data) {\r\n        const result = await createCharity({\r\n            owner: address,\r\n            title: data.title,\r\n            description: data.description,\r\n            target: data.target, // 2.5 ETH\r\n            deadline: Math.floor(new Date(watchForm.deadline).getTime() / 1000),\r\n            image: `${process.env.NEXT_PUBLIC_IPFS_GATEWAY}/${watchForm.image.file_cid}`,\r\n            userId: userData.user.id\r\n        });\r\n        if (result?.error && result?.walletError) {\r\n            toast.error(result.error);\r\n        } else {\r\n            toast.success(\"Charity created successfully \\n Your charity will be visible on the home page shortly\");\r\n            router.push('/home/<USER>')\r\n        }\r\n    }\r\n\r\n    return (\r\n        <div className=\"p-2 md:p-6 lg:p-8 pt-10\">\r\n            <p className='text-2xl font-semibold mb-1 '>\r\n                Create a new charity\r\n            </p>\r\n            <p className='text-muted-foreground  text-xs mb-8'>\r\n                By uploading files, you confirm that you have the necessary rights to any content that you upload. Do not use content that infringes on others intellectual property or privacy rights.\r\n            </p>\r\n            <div className='grid grid-cols-1 lg:grid-cols-2 gap-8'>\r\n                <CreateCharityFormComponent\r\n                    form={CreateCharityForm}\r\n                    onSubmit={onSubmit}\r\n                />\r\n                <div className=\"p-4 bg-border rounded-lg ring-1 ring-input\">\r\n                    <h2 className='text-2xl font-semibold mb-4 text-center'>\r\n                        {t('common.preview')}\r\n                    </h2>\r\n                    <div className=\"flex justify-center\">\r\n                        <CharityCardMd\r\n                            charity={{\r\n                                title: watchForm.title,\r\n                                description: watchForm.description,\r\n                                amountCollected: 0,\r\n                                target: watchForm.target,\r\n                                deadline: Math.floor(new Date(watchForm.deadline).getTime() / 1000),\r\n                                image: watchForm.image?.file_path ? `${process.env.NEXT_PUBLIC_IPFS_GATEWAY}/${watchForm.image.file_cid}` : watchForm.image?.local_preview_url,\r\n                            }}\r\n                        />\r\n                    </div>\r\n                    <div className='mt-6 text-muted-foreground text-sm'>\r\n                        <h3 className=' text-white mb-2 font-semibold'>Preview Notes:</h3>\r\n                        <ul className='list-disc pl-5 space-y-1'>\r\n                            <li>This is how your charity will appear to donors</li>\r\n                            <li>Image will be cropped to square aspect ratio</li>\r\n                            <li>Target amount is in ETH (1 ETH = 1.000000000000000000 ETH)</li>\r\n                        </ul>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    )\r\n}\r\n\r\nexport default CreateCharityPage"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAdA;;;;;;;;;;;;;;;AAkBA,SAAS;IACL,MAAM,IAAI,CAAA,GAAA,mIAAA,CAAA,kBAAe,AAAD;IACxB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,2JAAA,CAAA,aAAU,AAAD;IAC7B,MAAM,EAAE,aAAa,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,YAAS,AAAD;IAC3C,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,aAAU,AAAD;IAEpC,MAAM,oBAAoB,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE;QAC9B,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE,0IAAA,CAAA,sBAAmB;QACzC,eAAe;YACX,OAAO;YACP,aAAa;YACb,QAAQ;YACR,UAAU;YACV,OAAO;QACX;IACJ;IAEA,MAAM,YAAY,kBAAkB,KAAK;IAEzC,oBAAoB;IACpB,sDAAsD;IAEtD,+DAA+D;IAE/D,6BAA6B;IAC7B,0EAA0E;IAC1E,oEAAoE;IACpE,eAAe;IACf,+CAA+C;IAC/C,QAAQ;IACR,mBAAmB;IAGnB,eAAe,SAAS,IAAI;QACxB,MAAM,SAAS,MAAM,cAAc;YAC/B,OAAO;YACP,OAAO,KAAK,KAAK;YACjB,aAAa,KAAK,WAAW;YAC7B,QAAQ,KAAK,MAAM;YACnB,UAAU,KAAK,KAAK,CAAC,IAAI,KAAK,UAAU,QAAQ,EAAE,OAAO,KAAK;YAC9D,OAAO,6FAAwC,CAAC,EAAE,UAAU,KAAK,CAAC,QAAQ,EAAE;YAC5E,QAAQ,SAAS,IAAI,CAAC,EAAE;QAC5B;QACA,IAAI,QAAQ,SAAS,QAAQ,aAAa;YACtC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,KAAK;QAC5B,OAAO;YACH,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,OAAO,IAAI,CAAC;QAChB;IACJ;IAEA,qBACI,8OAAC;QAAI,WAAU;;0BACX,8OAAC;gBAAE,WAAU;0BAA+B;;;;;;0BAG5C,8OAAC;gBAAE,WAAU;0BAAsC;;;;;;0BAGnD,8OAAC;gBAAI,WAAU;;kCACX,8OAAC,8JAAA,CAAA,UAA0B;wBACvB,MAAM;wBACN,UAAU;;;;;;kCAEd,8OAAC;wBAAI,WAAU;;0CACX,8OAAC;gCAAG,WAAU;0CACT,EAAE;;;;;;0CAEP,8OAAC;gCAAI,WAAU;0CACX,cAAA,8OAAC,iJAAA,CAAA,UAAa;oCACV,SAAS;wCACL,OAAO,UAAU,KAAK;wCACtB,aAAa,UAAU,WAAW;wCAClC,iBAAiB;wCACjB,QAAQ,UAAU,MAAM;wCACxB,UAAU,KAAK,KAAK,CAAC,IAAI,KAAK,UAAU,QAAQ,EAAE,OAAO,KAAK;wCAC9D,OAAO,UAAU,KAAK,EAAE,YAAY,2FAAwC,CAAC,EAAE,UAAU,KAAK,CAAC,QAAQ,EAAE,GAAG,UAAU,KAAK,EAAE;oCACjI;;;;;;;;;;;0CAGR,8OAAC;gCAAI,WAAU;;kDACX,8OAAC;wCAAG,WAAU;kDAAiC;;;;;;kDAC/C,8OAAC;wCAAG,WAAU;;0DACV,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOhC;uCAEe", "debugId": null}}, {"offset": {"line": 2217, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/js/web3/packages/chario/src/components/md/not-authenticated.jsx"], "sourcesContent": ["'use client'\r\n\r\nimport React from 'react'\r\nimport { Button } from '@/components/ui/button'\r\nimport Link from 'next/link'\r\nimport { authClient, signOut, useSession } from '@/lib/auth-client'\r\nimport { useTranslations } from '@/contexts/LanguageContext'\r\nimport { useRouter } from 'next/navigation'\r\n\r\nfunction NotAuthenticated() {\r\n  const t = useTranslations();\r\n  const router = useRouter()\r\n  const sessionData = useSession();\r\n  return (\r\n    <div className='h-full w-full flex flex-col items-center justify-center p-8 text-center'>\r\n      <h1 className='font-bold text-2xl'>{t('auth.notAuthorized')}</h1>\r\n      <p className='text-muted-foreground text-sm mb-8'>{t('auth.notAuthorizedDescription')}</p>\r\n      <Button\r\n        className={'cursor-pointer'}\r\n        onClick={async () => {\r\n          router.push('/signin')\t\r\n          await signOut({\r\n            fetchOptions: {\r\n              onSuccess: () => {\r\n                // router.push(\"/signin\")\r\n                router.push('/signin')\r\n                // toast.success('You have been logged out')\r\n              }\r\n            }\r\n          })\r\n        }}\r\n      >\r\n        {t('auth.signIn')}\r\n      </Button>\r\n    </div >\r\n  )\r\n}\r\n\r\nexport default NotAuthenticated"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AASA,SAAS;IACP,MAAM,IAAI,CAAA,GAAA,mIAAA,CAAA,kBAAe,AAAD;IACxB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,cAAc,CAAA,GAAA,4HAAA,CAAA,aAAU,AAAD;IAC7B,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAAsB,EAAE;;;;;;0BACtC,8OAAC;gBAAE,WAAU;0BAAsC,EAAE;;;;;;0BACrD,8OAAC,kIAAA,CAAA,SAAM;gBACL,WAAW;gBACX,SAAS;oBACP,OAAO,IAAI,CAAC;oBACZ,MAAM,CAAA,GAAA,4HAAA,CAAA,UAAO,AAAD,EAAE;wBACZ,cAAc;4BACZ,WAAW;gCACT,yBAAyB;gCACzB,OAAO,IAAI,CAAC;4BACZ,4CAA4C;4BAC9C;wBACF;oBACF;gBACF;0BAEC,EAAE;;;;;;;;;;;;AAIX;uCAEe", "debugId": null}}]}