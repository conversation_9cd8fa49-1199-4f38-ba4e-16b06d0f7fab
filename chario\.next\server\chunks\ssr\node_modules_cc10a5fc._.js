module.exports = {

"[project]/node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/secp256k1.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/9de0f_@noble_96857cc0._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@walletconnect/utils/node_modules/@noble/curves/esm/secp256k1.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit/dist/esm/exports/core.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_70b76db9._.js",
  "server/chunks/ssr/node_modules_@reown_257cb94b._.js",
  "server/chunks/ssr/node_modules_@reown_6ec2f273._.js",
  "server/chunks/ssr/node_modules_@reown_7cd91761._.js",
  "server/chunks/ssr/node_modules_@reown_fdb8ee88._.js",
  "server/chunks/ssr/node_modules_@reown_appkit-controllers_dist_esm_src_cad7e2a2._.js",
  "server/chunks/ssr/node_modules_@reown_appkit_dist_esm_cae710b7._.js",
  "server/chunks/ssr/node_modules_248d135f._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit/dist/esm/exports/core.js [app-ssr] (ecmascript)");
    });
});
}}),

};