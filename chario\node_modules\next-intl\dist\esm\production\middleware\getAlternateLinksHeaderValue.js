import{normalizeTrailingSlash as e}from"../shared/utils.js";import{getHost as a,getNormalizedPathname as t,getLocalePrefixes as n,isLocaleSupportedOnDomain as o,applyBasePath as r,formatTemplatePathname as l}from"./utils.js";function s({internalTemplateName:s,localizedPathnames:m,request:i,resolvedLocale:p,routing:c}){const f=i.nextUrl.clone(),h=a(i.headers);function u(a,t){return a.pathname=e(a.pathname),i.nextUrl.basePath&&((a=new URL(a)).pathname=r(a.pathname,i.nextUrl.basePath)),`<${a.toString()}>; rel="alternate"; hreflang="${t}"`}function d(e,a){if(m&&"object"==typeof m){const t=m[p];return l(e,t??s,m[a]??s)}return e}h&&(f.port="",f.host=h),f.protocol=i.headers.get("x-forwarded-proto")??f.protocol,f.pathname=t(f.pathname,c.locales,c.localePrefix);const x=n(c.locales,c.localePrefix,!1).flatMap((([e,a])=>{function t(e){return"/"===e?a:a+e}let n;if(c.domains){return c.domains.filter((a=>o(e,a))).map((a=>(n=new URL(f),n.port="",n.host=a.domain,n.pathname=d(f.pathname,e),e===a.defaultLocale&&"always"!==c.localePrefix.mode||(n.pathname=t(n.pathname)),u(n,e))))}{let a;a=m&&"object"==typeof m?d(f.pathname,e):f.pathname,e===c.defaultLocale&&"always"!==c.localePrefix.mode||(a=t(a)),n=new URL(a,f)}return u(n,e)}));if(!c.domains||0===c.domains.length){const e=d(f.pathname,c.defaultLocale);if(e){const a=new URL(e,f);x.push(u(a,"x-default"))}}return x.join(", ")}export{s as default};
