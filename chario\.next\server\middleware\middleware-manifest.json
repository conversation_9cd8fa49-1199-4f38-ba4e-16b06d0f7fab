{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_ecd95a42._.js", "server/edge/chunks/[root-of-the-server]__5e96eddc._.js", "server/edge/chunks/edge-wrapper_5f920134.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/(/?index|/?index\\\\.json)?", "originalSource": "/"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/(ru|en)/:path*{(\\\\.json)}?", "originalSource": "/(ru|en)/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "F/PIblFB6nvDke9duwJxi0Bg6fdrL0y4QbpsLiiVEPI=", "__NEXT_PREVIEW_MODE_ID": "37658f5f279f1697d98a7ebfaae66b24", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "a5f020ab5e24cf7542053db00fa89e55e5576b4c4dc01a039deac2ecf755e1d5", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "fbba2488c526e5bc5ff05160bf61795d40be8c2492b7482a49cea14c117e2a51"}}}, "instrumentation": null, "functions": {}}