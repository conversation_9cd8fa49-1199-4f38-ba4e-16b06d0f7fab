{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_ecd95a42._.js", "server/edge/chunks/[root-of-the-server]__5e96eddc._.js", "server/edge/chunks/edge-wrapper_5f920134.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "F/PIblFB6nvDke9duwJxi0Bg6fdrL0y4QbpsLiiVEPI=", "__NEXT_PREVIEW_MODE_ID": "afa087c5d48553e5dfef54af4afdfd27", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "d6207a0ec23cbe041a5e4267b0db289e4a857846e08897dfbe25a5172aa764fd", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "2d6a368db6b2af7d59949a3b69a3a6a02663d062dd6f12c2629ab600251957d5"}}}, "instrumentation": null, "functions": {}}