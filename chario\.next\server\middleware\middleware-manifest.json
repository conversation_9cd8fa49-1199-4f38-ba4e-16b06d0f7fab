{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_ecd95a42._.js", "server/edge/chunks/[root-of-the-server]__5e96eddc._.js", "server/edge/chunks/edge-wrapper_5f920134.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/(/?index|/?index\\\\.json)?", "originalSource": "/"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/(ru|en)/:path*{(\\\\.json)}?", "originalSource": "/(ru|en)/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "F/PIblFB6nvDke9duwJxi0Bg6fdrL0y4QbpsLiiVEPI=", "__NEXT_PREVIEW_MODE_ID": "939290278bb7b77bcd97557138f896a8", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "a73139bfac6aa44ef6699ebdbeee0db9382e1b091114c9d6f350553852aed05b", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "c91dc8a918ac0fdf1115277e728cb8318d84c69801cea60c34c741daf0ff97b9"}}}, "instrumentation": null, "functions": {}}