{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/js/web3/packages/chario/src/app/page.jsx"], "sourcesContent": ["'use client'\n\nimport { motion } from \"framer-motion\";\nimport { Circle } from \"lucide-react\";\nimport { cn } from \"@/lib/utils\";\nimport { useTranslations } from '@/contexts/LanguageContext';\n\nfunction ElegantShape({\n  className,\n  delay = 0,\n  width = 400,\n  height = 100,\n  rotate = 0,\n  gradient = \"from-white/[0.08]\",\n}) {\n  return (\n    <motion.div\n      initial={{\n        opacity: 0,\n        y: -150,\n        rotate: rotate - 15,\n      }}\n      animate={{\n        opacity: 1,\n        y: 0,\n        rotate: rotate,\n      }}\n      transition={{\n        duration: 2.4,\n        delay,\n        ease: [0.23, 0.86, 0.39, 0.96],\n        opacity: { duration: 1.2 },\n      }}\n      className={cn(\"absolute\", className)}\n    >\n      <motion.div\n        animate={{\n          y: [0, 15, 0],\n        }}\n        transition={{\n          duration: 12,\n          repeat: Number.POSITIVE_INFINITY,\n          ease: \"easeInOut\",\n        }}\n        style={{\n          width,\n          height,\n        }}\n        className=\"relative\"\n      >\n        <div\n          className={cn(\n            \"absolute inset-0 rounded-full\",\n            \"bg-gradient-to-r to-transparent\",\n            gradient,\n            \"backdrop-blur-[2px] border-2 border-white/[0.15]\",\n            \"after:absolute after:inset-0 after:rounded-full\",\n            \"after:bg-[radial-gradient(circle_at_50%_50%,rgba(255,255,255,0.2),transparent_70%)]\"\n          )}\n        />\n      </motion.div>\n    </motion.div>\n  );\n}\n\nconst Page = () => {\n  const t = useTranslations();\n\n  const fadeUpVariants = {\n    hidden: { opacity: 0, y: 30 },\n    visible: (i) => ({\n      opacity: 1,\n      y: 0,\n      transition: {\n        delay: i * 0.2,\n        duration: 0.8,\n        ease: [0.23, 0.86, 0.39, 0.96],\n      },\n    }),\n  };\n\n  return (\n    <div className=\"relative min-h-screen w-full flex items-center justify-center overflow-hidden bg-background\">\n      <div className=\"absolute inset-0 bg-gradient-to-br from-indigo-500/[0.05] via-transparent to-rose-500/[0.05] blur-3xl\" />\n\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <ElegantShape\n          delay={0.3}\n          width={600}\n          height={140}\n          rotate={12}\n          gradient=\"from-indigo-500/[0.15]\"\n          className=\"left-[-10%] md:left-[-5%] top-[15%] md:top-[20%]\"\n        />\n\n        <ElegantShape\n          delay={0.5}\n          width={500}\n          height={120}\n          rotate={-15}\n          gradient=\"from-rose-500/[0.15]\"\n          className=\"right-[-5%] md:right-[0%] top-[70%] md:top-[75%]\"\n        />\n\n        <ElegantShape\n          delay={0.4}\n          width={300}\n          height={80}\n          rotate={-8}\n          gradient=\"from-violet-500/[0.15]\"\n          className=\"left-[5%] md:left-[10%] bottom-[5%] md:bottom-[10%]\"\n        />\n\n        <ElegantShape\n          delay={0.6}\n          width={200}\n          height={60}\n          rotate={20}\n          gradient=\"from-amber-500/[0.15]\"\n          className=\"right-[15%] md:right-[20%] top-[10%] md:top-[15%]\"\n        />\n\n        <ElegantShape\n          delay={0.7}\n          width={150}\n          height={40}\n          rotate={-25}\n          gradient=\"from-cyan-500/[0.15]\"\n          className=\"left-[20%] md:left-[25%] top-[5%] md:top-[10%]\"\n        />\n      </div>\n\n      <div className=\"relative z-10 container mx-auto px-4 md:px-6\">\n        <div className=\"max-w-3xl mx-auto text-center\">\n          <motion.div\n            custom={0}\n            variants={fadeUpVariants}\n            initial=\"hidden\"\n            animate=\"visible\"\n            className=\"inline-flex items-center gap-2 px-3 py-1 rounded-full bg-white/[0.03] border border-white/[0.08] mb-8 md:mb-12\"\n          >\n            <Circle className=\"h-2 w-2 fill-primary text-primary\" />\n            <span className=\"text-sm text-white/60 tracking-wide\">\n              Document Analysis\n            </span>\n          </motion.div>\n\n          <motion.div\n            custom={1}\n            variants={fadeUpVariants}\n            initial=\"hidden\"\n            animate=\"visible\"\n          >\n            <h1 className=\"text-4xl sm:text-6xl md:text-8xl font-bold mb-6 md:mb-8 tracking-tight\">\n              <span className=\"bg-clip-text text-transparent bg-gradient-to-b from-white to-white/80\">\n                {t('home.title')} -\n              </span>\n              <br />\n              <span className={cn(\n                \"bg-clip-text text-transparent bg-gradient-to-r from-primary via-white/90 to-primary\"\n              )}>\n                {t('home.subtitle')}\n              </span>\n            </h1>\n          </motion.div>\n\n          <motion.div\n            custom={2}\n            variants={fadeUpVariants}\n            initial=\"hidden\"\n            animate=\"visible\"\n          >\n            <p className=\"text-lg text-white/70 mb-8 leading-relaxed font-light tracking-wide max-w-xl mx-auto px-4\">\n              {t('home.description')}\n            </p>\n          </motion.div>\n\n          <div className=\"flex gap-4 justify-center\">\n            <motion.div\n              custom={3}\n              variants={fadeUpVariants}\n              initial=\"hidden\"\n              animate=\"visible\"\n            >\n              <a\n                href=\"/signup\"\n              >\n                <button\n                  className=\"text-lg rounded-full px-6 py-2 ring-1 ring-primary/40  text-white cursor-pointer transition-all hover:ring-primary/50 bg-transparent\"\n                >\n                  {t('auth.signUp')}\n                </button>\n              </a>\n            </motion.div>\n            <motion.div\n              custom={3}\n              variants={fadeUpVariants}\n              initial=\"hidden\"\n              animate=\"visible\"\n            >\n              <a\n                href=\"/home/<USER>\"\n              >\n                <button\n                  className=\"text-lg rounded-full px-6 py-2 ring-1 ring-primary/40 bg-gradient-to-br from-background to-primary/30 text-white cursor-pointer transition-all hover:ring-primary/50\"\n                >\n                  {t('charity.startNow')}\n                </button>\n              </a>\n            </motion.div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"absolute inset-0 bg-gradient-to-t from-[#030303] via-transparent to-[#030303]/80 pointer-events-none\" />\n    </div>\n  );\n};\n\nexport default Page;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOA,SAAS,aAAa,EACpB,SAAS,EACT,QAAQ,CAAC,EACT,QAAQ,GAAG,EACX,SAAS,GAAG,EACZ,SAAS,CAAC,EACV,WAAW,mBAAmB,EAC/B;IACC,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YACP,SAAS;YACT,GAAG,CAAC;YACJ,QAAQ,SAAS;QACnB;QACA,SAAS;YACP,SAAS;YACT,GAAG;YACH,QAAQ;QACV;QACA,YAAY;YACV,UAAU;YACV;YACA,MAAM;gBAAC;gBAAM;gBAAM;gBAAM;aAAK;YAC9B,SAAS;gBAAE,UAAU;YAAI;QAC3B;QACA,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;kBAE1B,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBACP,GAAG;oBAAC;oBAAG;oBAAI;iBAAE;YACf;YACA,YAAY;gBACV,UAAU;gBACV,QAAQ,OAAO,iBAAiB;gBAChC,MAAM;YACR;YACA,OAAO;gBACL;gBACA;YACF;YACA,WAAU;sBAEV,cAAA,8OAAC;gBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iCACA,mCACA,UACA,oDACA,mDACA;;;;;;;;;;;;;;;;AAMZ;AAEA,MAAM,OAAO;IACX,MAAM,IAAI,CAAA,GAAA,mIAAA,CAAA,kBAAe,AAAD;IAExB,MAAM,iBAAiB;QACrB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS,CAAC,IAAM,CAAC;gBACf,SAAS;gBACT,GAAG;gBACH,YAAY;oBACV,OAAO,IAAI;oBACX,UAAU;oBACV,MAAM;wBAAC;wBAAM;wBAAM;wBAAM;qBAAK;gBAChC;YACF,CAAC;IACH;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;;;;;0BAEf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,OAAO;wBACP,OAAO;wBACP,QAAQ;wBACR,QAAQ;wBACR,UAAS;wBACT,WAAU;;;;;;kCAGZ,8OAAC;wBACC,OAAO;wBACP,OAAO;wBACP,QAAQ;wBACR,QAAQ,CAAC;wBACT,UAAS;wBACT,WAAU;;;;;;kCAGZ,8OAAC;wBACC,OAAO;wBACP,OAAO;wBACP,QAAQ;wBACR,QAAQ,CAAC;wBACT,UAAS;wBACT,WAAU;;;;;;kCAGZ,8OAAC;wBACC,OAAO;wBACP,OAAO;wBACP,QAAQ;wBACR,QAAQ;wBACR,UAAS;wBACT,WAAU;;;;;;kCAGZ,8OAAC;wBACC,OAAO;wBACP,OAAO;wBACP,QAAQ;wBACR,QAAQ,CAAC;wBACT,UAAS;wBACT,WAAU;;;;;;;;;;;;0BAId,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,QAAQ;4BACR,UAAU;4BACV,SAAQ;4BACR,SAAQ;4BACR,WAAU;;8CAEV,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,8OAAC;oCAAK,WAAU;8CAAsC;;;;;;;;;;;;sCAKxD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,QAAQ;4BACR,UAAU;4BACV,SAAQ;4BACR,SAAQ;sCAER,cAAA,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAK,WAAU;;4CACb,EAAE;4CAAc;;;;;;;kDAEnB,8OAAC;;;;;kDACD,8OAAC;wCAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAChB;kDAEC,EAAE;;;;;;;;;;;;;;;;;sCAKT,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,QAAQ;4BACR,UAAU;4BACV,SAAQ;4BACR,SAAQ;sCAER,cAAA,8OAAC;gCAAE,WAAU;0CACV,EAAE;;;;;;;;;;;sCAIP,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,QAAQ;oCACR,UAAU;oCACV,SAAQ;oCACR,SAAQ;8CAER,cAAA,8OAAC;wCACC,MAAK;kDAEL,cAAA,8OAAC;4CACC,WAAU;sDAET,EAAE;;;;;;;;;;;;;;;;8CAIT,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,QAAQ;oCACR,UAAU;oCACV,SAAQ;oCACR,SAAQ;8CAER,cAAA,8OAAC;wCACC,MAAK;kDAEL,cAAA,8OAAC;4CACC,WAAU;sDAET,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQf,8OAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;uCAEe", "debugId": null}}]}