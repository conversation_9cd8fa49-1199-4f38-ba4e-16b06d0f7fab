const CHUNK_PUBLIC_PATH = "server/app/home/<USER>/new/page.js";
const runtime = require("../../../../chunks/ssr/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_0962b827._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__4c58c055._.js");
runtime.loadChunk("server/chunks/ssr/src_app_a4430781._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__b581052d._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_96715ba7._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_client_components_forbidden-error_ea7ea172.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_client_components_unauthorized-error_c8949b27.js");
runtime.loadChunk("server/chunks/ssr/_2c5903c8._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_3ac035e4._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_0666b04c._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_zod_v3_020e7f8a._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_better-call_dist_index_21ba3623.js");
runtime.loadChunk("server/chunks/ssr/node_modules_better-auth_dist_shared_856e6e8a._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_better-auth_dist_plugins_7aacf88f._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_better-auth_dist_58a41214._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_jose_dist_webapi_be919571._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_kysely_dist_esm_85f6825d._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_ee47ef66._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__3b10ffe9._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/home/<USER>/new/page/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-page.js?page=/home/<USER>/new/page { METADATA_0 => \"[project]/src/app/favicon.ico.mjs { IMAGE => \\\"[project]/src/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js server component)\", MODULE_1 => \"[project]/src/app/layout.jsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_2 => \"[project]/node_modules/next/dist/client/components/not-found-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_3 => \"[project]/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_4 => \"[project]/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_5 => \"[project]/src/app/home/<USER>", MODULE_6 => \"[project]/src/app/home/<USER>/new/page.jsx [app-rsc] (ecmascript, Next.js server component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-page.js?page=/home/<USER>/new/page { METADATA_0 => \"[project]/src/app/favicon.ico.mjs { IMAGE => \\\"[project]/src/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js server component)\", MODULE_1 => \"[project]/src/app/layout.jsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_2 => \"[project]/node_modules/next/dist/client/components/not-found-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_3 => \"[project]/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_4 => \"[project]/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_5 => \"[project]/src/app/home/<USER>", MODULE_6 => \"[project]/src/app/home/<USER>/new/page.jsx [app-rsc] (ecmascript, Next.js server component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
