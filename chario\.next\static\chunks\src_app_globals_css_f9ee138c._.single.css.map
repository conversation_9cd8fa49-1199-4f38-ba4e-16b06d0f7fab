{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/globals.css"], "sourcesContent": ["/*! tailwindcss v4.1.11 | MIT License | https://tailwindcss.com */\n@layer properties;\n.\\@container\\/card-header {\n  container-type: inline-size;\n  container-name: card-header;\n}\n.pointer-events-none {\n  pointer-events: none;\n}\n.visible {\n  visibility: visible;\n}\n.sr-only {\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  margin: -1px;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  white-space: nowrap;\n  border-width: 0;\n}\n.absolute {\n  position: absolute;\n}\n.fixed {\n  position: fixed;\n}\n.relative {\n  position: relative;\n}\n.inset-0 {\n  inset: calc(var(--spacing) * 0);\n}\n.inset-x-0 {\n  inset-inline: calc(var(--spacing) * 0);\n}\n.inset-y-0 {\n  inset-block: calc(var(--spacing) * 0);\n}\n.-top-1 {\n  top: calc(var(--spacing) * -1);\n}\n.-top-2 {\n  top: calc(var(--spacing) * -2);\n}\n.-top-4 {\n  top: calc(var(--spacing) * -4);\n}\n.top-0 {\n  top: calc(var(--spacing) * 0);\n}\n.top-0\\.5 {\n  top: calc(var(--spacing) * 0.5);\n}\n.top-1\\.5 {\n  top: calc(var(--spacing) * 1.5);\n}\n.top-1\\/2 {\n  top: calc(1/2 * 100%);\n}\n.top-2 {\n  top: calc(var(--spacing) * 2);\n}\n.top-3\\.5 {\n  top: calc(var(--spacing) * 3.5);\n}\n.top-4 {\n  top: calc(var(--spacing) * 4);\n}\n.top-\\[5\\%\\] {\n  top: 5%;\n}\n.top-\\[10\\%\\] {\n  top: 10%;\n}\n.top-\\[15\\%\\] {\n  top: 15%;\n}\n.top-\\[50\\%\\] {\n  top: 50%;\n}\n.top-\\[70\\%\\] {\n  top: 70%;\n}\n.-right-1 {\n  right: calc(var(--spacing) * -1);\n}\n.right-0 {\n  right: calc(var(--spacing) * 0);\n}\n.right-0\\.5 {\n  right: calc(var(--spacing) * 0.5);\n}\n.right-1 {\n  right: calc(var(--spacing) * 1);\n}\n.right-2 {\n  right: calc(var(--spacing) * 2);\n}\n.right-3 {\n  right: calc(var(--spacing) * 3);\n}\n.right-4 {\n  right: calc(var(--spacing) * 4);\n}\n.right-\\[-5\\%\\] {\n  right: -5%;\n}\n.right-\\[15\\%\\] {\n  right: 15%;\n}\n.bottom-0 {\n  bottom: calc(var(--spacing) * 0);\n}\n.bottom-\\[5\\%\\] {\n  bottom: 5%;\n}\n.-left-2 {\n  left: calc(var(--spacing) * -2);\n}\n.left-0 {\n  left: calc(var(--spacing) * 0);\n}\n.left-1\\/2 {\n  left: calc(1/2 * 100%);\n}\n.left-2 {\n  left: calc(var(--spacing) * 2);\n}\n.left-3 {\n  left: calc(var(--spacing) * 3);\n}\n.left-\\[-10\\%\\] {\n  left: -10%;\n}\n.left-\\[5\\%\\] {\n  left: 5%;\n}\n.left-\\[20\\%\\] {\n  left: 20%;\n}\n.left-\\[50\\%\\] {\n  left: 50%;\n}\n.z-0 {\n  z-index: 0;\n}\n.z-10 {\n  z-index: 10;\n}\n.z-20 {\n  z-index: 20;\n}\n.z-50 {\n  z-index: 50;\n}\n.col-start-2 {\n  grid-column-start: 2;\n}\n.row-span-2 {\n  grid-row: span 2 / span 2;\n}\n.row-start-1 {\n  grid-row-start: 1;\n}\n.container {\n  width: 100%;\n  @media (width >= 40rem) {\n    max-width: 40rem;\n  }\n  @media (width >= 48rem) {\n    max-width: 48rem;\n  }\n  @media (width >= 64rem) {\n    max-width: 64rem;\n  }\n  @media (width >= 80rem) {\n    max-width: 80rem;\n  }\n  @media (width >= 96rem) {\n    max-width: 96rem;\n  }\n}\n.m-3 {\n  margin: calc(var(--spacing) * 3);\n}\n.-mx-1 {\n  margin-inline: calc(var(--spacing) * -1);\n}\n.mx-2 {\n  margin-inline: calc(var(--spacing) * 2);\n}\n.mx-3\\.5 {\n  margin-inline: calc(var(--spacing) * 3.5);\n}\n.mx-auto {\n  margin-inline: auto;\n}\n.my-0\\.5 {\n  margin-block: calc(var(--spacing) * 0.5);\n}\n.my-1 {\n  margin-block: calc(var(--spacing) * 1);\n}\n.-mt-2 {\n  margin-top: calc(var(--spacing) * -2);\n}\n.mt-1 {\n  margin-top: calc(var(--spacing) * 1);\n}\n.mt-2 {\n  margin-top: calc(var(--spacing) * 2);\n}\n.mt-3 {\n  margin-top: calc(var(--spacing) * 3);\n}\n.mt-4 {\n  margin-top: calc(var(--spacing) * 4);\n}\n.mt-6 {\n  margin-top: calc(var(--spacing) * 6);\n}\n.mt-8 {\n  margin-top: calc(var(--spacing) * 8);\n}\n.mt-auto {\n  margin-top: auto;\n}\n.-mr-2 {\n  margin-right: calc(var(--spacing) * -2);\n}\n.mr-1 {\n  margin-right: calc(var(--spacing) * 1);\n}\n.mb-0 {\n  margin-bottom: calc(var(--spacing) * 0);\n}\n.mb-0\\.5 {\n  margin-bottom: calc(var(--spacing) * 0.5);\n}\n.mb-1 {\n  margin-bottom: calc(var(--spacing) * 1);\n}\n.mb-2 {\n  margin-bottom: calc(var(--spacing) * 2);\n}\n.mb-3 {\n  margin-bottom: calc(var(--spacing) * 3);\n}\n.mb-4 {\n  margin-bottom: calc(var(--spacing) * 4);\n}\n.mb-5 {\n  margin-bottom: calc(var(--spacing) * 5);\n}\n.mb-6 {\n  margin-bottom: calc(var(--spacing) * 6);\n}\n.mb-8 {\n  margin-bottom: calc(var(--spacing) * 8);\n}\n.mb-16 {\n  margin-bottom: calc(var(--spacing) * 16);\n}\n.-ml-1 {\n  margin-left: calc(var(--spacing) * -1);\n}\n.-ml-2 {\n  margin-left: calc(var(--spacing) * -2);\n}\n.ml-2 {\n  margin-left: calc(var(--spacing) * 2);\n}\n.ml-3 {\n  margin-left: calc(var(--spacing) * 3);\n}\n.ml-60 {\n  margin-left: calc(var(--spacing) * 60);\n}\n.ml-auto {\n  margin-left: auto;\n}\n.line-clamp-1 {\n  overflow: hidden;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 1;\n}\n.line-clamp-2 {\n  overflow: hidden;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 2;\n}\n.line-clamp-4 {\n  overflow: hidden;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 4;\n}\n.block {\n  display: block;\n}\n.flex {\n  display: flex;\n}\n.grid {\n  display: grid;\n}\n.hidden {\n  display: none;\n}\n.inline-block {\n  display: inline-block;\n}\n.inline-flex {\n  display: inline-flex;\n}\n.table {\n  display: table;\n}\n.table-caption {\n  display: table-caption;\n}\n.table-cell {\n  display: table-cell;\n}\n.table-row {\n  display: table-row;\n}\n.field-sizing-content {\n  field-sizing: content;\n}\n.aspect-auto {\n  aspect-ratio: auto;\n}\n.aspect-square {\n  aspect-ratio: 1 / 1;\n}\n.aspect-video {\n  aspect-ratio: var(--aspect-video);\n}\n.size-2 {\n  width: calc(var(--spacing) * 2);\n  height: calc(var(--spacing) * 2);\n}\n.size-2\\.5 {\n  width: calc(var(--spacing) * 2.5);\n  height: calc(var(--spacing) * 2.5);\n}\n.size-3\\.5 {\n  width: calc(var(--spacing) * 3.5);\n  height: calc(var(--spacing) * 3.5);\n}\n.size-4 {\n  width: calc(var(--spacing) * 4);\n  height: calc(var(--spacing) * 4);\n}\n.size-7 {\n  width: calc(var(--spacing) * 7);\n  height: calc(var(--spacing) * 7);\n}\n.size-9 {\n  width: calc(var(--spacing) * 9);\n  height: calc(var(--spacing) * 9);\n}\n.size-full {\n  width: 100%;\n  height: 100%;\n}\n.h-1 {\n  height: calc(var(--spacing) * 1);\n}\n.h-1\\.5 {\n  height: calc(var(--spacing) * 1.5);\n}\n.h-2 {\n  height: calc(var(--spacing) * 2);\n}\n.h-2\\.5 {\n  height: calc(var(--spacing) * 2.5);\n}\n.h-4 {\n  height: calc(var(--spacing) * 4);\n}\n.h-5 {\n  height: calc(var(--spacing) * 5);\n}\n.h-6 {\n  height: calc(var(--spacing) * 6);\n}\n.h-7 {\n  height: calc(var(--spacing) * 7);\n}\n.h-8 {\n  height: calc(var(--spacing) * 8);\n}\n.h-9 {\n  height: calc(var(--spacing) * 9);\n}\n.h-10 {\n  height: calc(var(--spacing) * 10);\n}\n.h-12 {\n  height: calc(var(--spacing) * 12);\n}\n.h-16 {\n  height: calc(var(--spacing) * 16);\n}\n.h-24 {\n  height: calc(var(--spacing) * 24);\n}\n.h-48 {\n  height: calc(var(--spacing) * 48);\n}\n.h-72 {\n  height: calc(var(--spacing) * 72);\n}\n.h-80 {\n  height: calc(var(--spacing) * 80);\n}\n.h-\\[1\\.15rem\\] {\n  height: 1.15rem;\n}\n.h-\\[250px\\] {\n  height: 250px;\n}\n.h-\\[var\\(--radix-select-trigger-height\\)\\] {\n  height: var(--radix-select-trigger-height);\n}\n.h-auto {\n  height: auto;\n}\n.h-full {\n  height: 100%;\n}\n.h-max {\n  height: max-content;\n}\n.h-px {\n  height: 1px;\n}\n.h-screen {\n  height: 100vh;\n}\n.h-svh {\n  height: 100svh;\n}\n.max-h-\\(--radix-dropdown-menu-content-available-height\\) {\n  max-height: var(--radix-dropdown-menu-content-available-height);\n}\n.max-h-\\(--radix-select-content-available-height\\) {\n  max-height: var(--radix-select-content-available-height);\n}\n.max-h-1 {\n  max-height: calc(var(--spacing) * 1);\n}\n.max-h-8 {\n  max-height: calc(var(--spacing) * 8);\n}\n.max-h-60 {\n  max-height: calc(var(--spacing) * 60);\n}\n.max-h-\\[300px\\] {\n  max-height: 300px;\n}\n.max-h-\\[400px\\] {\n  max-height: 400px;\n}\n.min-h-0 {\n  min-height: calc(var(--spacing) * 0);\n}\n.min-h-8 {\n  min-height: calc(var(--spacing) * 8);\n}\n.min-h-16 {\n  min-height: calc(var(--spacing) * 16);\n}\n.min-h-\\[24rem\\] {\n  min-height: 24rem;\n}\n.min-h-\\[120px\\] {\n  min-height: 120px;\n}\n.min-h-\\[400px\\] {\n  min-height: 400px;\n}\n.min-h-screen {\n  min-height: 100vh;\n}\n.min-h-svh {\n  min-height: 100svh;\n}\n.w-\\(--sidebar-width\\) {\n  width: var(--sidebar-width);\n}\n.w-0 {\n  width: calc(var(--spacing) * 0);\n}\n.w-1 {\n  width: calc(var(--spacing) * 1);\n}\n.w-1\\/2 {\n  width: calc(1/2 * 100%);\n}\n.w-2 {\n  width: calc(var(--spacing) * 2);\n}\n.w-2\\.5 {\n  width: calc(var(--spacing) * 2.5);\n}\n.w-2\\/3 {\n  width: calc(2/3 * 100%);\n}\n.w-3\\/4 {\n  width: calc(3/4 * 100%);\n}\n.w-4 {\n  width: calc(var(--spacing) * 4);\n}\n.w-5 {\n  width: calc(var(--spacing) * 5);\n}\n.w-6 {\n  width: calc(var(--spacing) * 6);\n}\n.w-8 {\n  width: calc(var(--spacing) * 8);\n}\n.w-10 {\n  width: calc(var(--spacing) * 10);\n}\n.w-12 {\n  width: calc(var(--spacing) * 12);\n}\n.w-16 {\n  width: calc(var(--spacing) * 16);\n}\n.w-20 {\n  width: calc(var(--spacing) * 20);\n}\n.w-60 {\n  width: calc(var(--spacing) * 60);\n}\n.w-72 {\n  width: calc(var(--spacing) * 72);\n}\n.w-\\[30rem\\] {\n  width: 30rem;\n}\n.w-\\[160px\\] {\n  width: 160px;\n}\n.w-\\[180px\\] {\n  width: 180px;\n}\n.w-\\[300px\\] {\n  width: 300px;\n}\n.w-\\[400px\\] {\n  width: 400px;\n}\n.w-auto {\n  width: auto;\n}\n.w-fit {\n  width: fit-content;\n}\n.w-full {\n  width: 100%;\n}\n.w-max {\n  width: max-content;\n}\n.w-min {\n  width: min-content;\n}\n.w-screen {\n  width: 100vw;\n}\n.max-w-\\(--skeleton-width\\) {\n  max-width: var(--skeleton-width);\n}\n.max-w-3xl {\n  max-width: var(--container-3xl);\n}\n.max-w-6xl {\n  max-width: var(--container-6xl);\n}\n.max-w-7xl {\n  max-width: var(--container-7xl);\n}\n.max-w-8 {\n  max-width: calc(var(--spacing) * 8);\n}\n.max-w-96 {\n  max-width: calc(var(--spacing) * 96);\n}\n.max-w-\\[600px\\] {\n  max-width: 600px;\n}\n.max-w-\\[1200px\\] {\n  max-width: 1200px;\n}\n.max-w-\\[calc\\(100\\%-2rem\\)\\] {\n  max-width: calc(100% - 2rem);\n}\n.max-w-lg {\n  max-width: var(--container-lg);\n}\n.max-w-md {\n  max-width: var(--container-md);\n}\n.max-w-sm {\n  max-width: var(--container-sm);\n}\n.max-w-xl {\n  max-width: var(--container-xl);\n}\n.min-w-0 {\n  min-width: calc(var(--spacing) * 0);\n}\n.min-w-5 {\n  min-width: calc(var(--spacing) * 5);\n}\n.min-w-8 {\n  min-width: calc(var(--spacing) * 8);\n}\n.min-w-96 {\n  min-width: calc(var(--spacing) * 96);\n}\n.min-w-\\[8rem\\] {\n  min-width: 8rem;\n}\n.min-w-\\[100px\\] {\n  min-width: 100px;\n}\n.min-w-\\[var\\(--radix-select-trigger-width\\)\\] {\n  min-width: var(--radix-select-trigger-width);\n}\n.flex-1 {\n  flex: 1;\n}\n.flex-shrink-0 {\n  flex-shrink: 0;\n}\n.shrink-0 {\n  flex-shrink: 0;\n}\n.flex-grow {\n  flex-grow: 1;\n}\n.caption-bottom {\n  caption-side: bottom;\n}\n.origin-\\(--radix-dropdown-menu-content-transform-origin\\) {\n  transform-origin: var(--radix-dropdown-menu-content-transform-origin);\n}\n.origin-\\(--radix-popover-content-transform-origin\\) {\n  transform-origin: var(--radix-popover-content-transform-origin);\n}\n.origin-\\(--radix-select-content-transform-origin\\) {\n  transform-origin: var(--radix-select-content-transform-origin);\n}\n.origin-\\(--radix-tooltip-content-transform-origin\\) {\n  transform-origin: var(--radix-tooltip-content-transform-origin);\n}\n.-translate-x-1\\/2 {\n  --tw-translate-x: calc(calc(1/2 * 100%) * -1);\n  translate: var(--tw-translate-x) var(--tw-translate-y);\n}\n.-translate-x-px {\n  --tw-translate-x: -1px;\n  translate: var(--tw-translate-x) var(--tw-translate-y);\n}\n.translate-x-\\[-50\\%\\] {\n  --tw-translate-x: -50%;\n  translate: var(--tw-translate-x) var(--tw-translate-y);\n}\n.translate-x-px {\n  --tw-translate-x: 1px;\n  translate: var(--tw-translate-x) var(--tw-translate-y);\n}\n.-translate-y-1\\/2 {\n  --tw-translate-y: calc(calc(1/2 * 100%) * -1);\n  translate: var(--tw-translate-x) var(--tw-translate-y);\n}\n.translate-y-\\[-50\\%\\] {\n  --tw-translate-y: -50%;\n  translate: var(--tw-translate-x) var(--tw-translate-y);\n}\n.translate-y-\\[calc\\(-50\\%_-_2px\\)\\] {\n  --tw-translate-y: calc(-50% - 2px);\n  translate: var(--tw-translate-x) var(--tw-translate-y);\n}\n.rotate-45 {\n  rotate: 45deg;\n}\n.rotate-180 {\n  rotate: 180deg;\n}\n.transform {\n  transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);\n}\n.animate-in {\n  animation: enter var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,none);\n}\n.animate-pulse {\n  animation: var(--animate-pulse);\n}\n.animate-spin {\n  animation: var(--animate-spin);\n}\n.cursor-default {\n  cursor: default;\n}\n.cursor-pointer {\n  cursor: pointer;\n}\n.cursor-text {\n  cursor: text;\n}\n.touch-none {\n  touch-action: none;\n}\n.scroll-my-1 {\n  scroll-margin-block: calc(var(--spacing) * 1);\n}\n.scroll-py-1 {\n  scroll-padding-block: calc(var(--spacing) * 1);\n}\n.list-disc {\n  list-style-type: disc;\n}\n.auto-rows-min {\n  grid-auto-rows: min-content;\n}\n.grid-cols-1 {\n  grid-template-columns: repeat(1, minmax(0, 1fr));\n}\n.grid-cols-2 {\n  grid-template-columns: repeat(2, minmax(0, 1fr));\n}\n.grid-rows-\\[auto_auto\\] {\n  grid-template-rows: auto auto;\n}\n.flex-col {\n  flex-direction: column;\n}\n.flex-col-reverse {\n  flex-direction: column-reverse;\n}\n.flex-row {\n  flex-direction: row;\n}\n.flex-wrap {\n  flex-wrap: wrap;\n}\n.items-center {\n  align-items: center;\n}\n.items-end {\n  align-items: flex-end;\n}\n.items-start {\n  align-items: flex-start;\n}\n.items-stretch {\n  align-items: stretch;\n}\n.justify-between {\n  justify-content: space-between;\n}\n.justify-center {\n  justify-content: center;\n}\n.justify-end {\n  justify-content: flex-end;\n}\n.gap-1 {\n  gap: calc(var(--spacing) * 1);\n}\n.gap-1\\.5 {\n  gap: calc(var(--spacing) * 1.5);\n}\n.gap-2 {\n  gap: calc(var(--spacing) * 2);\n}\n.gap-3 {\n  gap: calc(var(--spacing) * 3);\n}\n.gap-4 {\n  gap: calc(var(--spacing) * 4);\n}\n.gap-6 {\n  gap: calc(var(--spacing) * 6);\n}\n.gap-8 {\n  gap: calc(var(--spacing) * 8);\n}\n.space-y-0 {\n  :where(& > :not(:last-child)) {\n    --tw-space-y-reverse: 0;\n    margin-block-start: calc(calc(var(--spacing) * 0) * var(--tw-space-y-reverse));\n    margin-block-end: calc(calc(var(--spacing) * 0) * calc(1 - var(--tw-space-y-reverse)));\n  }\n}\n.space-y-1 {\n  :where(& > :not(:last-child)) {\n    --tw-space-y-reverse: 0;\n    margin-block-start: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));\n    margin-block-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));\n  }\n}\n.space-y-2 {\n  :where(& > :not(:last-child)) {\n    --tw-space-y-reverse: 0;\n    margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));\n    margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));\n  }\n}\n.space-y-3 {\n  :where(& > :not(:last-child)) {\n    --tw-space-y-reverse: 0;\n    margin-block-start: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse));\n    margin-block-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse)));\n  }\n}\n.space-y-4 {\n  :where(& > :not(:last-child)) {\n    --tw-space-y-reverse: 0;\n    margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));\n    margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));\n  }\n}\n.space-y-6 {\n  :where(& > :not(:last-child)) {\n    --tw-space-y-reverse: 0;\n    margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));\n    margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));\n  }\n}\n.gap-x-1 {\n  column-gap: calc(var(--spacing) * 1);\n}\n.gap-x-2 {\n  column-gap: calc(var(--spacing) * 2);\n}\n.space-x-2 {\n  :where(& > :not(:last-child)) {\n    --tw-space-x-reverse: 0;\n    margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));\n    margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));\n  }\n}\n.self-start {\n  align-self: flex-start;\n}\n.justify-self-end {\n  justify-self: flex-end;\n}\n.overflow-auto {\n  overflow: auto;\n}\n.overflow-hidden {\n  overflow: hidden;\n}\n.overflow-x-auto {\n  overflow-x: auto;\n}\n.overflow-x-hidden {\n  overflow-x: hidden;\n}\n.overflow-y-auto {\n  overflow-y: auto;\n}\n.rounded {\n  border-radius: 0.25rem;\n}\n.rounded-3xl {\n  border-radius: var(--radius-3xl);\n}\n.rounded-\\[2px\\] {\n  border-radius: 2px;\n}\n.rounded-\\[inherit\\] {\n  border-radius: inherit;\n}\n.rounded-full {\n  border-radius: calc(infinity * 1px);\n}\n.rounded-lg {\n  border-radius: var(--radius);\n}\n.rounded-md {\n  border-radius: calc(var(--radius) - 2px);\n}\n.rounded-sm {\n  border-radius: calc(var(--radius) - 4px);\n}\n.rounded-xl {\n  border-radius: calc(var(--radius) + 4px);\n}\n.rounded-xs {\n  border-radius: var(--radius-xs);\n}\n.rounded-s-lg {\n  border-start-start-radius: var(--radius);\n  border-end-start-radius: var(--radius);\n}\n.rounded-s-none {\n  border-start-start-radius: 0;\n  border-end-start-radius: 0;\n}\n.rounded-e-lg {\n  border-start-end-radius: var(--radius);\n  border-end-end-radius: var(--radius);\n}\n.rounded-e-none {\n  border-start-end-radius: 0;\n  border-end-end-radius: 0;\n}\n.rounded-t-3xl {\n  border-top-left-radius: var(--radius-3xl);\n  border-top-right-radius: var(--radius-3xl);\n}\n.rounded-t-lg {\n  border-top-left-radius: var(--radius);\n  border-top-right-radius: var(--radius);\n}\n.rounded-t-none {\n  border-top-left-radius: 0;\n  border-top-right-radius: 0;\n}\n.rounded-b-4xl {\n  border-bottom-right-radius: var(--radius-4xl);\n  border-bottom-left-radius: var(--radius-4xl);\n}\n.border {\n  border-style: var(--tw-border-style);\n  border-width: 1px;\n}\n.border-2 {\n  border-style: var(--tw-border-style);\n  border-width: 2px;\n}\n.border-4 {\n  border-style: var(--tw-border-style);\n  border-width: 4px;\n}\n.border-\\[1\\.5px\\] {\n  border-style: var(--tw-border-style);\n  border-width: 1.5px;\n}\n.border-y {\n  border-block-style: var(--tw-border-style);\n  border-block-width: 1px;\n}\n.border-t {\n  border-top-style: var(--tw-border-style);\n  border-top-width: 1px;\n}\n.border-r {\n  border-right-style: var(--tw-border-style);\n  border-right-width: 1px;\n}\n.border-r-0 {\n  border-right-style: var(--tw-border-style);\n  border-right-width: 0px;\n}\n.border-b {\n  border-bottom-style: var(--tw-border-style);\n  border-bottom-width: 1px;\n}\n.border-l {\n  border-left-style: var(--tw-border-style);\n  border-left-width: 1px;\n}\n.border-dashed {\n  --tw-border-style: dashed;\n  border-style: dashed;\n}\n.border-\\(--color-border\\) {\n  border-color: var(--color-border);\n}\n.border-accent {\n  border-color: var(--accent);\n}\n.border-border {\n  border-color: var(--border);\n}\n.border-border\\/50 {\n  border-color: var(--border);\n  @supports (color: color-mix(in lab, red, red)) {\n    border-color: color-mix(in oklab, var(--border) 50%, transparent);\n  }\n}\n.border-card\\/40 {\n  border-color: var(--card);\n  @supports (color: color-mix(in lab, red, red)) {\n    border-color: color-mix(in oklab, var(--card) 40%, transparent);\n  }\n}\n.border-gray-300 {\n  border-color: var(--color-gray-300);\n}\n.border-input {\n  border-color: var(--input);\n}\n.border-primary {\n  border-color: var(--primary);\n}\n.border-sidebar-border {\n  border-color: var(--sidebar-border);\n}\n.border-transparent {\n  border-color: transparent;\n}\n.border-white\\/\\[0\\.08\\] {\n  border-color: color-mix(in srgb, #fff 8%, transparent);\n  @supports (color: color-mix(in lab, red, red)) {\n    border-color: color-mix(in oklab, var(--color-white) 8%, transparent);\n  }\n}\n.border-white\\/\\[0\\.15\\] {\n  border-color: color-mix(in srgb, #fff 15%, transparent);\n  @supports (color: color-mix(in lab, red, red)) {\n    border-color: color-mix(in oklab, var(--color-white) 15%, transparent);\n  }\n}\n.border-zinc-600 {\n  border-color: var(--color-zinc-600);\n}\n.border-zinc-700 {\n  border-color: var(--color-zinc-700);\n}\n.border-t-transparent {\n  border-top-color: transparent;\n}\n.border-l-transparent {\n  border-left-color: transparent;\n}\n.bg-\\(--color-bg\\) {\n  background-color: var(--color-bg);\n}\n.bg-accent {\n  background-color: var(--accent);\n}\n.bg-background {\n  background-color: var(--background);\n}\n.bg-black {\n  background-color: var(--color-black);\n}\n.bg-black\\/50 {\n  background-color: color-mix(in srgb, #000 50%, transparent);\n  @supports (color: color-mix(in lab, red, red)) {\n    background-color: color-mix(in oklab, var(--color-black) 50%, transparent);\n  }\n}\n.bg-blue-600 {\n  background-color: var(--color-blue-600);\n}\n.bg-border {\n  background-color: var(--border);\n}\n.bg-card {\n  background-color: var(--card);\n}\n.bg-card\\/50 {\n  background-color: var(--card);\n  @supports (color: color-mix(in lab, red, red)) {\n    background-color: color-mix(in oklab, var(--card) 50%, transparent);\n  }\n}\n.bg-destructive {\n  background-color: var(--destructive);\n}\n.bg-destructive\\/15 {\n  background-color: var(--destructive);\n  @supports (color: color-mix(in lab, red, red)) {\n    background-color: color-mix(in oklab, var(--destructive) 15%, transparent);\n  }\n}\n.bg-emerald-500 {\n  background-color: var(--color-emerald-500);\n}\n.bg-emerald-800 {\n  background-color: var(--color-emerald-800);\n}\n.bg-emerald-900 {\n  background-color: var(--color-emerald-900);\n}\n.bg-foreground\\/20 {\n  background-color: var(--foreground);\n  @supports (color: color-mix(in lab, red, red)) {\n    background-color: color-mix(in oklab, var(--foreground) 20%, transparent);\n  }\n}\n.bg-gray-700 {\n  background-color: var(--color-gray-700);\n}\n.bg-green-100 {\n  background-color: var(--color-green-100);\n}\n.bg-input {\n  background-color: var(--input);\n}\n.bg-muted {\n  background-color: var(--muted);\n}\n.bg-muted-foreground {\n  background-color: var(--muted-foreground);\n}\n.bg-muted\\/50 {\n  background-color: var(--muted);\n  @supports (color: color-mix(in lab, red, red)) {\n    background-color: color-mix(in oklab, var(--muted) 50%, transparent);\n  }\n}\n.bg-neutral-900 {\n  background-color: var(--color-neutral-900);\n}\n.bg-popover {\n  background-color: var(--popover);\n}\n.bg-primary {\n  background-color: var(--primary);\n}\n.bg-primary-foreground {\n  background-color: var(--primary-foreground);\n}\n.bg-primary\\/10 {\n  background-color: var(--primary);\n  @supports (color: color-mix(in lab, red, red)) {\n    background-color: color-mix(in oklab, var(--primary) 10%, transparent);\n  }\n}\n.bg-primary\\/20 {\n  background-color: var(--primary);\n  @supports (color: color-mix(in lab, red, red)) {\n    background-color: color-mix(in oklab, var(--primary) 20%, transparent);\n  }\n}\n.bg-red-900\\/50 {\n  background-color: color-mix(in srgb, oklch(39.6% 0.141 25.723) 50%, transparent);\n  @supports (color: color-mix(in lab, red, red)) {\n    background-color: color-mix(in oklab, var(--color-red-900) 50%, transparent);\n  }\n}\n.bg-secondary {\n  background-color: var(--secondary);\n}\n.bg-sidebar {\n  background-color: var(--sidebar);\n}\n.bg-sidebar-border {\n  background-color: var(--sidebar-border);\n}\n.bg-transparent {\n  background-color: transparent;\n}\n.bg-white\\/\\[0\\.03\\] {\n  background-color: color-mix(in srgb, #fff 3%, transparent);\n  @supports (color: color-mix(in lab, red, red)) {\n    background-color: color-mix(in oklab, var(--color-white) 3%, transparent);\n  }\n}\n.bg-yellow-500 {\n  background-color: var(--color-yellow-500);\n}\n.bg-zinc-600 {\n  background-color: var(--color-zinc-600);\n}\n.bg-zinc-700 {\n  background-color: var(--color-zinc-700);\n}\n.bg-zinc-800 {\n  background-color: var(--color-zinc-800);\n}\n.bg-gradient-to-b {\n  --tw-gradient-position: to bottom in oklab;\n  background-image: linear-gradient(var(--tw-gradient-stops));\n}\n.bg-gradient-to-br {\n  --tw-gradient-position: to bottom right in oklab;\n  background-image: linear-gradient(var(--tw-gradient-stops));\n}\n.bg-gradient-to-r {\n  --tw-gradient-position: to right in oklab;\n  background-image: linear-gradient(var(--tw-gradient-stops));\n}\n.bg-gradient-to-t {\n  --tw-gradient-position: to top in oklab;\n  background-image: linear-gradient(var(--tw-gradient-stops));\n}\n.bg-gradient-to-tr {\n  --tw-gradient-position: to top right in oklab;\n  background-image: linear-gradient(var(--tw-gradient-stops));\n}\n.from-\\[\\#030303\\] {\n  --tw-gradient-from: #030303;\n  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n}\n.from-amber-400 {\n  --tw-gradient-from: var(--color-amber-400);\n  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n}\n.from-amber-500\\/\\[0\\.15\\] {\n  --tw-gradient-from: color-mix(in srgb, oklch(76.9% 0.188 70.08) 15%, transparent);\n  @supports (color: color-mix(in lab, red, red)) {\n    --tw-gradient-from: color-mix(in oklab, var(--color-amber-500) 15%, transparent);\n  }\n  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n}\n.from-background {\n  --tw-gradient-from: var(--background);\n  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n}\n.from-cyan-500\\/\\[0\\.15\\] {\n  --tw-gradient-from: color-mix(in srgb, oklch(71.5% 0.143 215.221) 15%, transparent);\n  @supports (color: color-mix(in lab, red, red)) {\n    --tw-gradient-from: color-mix(in oklab, var(--color-cyan-500) 15%, transparent);\n  }\n  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n}\n.from-indigo-500\\/\\[0\\.05\\] {\n  --tw-gradient-from: color-mix(in srgb, oklch(58.5% 0.233 277.117) 5%, transparent);\n  @supports (color: color-mix(in lab, red, red)) {\n    --tw-gradient-from: color-mix(in oklab, var(--color-indigo-500) 5%, transparent);\n  }\n  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n}\n.from-indigo-500\\/\\[0\\.15\\] {\n  --tw-gradient-from: color-mix(in srgb, oklch(58.5% 0.233 277.117) 15%, transparent);\n  @supports (color: color-mix(in lab, red, red)) {\n    --tw-gradient-from: color-mix(in oklab, var(--color-indigo-500) 15%, transparent);\n  }\n  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n}\n.from-primary {\n  --tw-gradient-from: var(--primary);\n  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n}\n.from-rose-500\\/\\[0\\.15\\] {\n  --tw-gradient-from: color-mix(in srgb, oklch(64.5% 0.246 16.439) 15%, transparent);\n  @supports (color: color-mix(in lab, red, red)) {\n    --tw-gradient-from: color-mix(in oklab, var(--color-rose-500) 15%, transparent);\n  }\n  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n}\n.from-violet-500\\/\\[0\\.15\\] {\n  --tw-gradient-from: color-mix(in srgb, oklch(60.6% 0.25 292.717) 15%, transparent);\n  @supports (color: color-mix(in lab, red, red)) {\n    --tw-gradient-from: color-mix(in oklab, var(--color-violet-500) 15%, transparent);\n  }\n  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n}\n.from-white {\n  --tw-gradient-from: var(--color-white);\n  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n}\n.from-white\\/\\[0\\.08\\] {\n  --tw-gradient-from: color-mix(in srgb, #fff 8%, transparent);\n  @supports (color: color-mix(in lab, red, red)) {\n    --tw-gradient-from: color-mix(in oklab, var(--color-white) 8%, transparent);\n  }\n  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n}\n.via-transparent {\n  --tw-gradient-via: transparent;\n  --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-via-stops);\n}\n.via-white\\/90 {\n  --tw-gradient-via: color-mix(in srgb, #fff 90%, transparent);\n  @supports (color: color-mix(in lab, red, red)) {\n    --tw-gradient-via: color-mix(in oklab, var(--color-white) 90%, transparent);\n  }\n  --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-via-stops);\n}\n.to-\\[\\#030303\\]\\/80 {\n  --tw-gradient-to: color-mix(in oklab, #030303 80%, transparent);\n  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n}\n.to-amber-600 {\n  --tw-gradient-to: var(--color-amber-600);\n  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n}\n.to-primary {\n  --tw-gradient-to: var(--primary);\n  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n}\n.to-primary\\/30 {\n  --tw-gradient-to: var(--primary);\n  @supports (color: color-mix(in lab, red, red)) {\n    --tw-gradient-to: color-mix(in oklab, var(--primary) 30%, transparent);\n  }\n  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n}\n.to-rose-500\\/\\[0\\.05\\] {\n  --tw-gradient-to: color-mix(in srgb, oklch(64.5% 0.246 16.439) 5%, transparent);\n  @supports (color: color-mix(in lab, red, red)) {\n    --tw-gradient-to: color-mix(in oklab, var(--color-rose-500) 5%, transparent);\n  }\n  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n}\n.to-transparent {\n  --tw-gradient-to: transparent;\n  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n}\n.to-white\\/80 {\n  --tw-gradient-to: color-mix(in srgb, #fff 80%, transparent);\n  @supports (color: color-mix(in lab, red, red)) {\n    --tw-gradient-to: color-mix(in oklab, var(--color-white) 80%, transparent);\n  }\n  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n}\n.bg-\\[length\\:250\\%_100\\%\\,auto\\] {\n  background-size: 250% 100%,auto;\n}\n.bg-clip-text {\n  background-clip: text;\n}\n.\\[background-repeat\\:no-repeat\\,padding-box\\] {\n  background-repeat: no-repeat,padding-box;\n}\n.fill-current {\n  fill: currentcolor;\n}\n.fill-primary {\n  fill: var(--primary);\n}\n.object-cover {\n  object-fit: cover;\n}\n.p-0 {\n  padding: calc(var(--spacing) * 0);\n}\n.p-0\\.5 {\n  padding: calc(var(--spacing) * 0.5);\n}\n.p-1 {\n  padding: calc(var(--spacing) * 1);\n}\n.p-2 {\n  padding: calc(var(--spacing) * 2);\n}\n.p-3 {\n  padding: calc(var(--spacing) * 3);\n}\n.p-4 {\n  padding: calc(var(--spacing) * 4);\n}\n.p-6 {\n  padding: calc(var(--spacing) * 6);\n}\n.p-8 {\n  padding: calc(var(--spacing) * 8);\n}\n.p-\\[4px\\] {\n  padding: 4px;\n}\n.p-px {\n  padding: 1px;\n}\n.px-1 {\n  padding-inline: calc(var(--spacing) * 1);\n}\n.px-2 {\n  padding-inline: calc(var(--spacing) * 2);\n}\n.px-2\\.5 {\n  padding-inline: calc(var(--spacing) * 2.5);\n}\n.px-3 {\n  padding-inline: calc(var(--spacing) * 3);\n}\n.px-4 {\n  padding-inline: calc(var(--spacing) * 4);\n}\n.px-6 {\n  padding-inline: calc(var(--spacing) * 6);\n}\n.py-0 {\n  padding-block: calc(var(--spacing) * 0);\n}\n.py-0\\.5 {\n  padding-block: calc(var(--spacing) * 0.5);\n}\n.py-1 {\n  padding-block: calc(var(--spacing) * 1);\n}\n.py-1\\.5 {\n  padding-block: calc(var(--spacing) * 1.5);\n}\n.py-2 {\n  padding-block: calc(var(--spacing) * 2);\n}\n.py-3 {\n  padding-block: calc(var(--spacing) * 3);\n}\n.py-4 {\n  padding-block: calc(var(--spacing) * 4);\n}\n.py-5 {\n  padding-block: calc(var(--spacing) * 5);\n}\n.py-6 {\n  padding-block: calc(var(--spacing) * 6);\n}\n.py-16 {\n  padding-block: calc(var(--spacing) * 16);\n}\n.pt-0 {\n  padding-top: calc(var(--spacing) * 0);\n}\n.pt-1 {\n  padding-top: calc(var(--spacing) * 1);\n}\n.pt-3 {\n  padding-top: calc(var(--spacing) * 3);\n}\n.pt-4 {\n  padding-top: calc(var(--spacing) * 4);\n}\n.pt-10 {\n  padding-top: calc(var(--spacing) * 10);\n}\n.pr-2 {\n  padding-right: calc(var(--spacing) * 2);\n}\n.pr-4 {\n  padding-right: calc(var(--spacing) * 4);\n}\n.pr-8 {\n  padding-right: calc(var(--spacing) * 8);\n}\n.pb-3 {\n  padding-bottom: calc(var(--spacing) * 3);\n}\n.pl-2 {\n  padding-left: calc(var(--spacing) * 2);\n}\n.pl-5 {\n  padding-left: calc(var(--spacing) * 5);\n}\n.pl-8 {\n  padding-left: calc(var(--spacing) * 8);\n}\n.pl-10 {\n  padding-left: calc(var(--spacing) * 10);\n}\n.text-center {\n  text-align: center;\n}\n.text-left {\n  text-align: left;\n}\n.align-middle {\n  vertical-align: middle;\n}\n.font-mono {\n  font-family: var(--font-mono);\n}\n.text-2xl {\n  font-size: var(--text-2xl);\n  line-height: var(--tw-leading, var(--text-2xl--line-height));\n}\n.text-3xl {\n  font-size: var(--text-3xl);\n  line-height: var(--tw-leading, var(--text-3xl--line-height));\n}\n.text-4xl {\n  font-size: var(--text-4xl);\n  line-height: var(--tw-leading, var(--text-4xl--line-height));\n}\n.text-base {\n  font-size: var(--text-base);\n  line-height: var(--tw-leading, var(--text-base--line-height));\n}\n.text-lg {\n  font-size: var(--text-lg);\n  line-height: var(--tw-leading, var(--text-lg--line-height));\n}\n.text-sm {\n  font-size: var(--text-sm);\n  line-height: var(--tw-leading, var(--text-sm--line-height));\n}\n.text-xl {\n  font-size: var(--text-xl);\n  line-height: var(--tw-leading, var(--text-xl--line-height));\n}\n.text-xs {\n  font-size: var(--text-xs);\n  line-height: var(--tw-leading, var(--text-xs--line-height));\n}\n.text-\\[8px\\] {\n  font-size: 8px;\n}\n.leading-none {\n  --tw-leading: 1;\n  line-height: 1;\n}\n.leading-relaxed {\n  --tw-leading: var(--leading-relaxed);\n  line-height: var(--leading-relaxed);\n}\n.font-bold {\n  --tw-font-weight: var(--font-weight-bold);\n  font-weight: var(--font-weight-bold);\n}\n.font-light {\n  --tw-font-weight: var(--font-weight-light);\n  font-weight: var(--font-weight-light);\n}\n.font-medium {\n  --tw-font-weight: var(--font-weight-medium);\n  font-weight: var(--font-weight-medium);\n}\n.font-semibold {\n  --tw-font-weight: var(--font-weight-semibold);\n  font-weight: var(--font-weight-semibold);\n}\n.tracking-tight {\n  --tw-tracking: var(--tracking-tight);\n  letter-spacing: var(--tracking-tight);\n}\n.tracking-wide {\n  --tw-tracking: var(--tracking-wide);\n  letter-spacing: var(--tracking-wide);\n}\n.tracking-widest {\n  --tw-tracking: var(--tracking-widest);\n  letter-spacing: var(--tracking-widest);\n}\n.text-balance {\n  text-wrap: balance;\n}\n.whitespace-nowrap {\n  white-space: nowrap;\n}\n.text-accent-foreground {\n  color: var(--accent-foreground);\n}\n.text-amber-700 {\n  color: var(--color-amber-700);\n}\n.text-blue-400 {\n  color: var(--color-blue-400);\n}\n.text-blue-600 {\n  color: var(--color-blue-600);\n}\n.text-card-foreground {\n  color: var(--card-foreground);\n}\n.text-destructive {\n  color: var(--destructive);\n}\n.text-emerald-400 {\n  color: var(--color-emerald-400);\n}\n.text-emerald-500 {\n  color: var(--color-emerald-500);\n}\n.text-foreground {\n  color: var(--foreground);\n}\n.text-foreground\\/50 {\n  color: var(--foreground);\n  @supports (color: color-mix(in lab, red, red)) {\n    color: color-mix(in oklab, var(--foreground) 50%, transparent);\n  }\n}\n.text-green-500 {\n  color: var(--color-green-500);\n}\n.text-green-600 {\n  color: var(--color-green-600);\n}\n.text-muted-foreground {\n  color: var(--muted-foreground);\n}\n.text-muted-foreground\\/75 {\n  color: var(--muted-foreground);\n  @supports (color: color-mix(in lab, red, red)) {\n    color: color-mix(in oklab, var(--muted-foreground) 75%, transparent);\n  }\n}\n.text-neutral-500 {\n  color: var(--color-neutral-500);\n}\n.text-popover-foreground {\n  color: var(--popover-foreground);\n}\n.text-primary {\n  color: var(--primary);\n}\n.text-primary-foreground {\n  color: var(--primary-foreground);\n}\n.text-red-200 {\n  color: var(--color-red-200);\n}\n.text-red-300 {\n  color: var(--color-red-300);\n}\n.text-red-400 {\n  color: var(--color-red-400);\n}\n.text-red-500 {\n  color: var(--color-red-500);\n}\n.text-secondary-foreground {\n  color: var(--secondary-foreground);\n}\n.text-sidebar-foreground {\n  color: var(--sidebar-foreground);\n}\n.text-sidebar-foreground\\/70 {\n  color: var(--sidebar-foreground);\n  @supports (color: color-mix(in lab, red, red)) {\n    color: color-mix(in oklab, var(--sidebar-foreground) 70%, transparent);\n  }\n}\n.text-stone-500 {\n  color: var(--color-stone-500);\n}\n.text-transparent {\n  color: transparent;\n}\n.text-white {\n  color: var(--color-white);\n}\n.text-white\\/60 {\n  color: color-mix(in srgb, #fff 60%, transparent);\n  @supports (color: color-mix(in lab, red, red)) {\n    color: color-mix(in oklab, var(--color-white) 60%, transparent);\n  }\n}\n.text-white\\/70 {\n  color: color-mix(in srgb, #fff 70%, transparent);\n  @supports (color: color-mix(in lab, red, red)) {\n    color: color-mix(in oklab, var(--color-white) 70%, transparent);\n  }\n}\n.text-yellow-600 {\n  color: var(--color-yellow-600);\n}\n.text-zinc-300 {\n  color: var(--color-zinc-300);\n}\n.text-zinc-400 {\n  color: var(--color-zinc-400);\n}\n.text-zinc-500 {\n  color: var(--color-zinc-500);\n}\n.capitalize {\n  text-transform: capitalize;\n}\n.tabular-nums {\n  --tw-numeric-spacing: tabular-nums;\n  font-variant-numeric: var(--tw-ordinal,) var(--tw-slashed-zero,) var(--tw-numeric-figure,) var(--tw-numeric-spacing,) var(--tw-numeric-fraction,);\n}\n.underline {\n  text-decoration-line: underline;\n}\n.underline-offset-4 {\n  text-underline-offset: 4px;\n}\n.antialiased {\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n.opacity-0 {\n  opacity: 0%;\n}\n.opacity-50 {\n  opacity: 50%;\n}\n.opacity-70 {\n  opacity: 70%;\n}\n.opacity-75 {\n  opacity: 75%;\n}\n.opacity-100 {\n  opacity: 100%;\n}\n.shadow-\\[0_0_0_1px_hsl\\(var\\(--sidebar-border\\)\\)\\] {\n  --tw-shadow: 0 0 0 1px var(--tw-shadow-color, hsl(var(--sidebar-border)));\n  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n}\n.shadow-\\[0_8px_32px_0_rgba\\(255\\,255\\,255\\,0\\.1\\)\\] {\n  --tw-shadow: 0 8px 32px 0 var(--tw-shadow-color, rgba(255,255,255,0.1));\n  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n}\n.shadow-lg {\n  --tw-shadow: var(--shadow-lg);\n  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n}\n.shadow-md {\n  --tw-shadow: var(--shadow-md);\n  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n}\n.shadow-none {\n  --tw-shadow: 0 0 #0000;\n  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n}\n.shadow-sm {\n  --tw-shadow: var(--shadow-sm);\n  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n}\n.shadow-xl {\n  --tw-shadow: var(--shadow-xl);\n  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n}\n.shadow-xs {\n  --tw-shadow: var(--shadow-xs);\n  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n}\n.ring {\n  --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n}\n.ring-0 {\n  --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n}\n.ring-1 {\n  --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n}\n.ring-blue-300 {\n  --tw-ring-color: var(--color-blue-300);\n}\n.ring-border {\n  --tw-ring-color: var(--border);\n}\n.ring-card-foreground\\/10 {\n  --tw-ring-color: var(--card-foreground);\n  @supports (color: color-mix(in lab, red, red)) {\n    --tw-ring-color: color-mix(in oklab, var(--card-foreground) 10%, transparent);\n  }\n}\n.ring-emerald-600 {\n  --tw-ring-color: var(--color-emerald-600);\n}\n.ring-gray-300 {\n  --tw-ring-color: var(--color-gray-300);\n}\n.ring-green-300 {\n  --tw-ring-color: var(--color-green-300);\n}\n.ring-input {\n  --tw-ring-color: var(--input);\n}\n.ring-muted {\n  --tw-ring-color: var(--muted);\n}\n.ring-primary {\n  --tw-ring-color: var(--primary);\n}\n.ring-primary\\/40 {\n  --tw-ring-color: var(--primary);\n  @supports (color: color-mix(in lab, red, red)) {\n    --tw-ring-color: color-mix(in oklab, var(--primary) 40%, transparent);\n  }\n}\n.ring-primary\\/50 {\n  --tw-ring-color: var(--primary);\n  @supports (color: color-mix(in lab, red, red)) {\n    --tw-ring-color: color-mix(in oklab, var(--primary) 50%, transparent);\n  }\n}\n.ring-sidebar-ring {\n  --tw-ring-color: var(--sidebar-ring);\n}\n.ring-yellow-300 {\n  --tw-ring-color: var(--color-yellow-300);\n}\n.ring-offset-background {\n  --tw-ring-offset-color: var(--background);\n}\n.outline-hidden {\n  --tw-outline-style: none;\n  outline-style: none;\n  @media (forced-colors: active) {\n    outline: 2px solid transparent;\n    outline-offset: 2px;\n  }\n}\n.outline {\n  outline-style: var(--tw-outline-style);\n  outline-width: 1px;\n}\n.blur-3xl {\n  --tw-blur: blur(var(--blur-3xl));\n  filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);\n}\n.filter {\n  filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);\n}\n.backdrop-blur-\\[2px\\] {\n  --tw-backdrop-blur: blur(2px);\n  -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n  backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n}\n.transition {\n  transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;\n  transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n  transition-duration: var(--tw-duration, var(--default-transition-duration));\n}\n.transition-\\[color\\,box-shadow\\] {\n  transition-property: color,box-shadow;\n  transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n  transition-duration: var(--tw-duration, var(--default-transition-duration));\n}\n.transition-\\[left\\,right\\,width\\] {\n  transition-property: left,right,width;\n  transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n  transition-duration: var(--tw-duration, var(--default-transition-duration));\n}\n.transition-\\[margin\\,opacity\\] {\n  transition-property: margin,opacity;\n  transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n  transition-duration: var(--tw-duration, var(--default-transition-duration));\n}\n.transition-\\[width\\,height\\,padding\\] {\n  transition-property: width,height,padding;\n  transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n  transition-duration: var(--tw-duration, var(--default-transition-duration));\n}\n.transition-\\[width\\] {\n  transition-property: width;\n  transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n  transition-duration: var(--tw-duration, var(--default-transition-duration));\n}\n.transition-all {\n  transition-property: all;\n  transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n  transition-duration: var(--tw-duration, var(--default-transition-duration));\n}\n.transition-colors {\n  transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;\n  transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n  transition-duration: var(--tw-duration, var(--default-transition-duration));\n}\n.transition-opacity {\n  transition-property: opacity;\n  transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n  transition-duration: var(--tw-duration, var(--default-transition-duration));\n}\n.transition-transform {\n  transition-property: transform, translate, scale, rotate;\n  transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n  transition-duration: var(--tw-duration, var(--default-transition-duration));\n}\n.duration-200 {\n  --tw-duration: 200ms;\n  transition-duration: 200ms;\n}\n.duration-300 {\n  --tw-duration: 300ms;\n  transition-duration: 300ms;\n}\n.ease-in-out {\n  --tw-ease: var(--ease-in-out);\n  transition-timing-function: var(--ease-in-out);\n}\n.ease-linear {\n  --tw-ease: linear;\n  transition-timing-function: linear;\n}\n.fade-in-0 {\n  --tw-enter-opacity: calc(0/100);\n  --tw-enter-opacity: 0;\n}\n.outline-none {\n  --tw-outline-style: none;\n  outline-style: none;\n}\n.select-none {\n  -webkit-user-select: none;\n  user-select: none;\n}\n.zoom-in-95 {\n  --tw-enter-scale: calc(95*1%);\n  --tw-enter-scale: .95;\n}\n.\\[--base-color\\:\\#a1a1aa\\] {\n  --base-color: #a1a1aa;\n}\n.\\[--base-gradient-color\\:\\#000\\] {\n  --base-gradient-color: #000;\n}\n.\\[--bg\\:linear-gradient\\(90deg\\,\\#0000_calc\\(50\\%-var\\(--spread\\)\\)\\,var\\(--base-gradient-color\\)\\,\\#0000_calc\\(50\\%\\+var\\(--spread\\)\\)\\)\\] {\n  --bg: linear-gradient(90deg,#0000 calc(50% - var(--spread)),var(--base-gradient-color),#0000 calc(50% + var(--spread)));\n}\n.group-peer-data-\\[state\\=checked\\]\\:hidden {\n  &:is(:where(.group):is(:where(.peer)[data-state=\"checked\"] ~ *) *) {\n    display: none;\n  }\n}\n.group-peer-data-\\[state\\=unchecked\\]\\:hidden {\n  &:is(:where(.group):is(:where(.peer)[data-state=\"unchecked\"] ~ *) *) {\n    display: none;\n  }\n}\n.group-focus-within\\/menu-item\\:opacity-100 {\n  &:is(:where(.group\\/menu-item):focus-within *) {\n    opacity: 100%;\n  }\n}\n.group-hover\\/menu-item\\:opacity-100 {\n  &:is(:where(.group\\/menu-item):hover *) {\n    @media (hover: hover) {\n      opacity: 100%;\n    }\n  }\n}\n.group-has-data-\\[sidebar\\=menu-action\\]\\/menu-item\\:pr-8 {\n  &:is(:where(.group\\/menu-item):has(*[data-sidebar=\"menu-action\"]) *) {\n    padding-right: calc(var(--spacing) * 8);\n  }\n}\n.group-data-\\[collapsible\\=icon\\]\\:-mt-8 {\n  &:is(:where(.group)[data-collapsible=\"icon\"] *) {\n    margin-top: calc(var(--spacing) * -8);\n  }\n}\n.group-data-\\[collapsible\\=icon\\]\\:hidden {\n  &:is(:where(.group)[data-collapsible=\"icon\"] *) {\n    display: none;\n  }\n}\n.group-data-\\[collapsible\\=icon\\]\\:size-8\\! {\n  &:is(:where(.group)[data-collapsible=\"icon\"] *) {\n    width: calc(var(--spacing) * 8) !important;\n    height: calc(var(--spacing) * 8) !important;\n  }\n}\n.group-data-\\[collapsible\\=icon\\]\\:w-\\(--sidebar-width-icon\\) {\n  &:is(:where(.group)[data-collapsible=\"icon\"] *) {\n    width: var(--sidebar-width-icon);\n  }\n}\n.group-data-\\[collapsible\\=icon\\]\\:w-\\[calc\\(var\\(--sidebar-width-icon\\)\\+\\(--spacing\\(4\\)\\)\\)\\] {\n  &:is(:where(.group)[data-collapsible=\"icon\"] *) {\n    width: calc(var(--sidebar-width-icon) + (calc(var(--spacing) * 4)));\n  }\n}\n.group-data-\\[collapsible\\=icon\\]\\:w-\\[calc\\(var\\(--sidebar-width-icon\\)\\+\\(--spacing\\(4\\)\\)\\+2px\\)\\] {\n  &:is(:where(.group)[data-collapsible=\"icon\"] *) {\n    width: calc(var(--sidebar-width-icon) + (calc(var(--spacing) * 4)) + 2px);\n  }\n}\n.group-data-\\[collapsible\\=icon\\]\\:overflow-hidden {\n  &:is(:where(.group)[data-collapsible=\"icon\"] *) {\n    overflow: hidden;\n  }\n}\n.group-data-\\[collapsible\\=icon\\]\\:p-0\\! {\n  &:is(:where(.group)[data-collapsible=\"icon\"] *) {\n    padding: calc(var(--spacing) * 0) !important;\n  }\n}\n.group-data-\\[collapsible\\=icon\\]\\:p-2\\! {\n  &:is(:where(.group)[data-collapsible=\"icon\"] *) {\n    padding: calc(var(--spacing) * 2) !important;\n  }\n}\n.group-data-\\[collapsible\\=icon\\]\\:opacity-0 {\n  &:is(:where(.group)[data-collapsible=\"icon\"] *) {\n    opacity: 0%;\n  }\n}\n.group-data-\\[collapsible\\=offcanvas\\]\\:right-\\[calc\\(var\\(--sidebar-width\\)\\*-1\\)\\] {\n  &:is(:where(.group)[data-collapsible=\"offcanvas\"] *) {\n    right: calc(var(--sidebar-width) * -1);\n  }\n}\n.group-data-\\[collapsible\\=offcanvas\\]\\:left-\\[calc\\(var\\(--sidebar-width\\)\\*-1\\)\\] {\n  &:is(:where(.group)[data-collapsible=\"offcanvas\"] *) {\n    left: calc(var(--sidebar-width) * -1);\n  }\n}\n.group-data-\\[collapsible\\=offcanvas\\]\\:w-0 {\n  &:is(:where(.group)[data-collapsible=\"offcanvas\"] *) {\n    width: calc(var(--spacing) * 0);\n  }\n}\n.group-data-\\[collapsible\\=offcanvas\\]\\:translate-x-0 {\n  &:is(:where(.group)[data-collapsible=\"offcanvas\"] *) {\n    --tw-translate-x: calc(var(--spacing) * 0);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n}\n.group-data-\\[disabled\\=true\\]\\:pointer-events-none {\n  &:is(:where(.group)[data-disabled=\"true\"] *) {\n    pointer-events: none;\n  }\n}\n.group-data-\\[disabled\\=true\\]\\:opacity-50 {\n  &:is(:where(.group)[data-disabled=\"true\"] *) {\n    opacity: 50%;\n  }\n}\n.group-data-\\[side\\=left\\]\\:-right-4 {\n  &:is(:where(.group)[data-side=\"left\"] *) {\n    right: calc(var(--spacing) * -4);\n  }\n}\n.group-data-\\[side\\=left\\]\\:border-r {\n  &:is(:where(.group)[data-side=\"left\"] *) {\n    border-right-style: var(--tw-border-style);\n    border-right-width: 1px;\n  }\n}\n.group-data-\\[side\\=right\\]\\:left-0 {\n  &:is(:where(.group)[data-side=\"right\"] *) {\n    left: calc(var(--spacing) * 0);\n  }\n}\n.group-data-\\[side\\=right\\]\\:rotate-180 {\n  &:is(:where(.group)[data-side=\"right\"] *) {\n    rotate: 180deg;\n  }\n}\n.group-data-\\[side\\=right\\]\\:border-l {\n  &:is(:where(.group)[data-side=\"right\"] *) {\n    border-left-style: var(--tw-border-style);\n    border-left-width: 1px;\n  }\n}\n.group-data-\\[variant\\=floating\\]\\:rounded-lg {\n  &:is(:where(.group)[data-variant=\"floating\"] *) {\n    border-radius: var(--radius);\n  }\n}\n.group-data-\\[variant\\=floating\\]\\:border {\n  &:is(:where(.group)[data-variant=\"floating\"] *) {\n    border-style: var(--tw-border-style);\n    border-width: 1px;\n  }\n}\n.group-data-\\[variant\\=floating\\]\\:border-sidebar-border {\n  &:is(:where(.group)[data-variant=\"floating\"] *) {\n    border-color: var(--sidebar-border);\n  }\n}\n.group-data-\\[variant\\=floating\\]\\:shadow-sm {\n  &:is(:where(.group)[data-variant=\"floating\"] *) {\n    --tw-shadow: var(--shadow-sm);\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n}\n.peer-hover\\/menu-button\\:text-sidebar-accent-foreground {\n  &:is(:where(.peer\\/menu-button):hover ~ *) {\n    @media (hover: hover) {\n      color: var(--sidebar-accent-foreground);\n    }\n  }\n}\n.peer-focus-visible\\:ring-\\[3px\\] {\n  &:is(:where(.peer):focus-visible ~ *) {\n    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n}\n.peer-focus-visible\\:ring-ring\\/50 {\n  &:is(:where(.peer):focus-visible ~ *) {\n    --tw-ring-color: var(--ring);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-ring-color: color-mix(in oklab, var(--ring) 50%, transparent);\n    }\n  }\n}\n.peer-disabled\\:cursor-not-allowed {\n  &:is(:where(.peer):disabled ~ *) {\n    cursor: not-allowed;\n  }\n}\n.peer-disabled\\:opacity-50 {\n  &:is(:where(.peer):disabled ~ *) {\n    opacity: 50%;\n  }\n}\n.peer-data-disabled\\:cursor-not-allowed {\n  &:is(:where(.peer)[data-disabled] ~ *) {\n    cursor: not-allowed;\n  }\n}\n.peer-data-disabled\\:opacity-50 {\n  &:is(:where(.peer)[data-disabled] ~ *) {\n    opacity: 50%;\n  }\n}\n.peer-data-\\[active\\=true\\]\\/menu-button\\:text-sidebar-accent-foreground {\n  &:is(:where(.peer\\/menu-button)[data-active=\"true\"] ~ *) {\n    color: var(--sidebar-accent-foreground);\n  }\n}\n.peer-data-\\[size\\=default\\]\\/menu-button\\:top-1\\.5 {\n  &:is(:where(.peer\\/menu-button)[data-size=\"default\"] ~ *) {\n    top: calc(var(--spacing) * 1.5);\n  }\n}\n.peer-data-\\[size\\=lg\\]\\/menu-button\\:top-2\\.5 {\n  &:is(:where(.peer\\/menu-button)[data-size=\"lg\"] ~ *) {\n    top: calc(var(--spacing) * 2.5);\n  }\n}\n.peer-data-\\[size\\=sm\\]\\/menu-button\\:top-1 {\n  &:is(:where(.peer\\/menu-button)[data-size=\"sm\"] ~ *) {\n    top: calc(var(--spacing) * 1);\n  }\n}\n.peer-data-\\[state\\=checked\\]\\:border-primary {\n  &:is(:where(.peer)[data-state=\"checked\"] ~ *) {\n    border-color: var(--primary);\n  }\n}\n.peer-data-\\[state\\=checked\\]\\:bg-accent {\n  &:is(:where(.peer)[data-state=\"checked\"] ~ *) {\n    background-color: var(--accent);\n  }\n}\n.peer-data-\\[state\\=unchecked\\]\\:text-muted-foreground\\/70 {\n  &:is(:where(.peer)[data-state=\"unchecked\"] ~ *) {\n    color: var(--muted-foreground);\n    @supports (color: color-mix(in lab, red, red)) {\n      color: color-mix(in oklab, var(--muted-foreground) 70%, transparent);\n    }\n  }\n}\n.selection\\:bg-primary {\n  & *::selection {\n    background-color: var(--primary);\n  }\n  &::selection {\n    background-color: var(--primary);\n  }\n}\n.selection\\:text-primary-foreground {\n  & *::selection {\n    color: var(--primary-foreground);\n  }\n  &::selection {\n    color: var(--primary-foreground);\n  }\n}\n.file\\:inline-flex {\n  &::file-selector-button {\n    display: inline-flex;\n  }\n}\n.file\\:h-7 {\n  &::file-selector-button {\n    height: calc(var(--spacing) * 7);\n  }\n}\n.file\\:border-0 {\n  &::file-selector-button {\n    border-style: var(--tw-border-style);\n    border-width: 0px;\n  }\n}\n.file\\:bg-transparent {\n  &::file-selector-button {\n    background-color: transparent;\n  }\n}\n.file\\:text-sm {\n  &::file-selector-button {\n    font-size: var(--text-sm);\n    line-height: var(--tw-leading, var(--text-sm--line-height));\n  }\n}\n.file\\:font-medium {\n  &::file-selector-button {\n    --tw-font-weight: var(--font-weight-medium);\n    font-weight: var(--font-weight-medium);\n  }\n}\n.file\\:text-foreground {\n  &::file-selector-button {\n    color: var(--foreground);\n  }\n}\n.placeholder\\:text-muted-foreground {\n  &::placeholder {\n    color: var(--muted-foreground);\n  }\n}\n.after\\:absolute {\n  &::after {\n    content: var(--tw-content);\n    position: absolute;\n  }\n}\n.after\\:-inset-2 {\n  &::after {\n    content: var(--tw-content);\n    inset: calc(var(--spacing) * -2);\n  }\n}\n.after\\:inset-0 {\n  &::after {\n    content: var(--tw-content);\n    inset: calc(var(--spacing) * 0);\n  }\n}\n.after\\:inset-y-0 {\n  &::after {\n    content: var(--tw-content);\n    inset-block: calc(var(--spacing) * 0);\n  }\n}\n.after\\:left-1\\/2 {\n  &::after {\n    content: var(--tw-content);\n    left: calc(1/2 * 100%);\n  }\n}\n.after\\:w-\\[2px\\] {\n  &::after {\n    content: var(--tw-content);\n    width: 2px;\n  }\n}\n.after\\:rounded-full {\n  &::after {\n    content: var(--tw-content);\n    border-radius: calc(infinity * 1px);\n  }\n}\n.after\\:bg-\\[radial-gradient\\(circle_at_50\\%_50\\%\\,rgba\\(255\\,255\\,255\\,0\\.2\\)\\,transparent_70\\%\\)\\] {\n  &::after {\n    content: var(--tw-content);\n    background-image: radial-gradient(circle at 50% 50%,rgba(255,255,255,0.2),transparent 70%);\n  }\n}\n.group-data-\\[collapsible\\=offcanvas\\]\\:after\\:left-full {\n  &:is(:where(.group)[data-collapsible=\"offcanvas\"] *) {\n    &::after {\n      content: var(--tw-content);\n      left: 100%;\n    }\n  }\n}\n.hover\\:border-zinc-600 {\n  &:hover {\n    @media (hover: hover) {\n      border-color: var(--color-zinc-600);\n    }\n  }\n}\n.hover\\:bg-accent {\n  &:hover {\n    @media (hover: hover) {\n      background-color: var(--accent);\n    }\n  }\n}\n.hover\\:bg-blue-700 {\n  &:hover {\n    @media (hover: hover) {\n      background-color: var(--color-blue-700);\n    }\n  }\n}\n.hover\\:bg-destructive\\/90 {\n  &:hover {\n    @media (hover: hover) {\n      background-color: var(--destructive);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--destructive) 90%, transparent);\n      }\n    }\n  }\n}\n.hover\\:bg-emerald-700 {\n  &:hover {\n    @media (hover: hover) {\n      background-color: var(--color-emerald-700);\n    }\n  }\n}\n.hover\\:bg-emerald-800 {\n  &:hover {\n    @media (hover: hover) {\n      background-color: var(--color-emerald-800);\n    }\n  }\n}\n.hover\\:bg-muted\\/50 {\n  &:hover {\n    @media (hover: hover) {\n      background-color: var(--muted);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--muted) 50%, transparent);\n      }\n    }\n  }\n}\n.hover\\:bg-primary\\/10 {\n  &:hover {\n    @media (hover: hover) {\n      background-color: var(--primary);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--primary) 10%, transparent);\n      }\n    }\n  }\n}\n.hover\\:bg-primary\\/30 {\n  &:hover {\n    @media (hover: hover) {\n      background-color: var(--primary);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--primary) 30%, transparent);\n      }\n    }\n  }\n}\n.hover\\:bg-primary\\/90 {\n  &:hover {\n    @media (hover: hover) {\n      background-color: var(--primary);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--primary) 90%, transparent);\n      }\n    }\n  }\n}\n.hover\\:bg-secondary\\/80 {\n  &:hover {\n    @media (hover: hover) {\n      background-color: var(--secondary);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--secondary) 80%, transparent);\n      }\n    }\n  }\n}\n.hover\\:bg-sidebar-accent {\n  &:hover {\n    @media (hover: hover) {\n      background-color: var(--sidebar-accent);\n    }\n  }\n}\n.hover\\:text-accent-foreground {\n  &:hover {\n    @media (hover: hover) {\n      color: var(--accent-foreground);\n    }\n  }\n}\n.hover\\:text-sidebar-accent-foreground {\n  &:hover {\n    @media (hover: hover) {\n      color: var(--sidebar-accent-foreground);\n    }\n  }\n}\n.hover\\:text-white {\n  &:hover {\n    @media (hover: hover) {\n      color: var(--color-white);\n    }\n  }\n}\n.hover\\:underline {\n  &:hover {\n    @media (hover: hover) {\n      text-decoration-line: underline;\n    }\n  }\n}\n.hover\\:opacity-100 {\n  &:hover {\n    @media (hover: hover) {\n      opacity: 100%;\n    }\n  }\n}\n.hover\\:shadow-\\[0_0_0_1px_hsl\\(var\\(--sidebar-accent\\)\\)\\] {\n  &:hover {\n    @media (hover: hover) {\n      --tw-shadow: 0 0 0 1px var(--tw-shadow-color, hsl(var(--sidebar-accent)));\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n}\n.hover\\:ring-2 {\n  &:hover {\n    @media (hover: hover) {\n      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n}\n.hover\\:ring-card-foreground\\/10 {\n  &:hover {\n    @media (hover: hover) {\n      --tw-ring-color: var(--card-foreground);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-ring-color: color-mix(in oklab, var(--card-foreground) 10%, transparent);\n      }\n    }\n  }\n}\n.hover\\:ring-primary\\/50 {\n  &:hover {\n    @media (hover: hover) {\n      --tw-ring-color: var(--primary);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-ring-color: color-mix(in oklab, var(--primary) 50%, transparent);\n      }\n    }\n  }\n}\n.hover\\:group-data-\\[collapsible\\=offcanvas\\]\\:bg-sidebar {\n  &:hover {\n    @media (hover: hover) {\n      &:is(:where(.group)[data-collapsible=\"offcanvas\"] *) {\n        background-color: var(--sidebar);\n      }\n    }\n  }\n}\n.hover\\:after\\:bg-sidebar-border {\n  &:hover {\n    @media (hover: hover) {\n      &::after {\n        content: var(--tw-content);\n        background-color: var(--sidebar-border);\n      }\n    }\n  }\n}\n.focus\\:z-10 {\n  &:focus {\n    z-index: 10;\n  }\n}\n.focus\\:border-blue-500 {\n  &:focus {\n    border-color: var(--color-blue-500);\n  }\n}\n.focus\\:bg-accent {\n  &:focus {\n    background-color: var(--accent);\n  }\n}\n.focus\\:text-accent-foreground {\n  &:focus {\n    color: var(--accent-foreground);\n  }\n}\n.focus\\:ring-1 {\n  &:focus {\n    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n}\n.focus\\:ring-2 {\n  &:focus {\n    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n}\n.focus\\:ring-blue-500 {\n  &:focus {\n    --tw-ring-color: var(--color-blue-500);\n  }\n}\n.focus\\:ring-ring {\n  &:focus {\n    --tw-ring-color: var(--ring);\n  }\n}\n.focus\\:ring-offset-2 {\n  &:focus {\n    --tw-ring-offset-width: 2px;\n    --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  }\n}\n.focus\\:outline-hidden {\n  &:focus {\n    --tw-outline-style: none;\n    outline-style: none;\n    @media (forced-colors: active) {\n      outline: 2px solid transparent;\n      outline-offset: 2px;\n    }\n  }\n}\n.focus\\:outline-none {\n  &:focus {\n    --tw-outline-style: none;\n    outline-style: none;\n  }\n}\n.focus-visible\\:border-ring {\n  &:focus-visible {\n    border-color: var(--ring);\n  }\n}\n.focus-visible\\:ring-0 {\n  &:focus-visible {\n    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n}\n.focus-visible\\:ring-2 {\n  &:focus-visible {\n    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n}\n.focus-visible\\:ring-\\[1px\\] {\n  &:focus-visible {\n    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n}\n.focus-visible\\:ring-\\[3px\\] {\n  &:focus-visible {\n    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n}\n.focus-visible\\:ring-destructive\\/20 {\n  &:focus-visible {\n    --tw-ring-color: var(--destructive);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent);\n    }\n  }\n}\n.focus-visible\\:ring-ring\\/50 {\n  &:focus-visible {\n    --tw-ring-color: var(--ring);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-ring-color: color-mix(in oklab, var(--ring) 50%, transparent);\n    }\n  }\n}\n.focus-visible\\:outline-1 {\n  &:focus-visible {\n    outline-style: var(--tw-outline-style);\n    outline-width: 1px;\n  }\n}\n.focus-visible\\:outline-none {\n  &:focus-visible {\n    --tw-outline-style: none;\n    outline-style: none;\n  }\n}\n.active\\:bg-sidebar-accent {\n  &:active {\n    background-color: var(--sidebar-accent);\n  }\n}\n.active\\:text-sidebar-accent-foreground {\n  &:active {\n    color: var(--sidebar-accent-foreground);\n  }\n}\n.disabled\\:pointer-events-none {\n  &:disabled {\n    pointer-events: none;\n  }\n}\n.disabled\\:cursor-not-allowed {\n  &:disabled {\n    cursor: not-allowed;\n  }\n}\n.disabled\\:opacity-50 {\n  &:disabled {\n    opacity: 50%;\n  }\n}\n.in-data-\\[side\\=left\\]\\:cursor-w-resize {\n  :where(*[data-side=\"left\"]) & {\n    cursor: w-resize;\n  }\n}\n.in-data-\\[side\\=right\\]\\:cursor-e-resize {\n  :where(*[data-side=\"right\"]) & {\n    cursor: e-resize;\n  }\n}\n.has-data-\\[slot\\=card-action\\]\\:grid-cols-\\[1fr_auto\\] {\n  &:has(*[data-slot=\"card-action\"]) {\n    grid-template-columns: 1fr auto;\n  }\n}\n.has-data-\\[variant\\=inset\\]\\:bg-sidebar {\n  &:has(*[data-variant=\"inset\"]) {\n    background-color: var(--sidebar);\n  }\n}\n.has-\\[\\>svg\\]\\:px-2\\.5 {\n  &:has(>svg) {\n    padding-inline: calc(var(--spacing) * 2.5);\n  }\n}\n.has-\\[\\>svg\\]\\:px-3 {\n  &:has(>svg) {\n    padding-inline: calc(var(--spacing) * 3);\n  }\n}\n.has-\\[\\>svg\\]\\:px-4 {\n  &:has(>svg) {\n    padding-inline: calc(var(--spacing) * 4);\n  }\n}\n.aria-disabled\\:pointer-events-none {\n  &[aria-disabled=\"true\"] {\n    pointer-events: none;\n  }\n}\n.aria-disabled\\:opacity-50 {\n  &[aria-disabled=\"true\"] {\n    opacity: 50%;\n  }\n}\n.aria-invalid\\:border-destructive {\n  &[aria-invalid=\"true\"] {\n    border-color: var(--destructive);\n  }\n}\n.aria-invalid\\:ring-destructive\\/20 {\n  &[aria-invalid=\"true\"] {\n    --tw-ring-color: var(--destructive);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent);\n    }\n  }\n}\n.data-\\[active\\=true\\]\\:bg-sidebar-accent {\n  &[data-active=\"true\"] {\n    background-color: var(--sidebar-accent);\n  }\n}\n.data-\\[active\\=true\\]\\:font-medium {\n  &[data-active=\"true\"] {\n    --tw-font-weight: var(--font-weight-medium);\n    font-weight: var(--font-weight-medium);\n  }\n}\n.data-\\[active\\=true\\]\\:text-sidebar-accent-foreground {\n  &[data-active=\"true\"] {\n    color: var(--sidebar-accent-foreground);\n  }\n}\n.data-\\[disabled\\]\\:pointer-events-none {\n  &[data-disabled] {\n    pointer-events: none;\n  }\n}\n.data-\\[disabled\\]\\:opacity-50 {\n  &[data-disabled] {\n    opacity: 50%;\n  }\n}\n.data-\\[disabled\\=true\\]\\:pointer-events-none {\n  &[data-disabled=\"true\"] {\n    pointer-events: none;\n  }\n}\n.data-\\[disabled\\=true\\]\\:opacity-50 {\n  &[data-disabled=\"true\"] {\n    opacity: 50%;\n  }\n}\n.data-\\[error\\=true\\]\\:text-destructive {\n  &[data-error=\"true\"] {\n    color: var(--destructive);\n  }\n}\n.data-\\[inset\\]\\:pl-8 {\n  &[data-inset] {\n    padding-left: calc(var(--spacing) * 8);\n  }\n}\n.data-\\[orientation\\=horizontal\\]\\:h-px {\n  &[data-orientation=\"horizontal\"] {\n    height: 1px;\n  }\n}\n.data-\\[orientation\\=horizontal\\]\\:w-full {\n  &[data-orientation=\"horizontal\"] {\n    width: 100%;\n  }\n}\n.data-\\[orientation\\=vertical\\]\\:h-full {\n  &[data-orientation=\"vertical\"] {\n    height: 100%;\n  }\n}\n.data-\\[orientation\\=vertical\\]\\:w-px {\n  &[data-orientation=\"vertical\"] {\n    width: 1px;\n  }\n}\n.data-\\[placeholder\\]\\:text-muted-foreground {\n  &[data-placeholder] {\n    color: var(--muted-foreground);\n  }\n}\n.data-\\[selected\\=true\\]\\:bg-accent {\n  &[data-selected=\"true\"] {\n    background-color: var(--accent);\n  }\n}\n.data-\\[selected\\=true\\]\\:text-accent-foreground {\n  &[data-selected=\"true\"] {\n    color: var(--accent-foreground);\n  }\n}\n.data-\\[side\\=bottom\\]\\:translate-y-1 {\n  &[data-side=\"bottom\"] {\n    --tw-translate-y: calc(var(--spacing) * 1);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n}\n.data-\\[side\\=bottom\\]\\:slide-in-from-top-2 {\n  &[data-side=\"bottom\"] {\n    --tw-enter-translate-y: calc(2*var(--spacing)*-1);\n  }\n}\n.data-\\[side\\=left\\]\\:-translate-x-1 {\n  &[data-side=\"left\"] {\n    --tw-translate-x: calc(var(--spacing) * -1);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n}\n.data-\\[side\\=left\\]\\:slide-in-from-right-2 {\n  &[data-side=\"left\"] {\n    --tw-enter-translate-x: calc(2*var(--spacing));\n  }\n}\n.data-\\[side\\=right\\]\\:translate-x-1 {\n  &[data-side=\"right\"] {\n    --tw-translate-x: calc(var(--spacing) * 1);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n}\n.data-\\[side\\=right\\]\\:slide-in-from-left-2 {\n  &[data-side=\"right\"] {\n    --tw-enter-translate-x: calc(2*var(--spacing)*-1);\n  }\n}\n.data-\\[side\\=top\\]\\:-translate-y-1 {\n  &[data-side=\"top\"] {\n    --tw-translate-y: calc(var(--spacing) * -1);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n}\n.data-\\[side\\=top\\]\\:slide-in-from-bottom-2 {\n  &[data-side=\"top\"] {\n    --tw-enter-translate-y: calc(2*var(--spacing));\n  }\n}\n.data-\\[size\\=default\\]\\:h-9 {\n  &[data-size=\"default\"] {\n    height: calc(var(--spacing) * 9);\n  }\n}\n.data-\\[size\\=sm\\]\\:h-8 {\n  &[data-size=\"sm\"] {\n    height: calc(var(--spacing) * 8);\n  }\n}\n.\\*\\*\\:data-\\[slot\\=command-input-wrapper\\]\\:h-12 {\n  :is(& *) {\n    &[data-slot=\"command-input-wrapper\"] {\n      height: calc(var(--spacing) * 12);\n    }\n  }\n}\n.\\*\\:data-\\[slot\\=select-value\\]\\:line-clamp-1 {\n  :is(& > *) {\n    &[data-slot=\"select-value\"] {\n      overflow: hidden;\n      display: -webkit-box;\n      -webkit-box-orient: vertical;\n      -webkit-line-clamp: 1;\n    }\n  }\n}\n.\\*\\:data-\\[slot\\=select-value\\]\\:flex {\n  :is(& > *) {\n    &[data-slot=\"select-value\"] {\n      display: flex;\n    }\n  }\n}\n.\\*\\:data-\\[slot\\=select-value\\]\\:items-center {\n  :is(& > *) {\n    &[data-slot=\"select-value\"] {\n      align-items: center;\n    }\n  }\n}\n.\\*\\:data-\\[slot\\=select-value\\]\\:gap-2 {\n  :is(& > *) {\n    &[data-slot=\"select-value\"] {\n      gap: calc(var(--spacing) * 2);\n    }\n  }\n}\n.data-\\[state\\=checked\\]\\:translate-x-\\[calc\\(100\\%-2px\\)\\] {\n  &[data-state=\"checked\"] {\n    --tw-translate-x: calc(100% - 2px);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n}\n.data-\\[state\\=checked\\]\\:bg-primary {\n  &[data-state=\"checked\"] {\n    background-color: var(--primary);\n  }\n}\n.data-\\[state\\=closed\\]\\:animate-out {\n  &[data-state=\"closed\"] {\n    animation: exit var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,none);\n  }\n}\n.data-\\[state\\=closed\\]\\:duration-300 {\n  &[data-state=\"closed\"] {\n    --tw-duration: 300ms;\n    transition-duration: 300ms;\n  }\n}\n.data-\\[state\\=closed\\]\\:fade-out-0 {\n  &[data-state=\"closed\"] {\n    --tw-exit-opacity: calc(0/100);\n    --tw-exit-opacity: 0;\n  }\n}\n.data-\\[state\\=closed\\]\\:zoom-out-95 {\n  &[data-state=\"closed\"] {\n    --tw-exit-scale: calc(95*1%);\n    --tw-exit-scale: .95;\n  }\n}\n.data-\\[state\\=closed\\]\\:slide-out-to-bottom {\n  &[data-state=\"closed\"] {\n    --tw-exit-translate-y: 100%;\n  }\n}\n.data-\\[state\\=closed\\]\\:slide-out-to-left {\n  &[data-state=\"closed\"] {\n    --tw-exit-translate-x: -100%;\n  }\n}\n.data-\\[state\\=closed\\]\\:slide-out-to-right {\n  &[data-state=\"closed\"] {\n    --tw-exit-translate-x: 100%;\n  }\n}\n.data-\\[state\\=closed\\]\\:slide-out-to-top {\n  &[data-state=\"closed\"] {\n    --tw-exit-translate-y: -100%;\n  }\n}\n.data-\\[state\\=open\\]\\:animate-in {\n  &[data-state=\"open\"] {\n    animation: enter var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,none);\n  }\n}\n.data-\\[state\\=open\\]\\:bg-accent {\n  &[data-state=\"open\"] {\n    background-color: var(--accent);\n  }\n}\n.data-\\[state\\=open\\]\\:bg-secondary {\n  &[data-state=\"open\"] {\n    background-color: var(--secondary);\n  }\n}\n.data-\\[state\\=open\\]\\:text-accent-foreground {\n  &[data-state=\"open\"] {\n    color: var(--accent-foreground);\n  }\n}\n.data-\\[state\\=open\\]\\:text-muted-foreground {\n  &[data-state=\"open\"] {\n    color: var(--muted-foreground);\n  }\n}\n.data-\\[state\\=open\\]\\:opacity-100 {\n  &[data-state=\"open\"] {\n    opacity: 100%;\n  }\n}\n.data-\\[state\\=open\\]\\:duration-500 {\n  &[data-state=\"open\"] {\n    --tw-duration: 500ms;\n    transition-duration: 500ms;\n  }\n}\n.data-\\[state\\=open\\]\\:fade-in-0 {\n  &[data-state=\"open\"] {\n    --tw-enter-opacity: calc(0/100);\n    --tw-enter-opacity: 0;\n  }\n}\n.data-\\[state\\=open\\]\\:zoom-in-95 {\n  &[data-state=\"open\"] {\n    --tw-enter-scale: calc(95*1%);\n    --tw-enter-scale: .95;\n  }\n}\n.data-\\[state\\=open\\]\\:slide-in-from-bottom {\n  &[data-state=\"open\"] {\n    --tw-enter-translate-y: 100%;\n  }\n}\n.data-\\[state\\=open\\]\\:slide-in-from-left {\n  &[data-state=\"open\"] {\n    --tw-enter-translate-x: -100%;\n  }\n}\n.data-\\[state\\=open\\]\\:slide-in-from-right {\n  &[data-state=\"open\"] {\n    --tw-enter-translate-x: 100%;\n  }\n}\n.data-\\[state\\=open\\]\\:slide-in-from-top {\n  &[data-state=\"open\"] {\n    --tw-enter-translate-y: -100%;\n  }\n}\n.data-\\[state\\=open\\]\\:hover\\:bg-sidebar-accent {\n  &[data-state=\"open\"] {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--sidebar-accent);\n      }\n    }\n  }\n}\n.data-\\[state\\=open\\]\\:hover\\:text-sidebar-accent-foreground {\n  &[data-state=\"open\"] {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--sidebar-accent-foreground);\n      }\n    }\n  }\n}\n.data-\\[state\\=selected\\]\\:bg-muted {\n  &[data-state=\"selected\"] {\n    background-color: var(--muted);\n  }\n}\n.data-\\[state\\=unchecked\\]\\:translate-x-0 {\n  &[data-state=\"unchecked\"] {\n    --tw-translate-x: calc(var(--spacing) * 0);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n}\n.data-\\[state\\=unchecked\\]\\:bg-input {\n  &[data-state=\"unchecked\"] {\n    background-color: var(--input);\n  }\n}\n.data-\\[variant\\=destructive\\]\\:text-destructive {\n  &[data-variant=\"destructive\"] {\n    color: var(--destructive);\n  }\n}\n.data-\\[variant\\=destructive\\]\\:focus\\:bg-destructive\\/10 {\n  &[data-variant=\"destructive\"] {\n    &:focus {\n      background-color: var(--destructive);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--destructive) 10%, transparent);\n      }\n    }\n  }\n}\n.data-\\[variant\\=destructive\\]\\:focus\\:text-destructive {\n  &[data-variant=\"destructive\"] {\n    &:focus {\n      color: var(--destructive);\n    }\n  }\n}\n.sm\\:ml-auto {\n  @media (width >= 40rem) {\n    margin-left: auto;\n  }\n}\n.sm\\:flex {\n  @media (width >= 40rem) {\n    display: flex;\n  }\n}\n.sm\\:max-w-lg {\n  @media (width >= 40rem) {\n    max-width: var(--container-lg);\n  }\n}\n.sm\\:max-w-sm {\n  @media (width >= 40rem) {\n    max-width: var(--container-sm);\n  }\n}\n.sm\\:flex-row {\n  @media (width >= 40rem) {\n    flex-direction: row;\n  }\n}\n.sm\\:justify-end {\n  @media (width >= 40rem) {\n    justify-content: flex-end;\n  }\n}\n.sm\\:px-6 {\n  @media (width >= 40rem) {\n    padding-inline: calc(var(--spacing) * 6);\n  }\n}\n.sm\\:pt-6 {\n  @media (width >= 40rem) {\n    padding-top: calc(var(--spacing) * 6);\n  }\n}\n.sm\\:text-left {\n  @media (width >= 40rem) {\n    text-align: left;\n  }\n}\n.sm\\:text-6xl {\n  @media (width >= 40rem) {\n    font-size: var(--text-6xl);\n    line-height: var(--tw-leading, var(--text-6xl--line-height));\n  }\n}\n.md\\:top-\\[10\\%\\] {\n  @media (width >= 48rem) {\n    top: 10%;\n  }\n}\n.md\\:top-\\[15\\%\\] {\n  @media (width >= 48rem) {\n    top: 15%;\n  }\n}\n.md\\:top-\\[20\\%\\] {\n  @media (width >= 48rem) {\n    top: 20%;\n  }\n}\n.md\\:top-\\[75\\%\\] {\n  @media (width >= 48rem) {\n    top: 75%;\n  }\n}\n.md\\:right-\\[0\\%\\] {\n  @media (width >= 48rem) {\n    right: 0%;\n  }\n}\n.md\\:right-\\[20\\%\\] {\n  @media (width >= 48rem) {\n    right: 20%;\n  }\n}\n.md\\:bottom-\\[10\\%\\] {\n  @media (width >= 48rem) {\n    bottom: 10%;\n  }\n}\n.md\\:left-\\[-5\\%\\] {\n  @media (width >= 48rem) {\n    left: -5%;\n  }\n}\n.md\\:left-\\[10\\%\\] {\n  @media (width >= 48rem) {\n    left: 10%;\n  }\n}\n.md\\:left-\\[25\\%\\] {\n  @media (width >= 48rem) {\n    left: 25%;\n  }\n}\n.md\\:mb-8 {\n  @media (width >= 48rem) {\n    margin-bottom: calc(var(--spacing) * 8);\n  }\n}\n.md\\:mb-12 {\n  @media (width >= 48rem) {\n    margin-bottom: calc(var(--spacing) * 12);\n  }\n}\n.md\\:block {\n  @media (width >= 48rem) {\n    display: block;\n  }\n}\n.md\\:flex {\n  @media (width >= 48rem) {\n    display: flex;\n  }\n}\n.md\\:grid-cols-2 {\n  @media (width >= 48rem) {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n}\n.md\\:flex-row {\n  @media (width >= 48rem) {\n    flex-direction: row;\n  }\n}\n.md\\:items-center {\n  @media (width >= 48rem) {\n    align-items: center;\n  }\n}\n.md\\:p-6 {\n  @media (width >= 48rem) {\n    padding: calc(var(--spacing) * 6);\n  }\n}\n.md\\:p-8 {\n  @media (width >= 48rem) {\n    padding: calc(var(--spacing) * 8);\n  }\n}\n.md\\:px-6 {\n  @media (width >= 48rem) {\n    padding-inline: calc(var(--spacing) * 6);\n  }\n}\n.md\\:text-4xl {\n  @media (width >= 48rem) {\n    font-size: var(--text-4xl);\n    line-height: var(--tw-leading, var(--text-4xl--line-height));\n  }\n}\n.md\\:text-8xl {\n  @media (width >= 48rem) {\n    font-size: var(--text-8xl);\n    line-height: var(--tw-leading, var(--text-8xl--line-height));\n  }\n}\n.md\\:text-sm {\n  @media (width >= 48rem) {\n    font-size: var(--text-sm);\n    line-height: var(--tw-leading, var(--text-sm--line-height));\n  }\n}\n.md\\:opacity-0 {\n  @media (width >= 48rem) {\n    opacity: 0%;\n  }\n}\n.md\\:peer-data-\\[variant\\=inset\\]\\:m-2 {\n  @media (width >= 48rem) {\n    &:is(:where(.peer)[data-variant=\"inset\"] ~ *) {\n      margin: calc(var(--spacing) * 2);\n    }\n  }\n}\n.md\\:peer-data-\\[variant\\=inset\\]\\:ml-0 {\n  @media (width >= 48rem) {\n    &:is(:where(.peer)[data-variant=\"inset\"] ~ *) {\n      margin-left: calc(var(--spacing) * 0);\n    }\n  }\n}\n.md\\:peer-data-\\[variant\\=inset\\]\\:rounded-xl {\n  @media (width >= 48rem) {\n    &:is(:where(.peer)[data-variant=\"inset\"] ~ *) {\n      border-radius: calc(var(--radius) + 4px);\n    }\n  }\n}\n.md\\:peer-data-\\[variant\\=inset\\]\\:shadow-sm {\n  @media (width >= 48rem) {\n    &:is(:where(.peer)[data-variant=\"inset\"] ~ *) {\n      --tw-shadow: var(--shadow-sm);\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n}\n.md\\:peer-data-\\[variant\\=inset\\]\\:peer-data-\\[state\\=collapsed\\]\\:ml-2 {\n  @media (width >= 48rem) {\n    &:is(:where(.peer)[data-variant=\"inset\"] ~ *) {\n      &:is(:where(.peer)[data-state=\"collapsed\"] ~ *) {\n        margin-left: calc(var(--spacing) * 2);\n      }\n    }\n  }\n}\n.md\\:after\\:hidden {\n  @media (width >= 48rem) {\n    &::after {\n      content: var(--tw-content);\n      display: none;\n    }\n  }\n}\n.lg\\:h-10 {\n  @media (width >= 64rem) {\n    height: calc(var(--spacing) * 10);\n  }\n}\n.lg\\:max-h-10 {\n  @media (width >= 64rem) {\n    max-height: calc(var(--spacing) * 10);\n  }\n}\n.lg\\:w-10 {\n  @media (width >= 64rem) {\n    width: calc(var(--spacing) * 10);\n  }\n}\n.lg\\:max-w-10 {\n  @media (width >= 64rem) {\n    max-width: calc(var(--spacing) * 10);\n  }\n}\n.lg\\:grid-cols-2 {\n  @media (width >= 64rem) {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n}\n.lg\\:grid-cols-3 {\n  @media (width >= 64rem) {\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n}\n.lg\\:p-8 {\n  @media (width >= 64rem) {\n    padding: calc(var(--spacing) * 8);\n  }\n}\n.xl\\:grid-cols-2 {\n  @media (width >= 80rem) {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n}\n.dark\\:border-input {\n  &:is(.dark *) {\n    border-color: var(--input);\n  }\n}\n.dark\\:bg-card {\n  &:is(.dark *) {\n    background-color: var(--card);\n  }\n}\n.dark\\:bg-destructive\\/60 {\n  &:is(.dark *) {\n    background-color: var(--destructive);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--destructive) 60%, transparent);\n    }\n  }\n}\n.dark\\:bg-emerald-600 {\n  &:is(.dark *) {\n    background-color: var(--color-emerald-600);\n  }\n}\n.dark\\:bg-input\\/30 {\n  &:is(.dark *) {\n    background-color: var(--input);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--input) 30%, transparent);\n    }\n  }\n}\n.dark\\:text-emerald-300 {\n  &:is(.dark *) {\n    color: var(--color-emerald-300);\n  }\n}\n.dark\\:\\[--base-color\\:\\#71717a\\] {\n  &:is(.dark *) {\n    --base-color: #71717a;\n  }\n}\n.dark\\:\\[--base-gradient-color\\:\\#ffffff\\] {\n  &:is(.dark *) {\n    --base-gradient-color: #ffffff;\n  }\n}\n.dark\\:\\[--bg\\:linear-gradient\\(90deg\\,\\#0000_calc\\(50\\%-var\\(--spread\\)\\)\\,var\\(--base-gradient-color\\)\\,\\#0000_calc\\(50\\%\\+var\\(--spread\\)\\)\\)\\] {\n  &:is(.dark *) {\n    --bg: linear-gradient(90deg,#0000 calc(50% - var(--spread)),var(--base-gradient-color),#0000 calc(50% + var(--spread)));\n  }\n}\n.dark\\:hover\\:bg-accent\\/50 {\n  &:is(.dark *) {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--accent);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--accent) 50%, transparent);\n        }\n      }\n    }\n  }\n}\n.dark\\:hover\\:bg-input\\/50 {\n  &:is(.dark *) {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--input);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--input) 50%, transparent);\n        }\n      }\n    }\n  }\n}\n.dark\\:focus-visible\\:ring-destructive\\/40 {\n  &:is(.dark *) {\n    &:focus-visible {\n      --tw-ring-color: var(--destructive);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-ring-color: color-mix(in oklab, var(--destructive) 40%, transparent);\n      }\n    }\n  }\n}\n.dark\\:aria-invalid\\:ring-destructive\\/40 {\n  &:is(.dark *) {\n    &[aria-invalid=\"true\"] {\n      --tw-ring-color: var(--destructive);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-ring-color: color-mix(in oklab, var(--destructive) 40%, transparent);\n      }\n    }\n  }\n}\n.dark\\:data-\\[state\\=checked\\]\\:bg-primary-foreground {\n  &:is(.dark *) {\n    &[data-state=\"checked\"] {\n      background-color: var(--primary-foreground);\n    }\n  }\n}\n.dark\\:data-\\[state\\=unchecked\\]\\:bg-foreground {\n  &:is(.dark *) {\n    &[data-state=\"unchecked\"] {\n      background-color: var(--foreground);\n    }\n  }\n}\n.dark\\:data-\\[state\\=unchecked\\]\\:bg-input\\/80 {\n  &:is(.dark *) {\n    &[data-state=\"unchecked\"] {\n      background-color: var(--input);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--input) 80%, transparent);\n      }\n    }\n  }\n}\n.dark\\:data-\\[variant\\=destructive\\]\\:focus\\:bg-destructive\\/20 {\n  &:is(.dark *) {\n    &[data-variant=\"destructive\"] {\n      &:focus {\n        background-color: var(--destructive);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--destructive) 20%, transparent);\n        }\n      }\n    }\n  }\n}\n.\\[\\&_\\.recharts-cartesian-axis-tick_text\\]\\:fill-muted-foreground {\n  & .recharts-cartesian-axis-tick text {\n    fill: var(--muted-foreground);\n  }\n}\n.\\[\\&_\\.recharts-cartesian-grid_line\\[stroke\\=\\'\\#ccc\\'\\]\\]\\:stroke-border\\/50 {\n  & .recharts-cartesian-grid line[stroke='#ccc'] {\n    stroke: var(--border);\n    @supports (color: color-mix(in lab, red, red)) {\n      stroke: color-mix(in oklab, var(--border) 50%, transparent);\n    }\n  }\n}\n.\\[\\&_\\.recharts-curve\\.recharts-tooltip-cursor\\]\\:stroke-border {\n  & .recharts-curve.recharts-tooltip-cursor {\n    stroke: var(--border);\n  }\n}\n.\\[\\&_\\.recharts-dot\\[stroke\\=\\'\\#fff\\'\\]\\]\\:stroke-transparent {\n  & .recharts-dot[stroke='#fff'] {\n    stroke: transparent;\n  }\n}\n.\\[\\&_\\.recharts-layer\\]\\:outline-hidden {\n  & .recharts-layer {\n    --tw-outline-style: none;\n    outline-style: none;\n    @media (forced-colors: active) {\n      outline: 2px solid transparent;\n      outline-offset: 2px;\n    }\n  }\n}\n.\\[\\&_\\.recharts-polar-grid_\\[stroke\\=\\'\\#ccc\\'\\]\\]\\:stroke-border {\n  & .recharts-polar-grid [stroke='#ccc'] {\n    stroke: var(--border);\n  }\n}\n.\\[\\&_\\.recharts-radial-bar-background-sector\\]\\:fill-muted {\n  & .recharts-radial-bar-background-sector {\n    fill: var(--muted);\n  }\n}\n.\\[\\&_\\.recharts-rectangle\\.recharts-tooltip-cursor\\]\\:fill-muted {\n  & .recharts-rectangle.recharts-tooltip-cursor {\n    fill: var(--muted);\n  }\n}\n.\\[\\&_\\.recharts-reference-line_\\[stroke\\=\\'\\#ccc\\'\\]\\]\\:stroke-border {\n  & .recharts-reference-line [stroke='#ccc'] {\n    stroke: var(--border);\n  }\n}\n.\\[\\&_\\.recharts-sector\\]\\:outline-hidden {\n  & .recharts-sector {\n    --tw-outline-style: none;\n    outline-style: none;\n    @media (forced-colors: active) {\n      outline: 2px solid transparent;\n      outline-offset: 2px;\n    }\n  }\n}\n.\\[\\&_\\.recharts-sector\\[stroke\\=\\'\\#fff\\'\\]\\]\\:stroke-transparent {\n  & .recharts-sector[stroke='#fff'] {\n    stroke: transparent;\n  }\n}\n.\\[\\&_\\.recharts-surface\\]\\:outline-hidden {\n  & .recharts-surface {\n    --tw-outline-style: none;\n    outline-style: none;\n    @media (forced-colors: active) {\n      outline: 2px solid transparent;\n      outline-offset: 2px;\n    }\n  }\n}\n.\\[\\&_\\[cmdk-group-heading\\]\\]\\:px-2 {\n  & [cmdk-group-heading] {\n    padding-inline: calc(var(--spacing) * 2);\n  }\n}\n.\\[\\&_\\[cmdk-group-heading\\]\\]\\:py-1\\.5 {\n  & [cmdk-group-heading] {\n    padding-block: calc(var(--spacing) * 1.5);\n  }\n}\n.\\[\\&_\\[cmdk-group-heading\\]\\]\\:text-xs {\n  & [cmdk-group-heading] {\n    font-size: var(--text-xs);\n    line-height: var(--tw-leading, var(--text-xs--line-height));\n  }\n}\n.\\[\\&_\\[cmdk-group-heading\\]\\]\\:font-medium {\n  & [cmdk-group-heading] {\n    --tw-font-weight: var(--font-weight-medium);\n    font-weight: var(--font-weight-medium);\n  }\n}\n.\\[\\&_\\[cmdk-group-heading\\]\\]\\:text-muted-foreground {\n  & [cmdk-group-heading] {\n    color: var(--muted-foreground);\n  }\n}\n.\\[\\&_\\[cmdk-group\\]\\]\\:px-2 {\n  & [cmdk-group] {\n    padding-inline: calc(var(--spacing) * 2);\n  }\n}\n.\\[\\&_\\[cmdk-group\\]\\:not\\(\\[hidden\\]\\)_\\~\\[cmdk-group\\]\\]\\:pt-0 {\n  & [cmdk-group]:not([hidden]) ~[cmdk-group] {\n    padding-top: calc(var(--spacing) * 0);\n  }\n}\n.\\[\\&_\\[cmdk-input-wrapper\\]_svg\\]\\:h-5 {\n  & [cmdk-input-wrapper] svg {\n    height: calc(var(--spacing) * 5);\n  }\n}\n.\\[\\&_\\[cmdk-input-wrapper\\]_svg\\]\\:w-5 {\n  & [cmdk-input-wrapper] svg {\n    width: calc(var(--spacing) * 5);\n  }\n}\n.\\[\\&_\\[cmdk-input\\]\\]\\:h-12 {\n  & [cmdk-input] {\n    height: calc(var(--spacing) * 12);\n  }\n}\n.\\[\\&_\\[cmdk-item\\]\\]\\:px-2 {\n  & [cmdk-item] {\n    padding-inline: calc(var(--spacing) * 2);\n  }\n}\n.\\[\\&_\\[cmdk-item\\]\\]\\:py-3 {\n  & [cmdk-item] {\n    padding-block: calc(var(--spacing) * 3);\n  }\n}\n.\\[\\&_\\[cmdk-item\\]_svg\\]\\:h-5 {\n  & [cmdk-item] svg {\n    height: calc(var(--spacing) * 5);\n  }\n}\n.\\[\\&_\\[cmdk-item\\]_svg\\]\\:w-5 {\n  & [cmdk-item] svg {\n    width: calc(var(--spacing) * 5);\n  }\n}\n.\\[\\&_svg\\]\\:pointer-events-none {\n  & svg {\n    pointer-events: none;\n  }\n}\n.\\[\\&_svg\\]\\:shrink-0 {\n  & svg {\n    flex-shrink: 0;\n  }\n}\n.\\[\\&_svg\\:not\\(\\[class\\*\\=\\'size-\\'\\]\\)\\]\\:size-4 {\n  & svg:not([class*='size-']) {\n    width: calc(var(--spacing) * 4);\n    height: calc(var(--spacing) * 4);\n  }\n}\n.\\[\\&_svg\\:not\\(\\[class\\*\\=\\'size-\\'\\]\\)\\]\\:size-full {\n  & svg:not([class*='size-']) {\n    width: 100%;\n    height: 100%;\n  }\n}\n.\\[\\&_svg\\:not\\(\\[class\\*\\=\\'text-\\'\\]\\)\\]\\:text-muted-foreground {\n  & svg:not([class*='text-']) {\n    color: var(--muted-foreground);\n  }\n}\n.\\[\\&_tr\\]\\:border-b {\n  & tr {\n    border-bottom-style: var(--tw-border-style);\n    border-bottom-width: 1px;\n  }\n}\n.\\[\\&_tr\\:last-child\\]\\:border-0 {\n  & tr:last-child {\n    border-style: var(--tw-border-style);\n    border-width: 0px;\n  }\n}\n.\\[\\&\\:has\\(\\[role\\=checkbox\\]\\)\\]\\:pr-0 {\n  &:has([role=checkbox]) {\n    padding-right: calc(var(--spacing) * 0);\n  }\n}\n.\\[\\.border-b\\]\\:pb-6 {\n  &:is(.border-b) {\n    padding-bottom: calc(var(--spacing) * 6);\n  }\n}\n.\\[\\.border-t\\]\\:pt-0 {\n  &:is(.border-t) {\n    padding-top: calc(var(--spacing) * 0);\n  }\n}\n.\\[\\.border-t\\]\\:pt-6 {\n  &:is(.border-t) {\n    padding-top: calc(var(--spacing) * 6);\n  }\n}\n.\\*\\:\\[span\\]\\:last\\:flex {\n  :is(& > *) {\n    &:is(span) {\n      &:last-child {\n        display: flex;\n      }\n    }\n  }\n}\n.\\*\\:\\[span\\]\\:last\\:items-center {\n  :is(& > *) {\n    &:is(span) {\n      &:last-child {\n        align-items: center;\n      }\n    }\n  }\n}\n.\\*\\:\\[span\\]\\:last\\:gap-2 {\n  :is(& > *) {\n    &:is(span) {\n      &:last-child {\n        gap: calc(var(--spacing) * 2);\n      }\n    }\n  }\n}\n.data-\\[variant\\=destructive\\]\\:\\*\\:\\[svg\\]\\:\\!text-destructive {\n  &[data-variant=\"destructive\"] {\n    :is(& > *) {\n      &:is(svg) {\n        color: var(--destructive) !important;\n      }\n    }\n  }\n}\n.\\[\\&\\>\\[role\\=checkbox\\]\\]\\:translate-y-\\[2px\\] {\n  &>[role=checkbox] {\n    --tw-translate-y: 2px;\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n}\n.\\[\\&\\>button\\]\\:hidden {\n  &>button {\n    display: none;\n  }\n}\n.\\[\\&\\>span\\:last-child\\]\\:truncate {\n  &>span:last-child {\n    overflow: hidden;\n    text-overflow: ellipsis;\n    white-space: nowrap;\n  }\n}\n.\\[\\&\\>svg\\]\\:pointer-events-none {\n  &>svg {\n    pointer-events: none;\n  }\n}\n.\\[\\&\\>svg\\]\\:size-3 {\n  &>svg {\n    width: calc(var(--spacing) * 3);\n    height: calc(var(--spacing) * 3);\n  }\n}\n.\\[\\&\\>svg\\]\\:size-4 {\n  &>svg {\n    width: calc(var(--spacing) * 4);\n    height: calc(var(--spacing) * 4);\n  }\n}\n.\\[\\&\\>svg\\]\\:h-2\\.5 {\n  &>svg {\n    height: calc(var(--spacing) * 2.5);\n  }\n}\n.\\[\\&\\>svg\\]\\:h-3 {\n  &>svg {\n    height: calc(var(--spacing) * 3);\n  }\n}\n.\\[\\&\\>svg\\]\\:w-2\\.5 {\n  &>svg {\n    width: calc(var(--spacing) * 2.5);\n  }\n}\n.\\[\\&\\>svg\\]\\:w-3 {\n  &>svg {\n    width: calc(var(--spacing) * 3);\n  }\n}\n.\\[\\&\\>svg\\]\\:shrink-0 {\n  &>svg {\n    flex-shrink: 0;\n  }\n}\n.\\[\\&\\>svg\\]\\:text-muted-foreground {\n  &>svg {\n    color: var(--muted-foreground);\n  }\n}\n.\\[\\&\\>svg\\]\\:text-sidebar-accent-foreground {\n  &>svg {\n    color: var(--sidebar-accent-foreground);\n  }\n}\n.\\[\\&\\>tr\\]\\:last\\:border-b-0 {\n  &>tr {\n    &:last-child {\n      border-bottom-style: var(--tw-border-style);\n      border-bottom-width: 0px;\n    }\n  }\n}\n.\\[\\[data-side\\=left\\]\\[data-collapsible\\=offcanvas\\]_\\&\\]\\:-right-2 {\n  [data-side=left][data-collapsible=offcanvas] & {\n    right: calc(var(--spacing) * -2);\n  }\n}\n.\\[\\[data-side\\=left\\]\\[data-state\\=collapsed\\]_\\&\\]\\:cursor-e-resize {\n  [data-side=left][data-state=collapsed] & {\n    cursor: e-resize;\n  }\n}\n.\\[\\[data-side\\=right\\]\\[data-collapsible\\=offcanvas\\]_\\&\\]\\:-left-2 {\n  [data-side=right][data-collapsible=offcanvas] & {\n    left: calc(var(--spacing) * -2);\n  }\n}\n.\\[\\[data-side\\=right\\]\\[data-state\\=collapsed\\]_\\&\\]\\:cursor-w-resize {\n  [data-side=right][data-state=collapsed] & {\n    cursor: w-resize;\n  }\n}\n.\\[a\\&\\]\\:hover\\:bg-accent {\n  a& {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--accent);\n      }\n    }\n  }\n}\n.\\[a\\&\\]\\:hover\\:bg-destructive\\/90 {\n  a& {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--destructive);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--destructive) 90%, transparent);\n        }\n      }\n    }\n  }\n}\n.\\[a\\&\\]\\:hover\\:bg-primary\\/90 {\n  a& {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--primary);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--primary) 90%, transparent);\n        }\n      }\n    }\n  }\n}\n.\\[a\\&\\]\\:hover\\:bg-secondary\\/90 {\n  a& {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--secondary);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--secondary) 90%, transparent);\n        }\n      }\n    }\n  }\n}\n.\\[a\\&\\]\\:hover\\:text-accent-foreground {\n  a& {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--accent-foreground);\n      }\n    }\n  }\n}\n@layer theme, base, components, utilities;\n@layer theme {\n  :root, :host {\n    --font-sans: var(--font-sans);\n    --font-serif: var(--font-serif);\n    --font-mono: var(--font-mono);\n    --color-red-200: oklch(88.5% 0.062 18.334);\n    --color-red-300: oklch(80.8% 0.114 19.571);\n    --color-red-400: oklch(70.4% 0.191 22.216);\n    --color-red-500: oklch(63.7% 0.237 25.331);\n    --color-red-900: oklch(39.6% 0.141 25.723);\n    --color-amber-400: oklch(82.8% 0.189 84.429);\n    --color-amber-500: oklch(76.9% 0.188 70.08);\n    --color-amber-600: oklch(66.6% 0.179 58.318);\n    --color-amber-700: oklch(55.5% 0.163 48.998);\n    --color-yellow-300: oklch(90.5% 0.182 98.111);\n    --color-yellow-500: oklch(79.5% 0.184 86.047);\n    --color-yellow-600: oklch(68.1% 0.162 75.834);\n    --color-green-100: oklch(96.2% 0.044 156.743);\n    --color-green-300: oklch(87.1% 0.15 154.449);\n    --color-green-500: oklch(72.3% 0.219 149.579);\n    --color-green-600: oklch(62.7% 0.194 149.214);\n    --color-emerald-300: oklch(84.5% 0.143 164.978);\n    --color-emerald-400: oklch(76.5% 0.177 163.223);\n    --color-emerald-500: oklch(69.6% 0.17 162.48);\n    --color-emerald-600: oklch(59.6% 0.145 163.225);\n    --color-emerald-700: oklch(50.8% 0.118 165.612);\n    --color-emerald-800: oklch(43.2% 0.095 166.913);\n    --color-emerald-900: oklch(37.8% 0.077 168.94);\n    --color-cyan-500: oklch(71.5% 0.143 215.221);\n    --color-blue-300: oklch(80.9% 0.105 251.813);\n    --color-blue-400: oklch(70.7% 0.165 254.624);\n    --color-blue-500: oklch(62.3% 0.214 259.815);\n    --color-blue-600: oklch(54.6% 0.245 262.881);\n    --color-blue-700: oklch(48.8% 0.243 264.376);\n    --color-indigo-500: oklch(58.5% 0.233 277.117);\n    --color-violet-500: oklch(60.6% 0.25 292.717);\n    --color-rose-500: oklch(64.5% 0.246 16.439);\n    --color-gray-300: oklch(87.2% 0.01 258.338);\n    --color-gray-700: oklch(37.3% 0.034 259.733);\n    --color-zinc-300: oklch(87.1% 0.006 286.286);\n    --color-zinc-400: oklch(70.5% 0.015 286.067);\n    --color-zinc-500: oklch(55.2% 0.016 285.938);\n    --color-zinc-600: oklch(44.2% 0.017 285.786);\n    --color-zinc-700: oklch(37% 0.013 285.805);\n    --color-zinc-800: oklch(27.4% 0.006 286.033);\n    --color-neutral-500: oklch(55.6% 0 0);\n    --color-neutral-900: oklch(20.5% 0 0);\n    --color-stone-500: oklch(55.3% 0.013 58.071);\n    --color-black: #000;\n    --color-white: #fff;\n    --spacing: 0.25rem;\n    --container-sm: 24rem;\n    --container-md: 28rem;\n    --container-lg: 32rem;\n    --container-xl: 36rem;\n    --container-3xl: 48rem;\n    --container-6xl: 72rem;\n    --container-7xl: 80rem;\n    --text-xs: 0.75rem;\n    --text-xs--line-height: calc(1 / 0.75);\n    --text-sm: 0.875rem;\n    --text-sm--line-height: calc(1.25 / 0.875);\n    --text-base: 1rem;\n    --text-base--line-height: calc(1.5 / 1);\n    --text-lg: 1.125rem;\n    --text-lg--line-height: calc(1.75 / 1.125);\n    --text-xl: 1.25rem;\n    --text-xl--line-height: calc(1.75 / 1.25);\n    --text-2xl: 1.5rem;\n    --text-2xl--line-height: calc(2 / 1.5);\n    --text-3xl: 1.875rem;\n    --text-3xl--line-height: calc(2.25 / 1.875);\n    --text-4xl: 2.25rem;\n    --text-4xl--line-height: calc(2.5 / 2.25);\n    --text-6xl: 3.75rem;\n    --text-6xl--line-height: 1;\n    --text-8xl: 6rem;\n    --text-8xl--line-height: 1;\n    --font-weight-light: 300;\n    --font-weight-medium: 500;\n    --font-weight-semibold: 600;\n    --font-weight-bold: 700;\n    --tracking-tight: -0.025em;\n    --tracking-wide: 0.025em;\n    --tracking-widest: 0.1em;\n    --leading-relaxed: 1.625;\n    --radius-xs: 0.125rem;\n    --radius-3xl: 1.5rem;\n    --radius-4xl: 2rem;\n    --shadow-2xs: var(--shadow-2xs);\n    --shadow-xs: var(--shadow-xs);\n    --shadow-sm: var(--shadow-sm);\n    --shadow-md: var(--shadow-md);\n    --shadow-lg: var(--shadow-lg);\n    --shadow-xl: var(--shadow-xl);\n    --shadow-2xl: var(--shadow-2xl);\n    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);\n    --animate-spin: spin 1s linear infinite;\n    --animate-pulse: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\n    --blur-3xl: 64px;\n    --aspect-video: 16 / 9;\n    --default-transition-duration: 150ms;\n    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n    --default-font-family: var(--font-sans);\n    --default-mono-font-family: var(--font-mono);\n    --shadow: var(--shadow);\n    --color-border: var(--border);\n  }\n}\n@layer base {\n  *, ::after, ::before, ::backdrop, ::file-selector-button {\n    box-sizing: border-box;\n    margin: 0;\n    padding: 0;\n    border: 0 solid;\n  }\n  html, :host {\n    line-height: 1.5;\n    -webkit-text-size-adjust: 100%;\n    tab-size: 4;\n    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\");\n    font-feature-settings: var(--default-font-feature-settings, normal);\n    font-variation-settings: var(--default-font-variation-settings, normal);\n    -webkit-tap-highlight-color: transparent;\n  }\n  hr {\n    height: 0;\n    color: inherit;\n    border-top-width: 1px;\n  }\n  abbr:where([title]) {\n    -webkit-text-decoration: underline dotted;\n    text-decoration: underline dotted;\n  }\n  h1, h2, h3, h4, h5, h6 {\n    font-size: inherit;\n    font-weight: inherit;\n  }\n  a {\n    color: inherit;\n    -webkit-text-decoration: inherit;\n    text-decoration: inherit;\n  }\n  b, strong {\n    font-weight: bolder;\n  }\n  code, kbd, samp, pre {\n    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace);\n    font-feature-settings: var(--default-mono-font-feature-settings, normal);\n    font-variation-settings: var(--default-mono-font-variation-settings, normal);\n    font-size: 1em;\n  }\n  small {\n    font-size: 80%;\n  }\n  sub, sup {\n    font-size: 75%;\n    line-height: 0;\n    position: relative;\n    vertical-align: baseline;\n  }\n  sub {\n    bottom: -0.25em;\n  }\n  sup {\n    top: -0.5em;\n  }\n  table {\n    text-indent: 0;\n    border-color: inherit;\n    border-collapse: collapse;\n  }\n  :-moz-focusring {\n    outline: auto;\n  }\n  progress {\n    vertical-align: baseline;\n  }\n  summary {\n    display: list-item;\n  }\n  ol, ul, menu {\n    list-style: none;\n  }\n  img, svg, video, canvas, audio, iframe, embed, object {\n    display: block;\n    vertical-align: middle;\n  }\n  img, video {\n    max-width: 100%;\n    height: auto;\n  }\n  button, input, select, optgroup, textarea, ::file-selector-button {\n    font: inherit;\n    font-feature-settings: inherit;\n    font-variation-settings: inherit;\n    letter-spacing: inherit;\n    color: inherit;\n    border-radius: 0;\n    background-color: transparent;\n    opacity: 1;\n  }\n  :where(select:is([multiple], [size])) optgroup {\n    font-weight: bolder;\n  }\n  :where(select:is([multiple], [size])) optgroup option {\n    padding-inline-start: 20px;\n  }\n  ::file-selector-button {\n    margin-inline-end: 4px;\n  }\n  ::placeholder {\n    opacity: 1;\n  }\n  @supports (not (-webkit-appearance: -apple-pay-button))  or (contain-intrinsic-size: 1px) {\n    ::placeholder {\n      color: currentcolor;\n      @supports (color: color-mix(in lab, red, red)) {\n        color: color-mix(in oklab, currentcolor 50%, transparent);\n      }\n    }\n  }\n  textarea {\n    resize: vertical;\n  }\n  ::-webkit-search-decoration {\n    -webkit-appearance: none;\n  }\n  ::-webkit-date-and-time-value {\n    min-height: 1lh;\n    text-align: inherit;\n  }\n  ::-webkit-datetime-edit {\n    display: inline-flex;\n  }\n  ::-webkit-datetime-edit-fields-wrapper {\n    padding: 0;\n  }\n  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {\n    padding-block: 0;\n  }\n  :-moz-ui-invalid {\n    box-shadow: none;\n  }\n  button, input:where([type=\"button\"], [type=\"reset\"], [type=\"submit\"]), ::file-selector-button {\n    appearance: button;\n  }\n  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {\n    height: auto;\n  }\n  [hidden]:where(:not([hidden=\"until-found\"])) {\n    display: none !important;\n  }\n}\n@layer utilities;\n@property --tw-animation-delay {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0s;\n}\n@property --tw-animation-direction {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: normal;\n}\n@property --tw-animation-duration {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-animation-fill-mode {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: none;\n}\n@property --tw-animation-iteration-count {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 1;\n}\n@property --tw-enter-opacity {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 1;\n}\n@property --tw-enter-rotate {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-enter-scale {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 1;\n}\n@property --tw-enter-translate-x {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-enter-translate-y {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-exit-opacity {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 1;\n}\n@property --tw-exit-rotate {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-exit-scale {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 1;\n}\n@property --tw-exit-translate-x {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-exit-translate-y {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n:root {\n  --background: oklch(0.9821 0 0);\n  --foreground: oklch(0.2435 0 0);\n  --card: oklch(0.9911 0 0);\n  --card-foreground: oklch(0.2435 0 0);\n  --popover: oklch(0.9911 0 0);\n  --popover-foreground: oklch(0.2435 0 0);\n  --primary: oklch(0.4341 0.0392 41.9938);\n  --primary-foreground: oklch(1.0000 0 0);\n  --secondary: oklch(0.9200 0.0651 74.3695);\n  --secondary-foreground: oklch(0.3499 0.0685 40.8288);\n  --muted: oklch(0.9521 0 0);\n  --muted-foreground: oklch(0.5032 0 0);\n  --accent: oklch(0.9310 0 0);\n  --accent-foreground: oklch(0.2435 0 0);\n  --destructive: oklch(0.6271 0.1936 33.3390);\n  --destructive-foreground: oklch(1.0000 0 0);\n  --border: oklch(0.8822 0 0);\n  --input: oklch(0.8822 0 0);\n  --ring: oklch(0.4341 0.0392 41.9938);\n  --chart-1: oklch(0.4341 0.0392 41.9938);\n  --chart-2: oklch(0.9200 0.0651 74.3695);\n  --chart-3: oklch(0.9310 0 0);\n  --chart-4: oklch(0.9367 0.0523 75.5009);\n  --chart-5: oklch(0.4338 0.0437 41.6746);\n  --sidebar: oklch(0.9881 0 0);\n  --sidebar-foreground: oklch(0.2645 0 0);\n  --sidebar-primary: oklch(0.3250 0 0);\n  --sidebar-primary-foreground: oklch(0.9881 0 0);\n  --sidebar-accent: oklch(0.9761 0 0);\n  --sidebar-accent-foreground: oklch(0.3250 0 0);\n  --sidebar-border: oklch(0.9401 0 0);\n  --sidebar-ring: oklch(0.7731 0 0);\n  --font-sans: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';\n  --font-serif: ui-serif, Georgia, Cambria, \"Times New Roman\", Times, serif;\n  --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace;\n  --radius: 0.5rem;\n  --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);\n  --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);\n  --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);\n  --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);\n  --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 2px 4px -1px hsl(0 0% 0% / 0.10);\n  --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10);\n  --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 8px 10px -1px hsl(0 0% 0% / 0.10);\n  --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);\n  --tracking-normal: 0em;\n  --spacing: 0.25rem;\n}\n.dark {\n  --background: oklch(0.1776 0 0);\n  --foreground: oklch(0.9491 0 0);\n  --card: oklch(0.2134 0 0);\n  --card-foreground: oklch(0.9491 0 0);\n  --popover: oklch(0.2134 0 0);\n  --popover-foreground: oklch(0.9491 0 0);\n  --primary: oklch(0.9247 0.0524 66.1732);\n  --primary-foreground: oklch(0.2029 0.0240 200.1962);\n  --secondary: oklch(0.3163 0.0190 63.6992);\n  --secondary-foreground: oklch(0.9247 0.0524 66.1732);\n  --muted: oklch(0.2520 0 0);\n  --muted-foreground: oklch(0.7699 0 0);\n  --accent: oklch(0.2850 0 0);\n  --accent-foreground: oklch(0.9491 0 0);\n  --destructive: oklch(0.6271 0.1936 33.3390);\n  --destructive-foreground: oklch(1.0000 0 0);\n  --border: oklch(0.2351 0.0115 91.7467);\n  --input: oklch(0.4017 0 0);\n  --ring: oklch(0.9247 0.0524 66.1732);\n  --chart-1: oklch(0.9247 0.0524 66.1732);\n  --chart-2: oklch(0.3163 0.0190 63.6992);\n  --chart-3: oklch(0.2850 0 0);\n  --chart-4: oklch(0.3481 0.0219 67.0001);\n  --chart-5: oklch(0.9245 0.0533 67.0855);\n  --sidebar: oklch(0.2103 0.0059 285.8852);\n  --sidebar-foreground: oklch(0.9674 0.0013 286.3752);\n  --sidebar-primary: oklch(0.4882 0.2172 264.3763);\n  --sidebar-primary-foreground: oklch(1.0000 0 0);\n  --sidebar-accent: oklch(0.2739 0.0055 286.0326);\n  --sidebar-accent-foreground: oklch(0.9674 0.0013 286.3752);\n  --sidebar-border: oklch(0.2739 0.0055 286.0326);\n  --sidebar-ring: oklch(0.8711 0.0055 286.2860);\n  --font-sans: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';\n  --font-serif: ui-serif, Georgia, Cambria, \"Times New Roman\", Times, serif;\n  --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace;\n  --radius: 0.5rem;\n  --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);\n  --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);\n  --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);\n  --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);\n  --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 2px 4px -1px hsl(0 0% 0% / 0.10);\n  --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10);\n  --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 8px 10px -1px hsl(0 0% 0% / 0.10);\n  --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);\n}\n@layer base {\n  * {\n    border-color: var(--border);\n    outline-color: var(--ring);\n    @supports (color: color-mix(in lab, red, red)) {\n      outline-color: color-mix(in oklab, var(--ring) 50%, transparent);\n    }\n  }\n  body {\n    background-color: var(--background);\n    color: var(--foreground);\n  }\n}\n@keyframes pulse-fade {\n  0% {\n    transform: scale(0.25);\n    opacity: 1;\n  }\n  100% {\n    transform: scale(1);\n    opacity: 0;\n  }\n}\n.animate-pulse-fade-out {\n  animation: pulse-fade 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\n}\n@property --tw-translate-x {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-translate-y {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-translate-z {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-rotate-x {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-rotate-y {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-rotate-z {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-skew-x {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-skew-y {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-space-y-reverse {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-space-x-reverse {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-border-style {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: solid;\n}\n@property --tw-gradient-position {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-gradient-from {\n  syntax: \"<color>\";\n  inherits: false;\n  initial-value: #0000;\n}\n@property --tw-gradient-via {\n  syntax: \"<color>\";\n  inherits: false;\n  initial-value: #0000;\n}\n@property --tw-gradient-to {\n  syntax: \"<color>\";\n  inherits: false;\n  initial-value: #0000;\n}\n@property --tw-gradient-stops {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-gradient-via-stops {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-gradient-from-position {\n  syntax: \"<length-percentage>\";\n  inherits: false;\n  initial-value: 0%;\n}\n@property --tw-gradient-via-position {\n  syntax: \"<length-percentage>\";\n  inherits: false;\n  initial-value: 50%;\n}\n@property --tw-gradient-to-position {\n  syntax: \"<length-percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-leading {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-font-weight {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-tracking {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-ordinal {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-slashed-zero {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-numeric-figure {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-numeric-spacing {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-numeric-fraction {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-shadow-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-shadow-alpha {\n  syntax: \"<percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-inset-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-inset-shadow-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-inset-shadow-alpha {\n  syntax: \"<percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-ring-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-ring-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-inset-ring-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-inset-ring-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-ring-inset {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-ring-offset-width {\n  syntax: \"<length>\";\n  inherits: false;\n  initial-value: 0px;\n}\n@property --tw-ring-offset-color {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: #fff;\n}\n@property --tw-ring-offset-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-outline-style {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: solid;\n}\n@property --tw-blur {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-brightness {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-contrast {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-grayscale {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-hue-rotate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-invert {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-opacity {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-saturate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-sepia {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-drop-shadow {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-drop-shadow-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-drop-shadow-alpha {\n  syntax: \"<percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-drop-shadow-size {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-blur {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-brightness {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-contrast {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-grayscale {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-hue-rotate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-invert {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-opacity {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-saturate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-sepia {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-duration {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-ease {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-content {\n  syntax: \"*\";\n  initial-value: \"\";\n  inherits: false;\n}\n@keyframes spin {\n  to {\n    transform: rotate(360deg);\n  }\n}\n@keyframes pulse {\n  50% {\n    opacity: 0.5;\n  }\n}\n@keyframes enter {\n  from {\n    opacity: var(--tw-enter-opacity,1);\n    transform: translate3d(var(--tw-enter-translate-x,0),var(--tw-enter-translate-y,0),0)scale3d(var(--tw-enter-scale,1),var(--tw-enter-scale,1),var(--tw-enter-scale,1))rotate(var(--tw-enter-rotate,0));\n  }\n}\n@keyframes exit {\n  to {\n    opacity: var(--tw-exit-opacity,1);\n    transform: translate3d(var(--tw-exit-translate-x,0),var(--tw-exit-translate-y,0),0)scale3d(var(--tw-exit-scale,1),var(--tw-exit-scale,1),var(--tw-exit-scale,1))rotate(var(--tw-exit-rotate,0));\n  }\n}\n@layer properties {\n  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {\n    *, ::before, ::after, ::backdrop {\n      --tw-translate-x: 0;\n      --tw-translate-y: 0;\n      --tw-translate-z: 0;\n      --tw-rotate-x: initial;\n      --tw-rotate-y: initial;\n      --tw-rotate-z: initial;\n      --tw-skew-x: initial;\n      --tw-skew-y: initial;\n      --tw-space-y-reverse: 0;\n      --tw-space-x-reverse: 0;\n      --tw-border-style: solid;\n      --tw-gradient-position: initial;\n      --tw-gradient-from: #0000;\n      --tw-gradient-via: #0000;\n      --tw-gradient-to: #0000;\n      --tw-gradient-stops: initial;\n      --tw-gradient-via-stops: initial;\n      --tw-gradient-from-position: 0%;\n      --tw-gradient-via-position: 50%;\n      --tw-gradient-to-position: 100%;\n      --tw-leading: initial;\n      --tw-font-weight: initial;\n      --tw-tracking: initial;\n      --tw-ordinal: initial;\n      --tw-slashed-zero: initial;\n      --tw-numeric-figure: initial;\n      --tw-numeric-spacing: initial;\n      --tw-numeric-fraction: initial;\n      --tw-shadow: 0 0 #0000;\n      --tw-shadow-color: initial;\n      --tw-shadow-alpha: 100%;\n      --tw-inset-shadow: 0 0 #0000;\n      --tw-inset-shadow-color: initial;\n      --tw-inset-shadow-alpha: 100%;\n      --tw-ring-color: initial;\n      --tw-ring-shadow: 0 0 #0000;\n      --tw-inset-ring-color: initial;\n      --tw-inset-ring-shadow: 0 0 #0000;\n      --tw-ring-inset: initial;\n      --tw-ring-offset-width: 0px;\n      --tw-ring-offset-color: #fff;\n      --tw-ring-offset-shadow: 0 0 #0000;\n      --tw-outline-style: solid;\n      --tw-blur: initial;\n      --tw-brightness: initial;\n      --tw-contrast: initial;\n      --tw-grayscale: initial;\n      --tw-hue-rotate: initial;\n      --tw-invert: initial;\n      --tw-opacity: initial;\n      --tw-saturate: initial;\n      --tw-sepia: initial;\n      --tw-drop-shadow: initial;\n      --tw-drop-shadow-color: initial;\n      --tw-drop-shadow-alpha: 100%;\n      --tw-drop-shadow-size: initial;\n      --tw-backdrop-blur: initial;\n      --tw-backdrop-brightness: initial;\n      --tw-backdrop-contrast: initial;\n      --tw-backdrop-grayscale: initial;\n      --tw-backdrop-hue-rotate: initial;\n      --tw-backdrop-invert: initial;\n      --tw-backdrop-opacity: initial;\n      --tw-backdrop-saturate: initial;\n      --tw-backdrop-sepia: initial;\n      --tw-duration: initial;\n      --tw-ease: initial;\n      --tw-content: \"\";\n      --tw-animation-delay: 0s;\n      --tw-animation-direction: normal;\n      --tw-animation-duration: initial;\n      --tw-animation-fill-mode: none;\n      --tw-animation-iteration-count: 1;\n      --tw-enter-opacity: 1;\n      --tw-enter-rotate: 0;\n      --tw-enter-scale: 1;\n      --tw-enter-translate-x: 0;\n      --tw-enter-translate-y: 0;\n      --tw-exit-opacity: 1;\n      --tw-exit-rotate: 0;\n      --tw-exit-scale: 1;\n      --tw-exit-translate-x: 0;\n      --tw-exit-translate-y: 0;\n    }\n  }\n}"], "names": [], "mappings": "AACA;EA+8IE;IACE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA/8IJ;;;;AAIA;;;;AAGA;;;;AAGA;;;;;;;;;;;;AAWA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAEE;EAAyB;;;;;AAGzB;EAAyB;;;;;AAGzB;EAAyB;;;;;AAGzB;EAAyB;;;;;AAGzB;EAAyB;;;;;AAI3B;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;AAMA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAMA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAIE;;;;;;AAOA;;;;;;AAOA;;;;;;AAOA;;;;;;AAOA;;;;;;AAOA;;;;;;AAMF;;;;AAGA;;;;AAIE;;;;;;AAMF;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAEE;EAAgD;;;;;AAIlD;;;;AAEE;EAAgD;;;;;AAIlD;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAEE;EAAgD;;;;;AAIlD;;;;AAEE;EAAgD;;;;;AAIlD;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAEE;EAAgD;;;;;AAIlD;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAEE;EAAgD;;;;;AAIlD;;;;AAGA;;;;AAEE;EAAgD;;;;;AAIlD;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAEE;EAAgD;;;;;AAIlD;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAEE;EAAgD;;;;;AAIlD;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAEE;EAAgD;;;;;AAIlD;;;;AAEE;EAAgD;;;;;AAIlD;;;;AAEE;EAAgD;;;;;AAIlD;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAEE;EAAgD;;;;;AAIlD;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAEE;EAAgD;;;;;AAKlD;;;;;AAIA;;;;;AAEE;EAAgD;;;;;AAKlD;;;;;AAEE;EAAgD;;;;;AAKlD;;;;;AAEE;EAAgD;;;;;AAKlD;;;;;AAIA;;;;;AAEE;EAAgD;;;;;AAKlD;;;;;AAEE;EAAgD;;;;;AAKlD;;;;;AAIA;;;;;AAEE;EAAgD;;;;;AAKlD;;;;;;AAKA;;;;;;AAEE;EAAgD;;;;;AAMlD;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAEE;EAAgD;;;;;AAKlD;;;;;AAEE;EAAgD;;;;;AAKlD;;;;;AAIA;;;;;AAEE;EAAgD;;;;;AAKlD;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAEE;EAAgD;;;;;AAIlD;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAEE;EAAgD;;;;;AAIlD;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAEE;EAAgD;;;;;AAIlD;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAEE;EAAgD;;;;;AAIlD;;;;AAEE;EAAgD;;;;;AAIlD;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAEE;EAAgD;;;;;AAIlD;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAEE;EAAgD;;;;;AAIlD;;;;AAEE;EAAgD;;;;;AAIlD;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAGE;EAAgC;;;;;;AAKlC;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAIE;;;;AAKA;;;;AAKA;;;;AAME;EAAuB;;;;;AAMzB;;;;AAKA;;;;AAKA;;;;AAKA;;;;;AAMA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;;AAMA;;;;AAKA;;;;AAKA;;;;AAKA;;;;;AAMA;;;;AAKA;;;;AAKA;;;;;AAMA;;;;AAKA;;;;;AAMA;;;;AAKA;;;;;AAOE;EAAuB;;;;;AAMzB;;;;;AAMA;;;;AAEE;EAAgD;;;;;AAMlD;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAEE;EAAgD;;;;;AAMlD;;;;AAQA;;;;AAQA;;;;AAKA;;;;AAKA;;;;;AAMA;;;;AAKA;;;;;AAMA;;;;;AAMA;;;;AAKA;;;;AAKA;;;;;AAMA;;;;;AAMA;;;;;AAMA;;;;;AAMA;;;;;AAMA;;;;;AAMA;;;;;AAMA;;;;;AAOE;;;;;AAQA;EAAuB;;;;;AAOvB;EAAuB;;;;;AAOvB;EAAuB;;;;;AAOvB;EAAuB;;;;EAErB;IAAgD;;;;;;AAQlD;EAAuB;;;;;AAOvB;EAAuB;;;;;AAOvB;EAAuB;;;;EAErB;IAAgD;;;;;;AAQlD;EAAuB;;;;EAErB;IAAgD;;;;;;AAQlD;EAAuB;;;;EAErB;IAAgD;;;;;;AAQlD;EAAuB;;;;EAErB;IAAgD;;;;;;AAQlD;EAAuB;;;;EAErB;IAAgD;;;;;;AAQlD;EAAuB;;;;;AAOvB;EAAuB;;;;;AAOvB;EAAuB;;;;;AAOvB;EAAuB;;;;;AAOvB;EAAuB;;;;;AAOvB;EAAuB;;;;;AAOvB;EAAuB;;;;;;AAQvB;EAAuB;;;;;;AAQvB;EAAuB;;;;EAErB;IAAgD;;;;;;AAQlD;EAAuB;;;;EAErB;IAAgD;;;;;;AAQlD;EACE;;;;;AAQF;EACE;;;;;;AAQJ;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;;AAMA;;;;;AAMA;;;;AAKA;;;;AAKA;;;;;AAMA;;;;;AAGE;EAAgC;;;;;;AAOlC;;;;;AAMA;;;;AAKA;;;;;AAMA;;;;;AAMA;;;;;AAMA;;;;;AAMA;;;;AAEE;EAAgD;;;;;AAMlD;;;;AAEE;EAAgD;;;;;AAMlD;;;;;AAMA;;;;;AAMA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAEE;EAAgD;;;;;AAMlD;;;;AAKA;;;;;AAMA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;;AAMA;;;;AAKA;;;;;AAMA;;;;AAKA;;;;;AAMA;;;;AAKA;;;;;AAMA;;;;AAKA;;;;AAKA;;;;AAME;;;;AAOA;;;;;;;AAUA;;;;AAOA;;;;AAOA;;;;AAMF;;;;;AAMA;;;;AAKA;;;;AAKA;;;;;AAMA;;;;AAMA;;;;AAMA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;;AAMA;;;;AAMA;;;;AAMA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAOI;EAAuB;;;;;AASvB;EAAuB;;;;;AAO3B;;;;AAKA;;;;;AAMA;;;;AAKA;;;;AAME;;;;AAEE;EAAgD;;;;;AAQlD;;;;AAMF;EAAyB;;;;;AAKzB;EAAyB;;;;;AAKzB;EAAyB;;;;;AAKzB;EAAyB;;;;;AAKzB;EAAyB;;;;;AAKzB;EAAyB;;;;;AAKzB;EAAyB;;;;;AAKzB;EAAyB;;;;;AAKzB;EAAyB;;;;;AAKzB;EAAyB;;;;;;AAMzB;EAAyB;;;;;AAKzB;EAAyB;;;;;AAKzB;EAAyB;;;;;AAKzB;EAAyB;;;;;AAKzB;EAAyB;;;;;AAKzB;EAAyB;;;;;AAKzB;EAAyB;;;;;AAKzB;EAAyB;;;;;AAKzB;EAAyB;;;;;AAKzB;EAAyB;;;;;AAKzB;EAAyB;;;;;AAKzB;EAAyB;;;;;AAKzB;EAAyB;;;;;AAKzB;EAAyB;;;;;AAKzB;EAAyB;;;;;AAKzB;EAAyB;;;;;AAKzB;EAAyB;;;;;AAKzB;EAAyB;;;;;AAKzB;EAAyB;;;;;AAKzB;EAAyB;;;;;AAKzB;EAAyB;;;;;;AAMzB;EAAyB;;;;;;AAMzB;EAAyB;;;;;;AAMzB;EAAyB;;;;;AAKzB;EACE;;;;;AAMF;EACE;;;;;AAMF;EACE;;;;;AAMF;EACE;;;;;;AAOF;EAEI;;;;;AAOJ;EACE;;;;;;AAOF;EAAyB;;;;;AAKzB;EAAyB;;;;;AAKzB;EAAyB;;;;;AAKzB;EAAyB;;;;;AAKzB;EAAyB;;;;;AAKzB;EAAyB;;;;;AAKzB;EAAyB;;;;;AAKzB;EAAyB;;;;;AAKzB;;;;AAKA;;;;AAKA;;;;AAEE;EAAgD;;;;;AAMlD;;;;AAKA;;;;AAEE;EAAgD;;;;;AAMlD;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAOI;EAAuB;;;;EAErB;IAAgD;;;;;;AAUlD;EAAuB;;;;EAErB;IAAgD;;;;;;AASpD;;;;AAEE;EAAgD;;;;;AAQlD;;;;AAEE;EAAgD;;;;;AAQlD;;;;AAOA;;;;AAOA;;;;AAEE;EAAgD;;;;;AAShD;;;;AAEE;EAAgD;;;;;AAQtD;;;;AAKA;;;;AAEE;EAAgD;;;;;AAMlD;;;;AAKA;;;;AAKA;;;;;AAGE;EAAgC;;;;;;AAOlC;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;;AAGE;EAAgC;;;;;;AAOlC;;;;AAKA;;;;;AAGE;EAAgC;;;;;;AAOlC;;;;AAKA;;;;AAKA;;;;;AAMA;;;;;AAMA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;;AAMA;;;;;AAMA;;;;AAKA;;;;;AAMA;;;;;AAMA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAOI;;;;AASA;;;;AASA;;;;AASA;;;;AAOJ;;;;;AAMA;;;;AAKA;;;;;;AAOA;;;;AAKA;;;;;AAMA;;;;;AAMA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAME;;;;;AAOF;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAOI;EAAuB;;;;;AASvB;EAAuB;;;;EAErB;IAAgD;;;;;;AAUlD;EAAuB;;;;EAErB;IAAgD;;;;;;AAUlD;EAAuB;;;;EAErB;IAAgD;;;;;;AAUlD;EAAuB;;;;;AAM7B;EAEE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAFF;EA+GE;;;;;;;EAMA;;;;;;;;;;EASA;;;;;;EAKA;;;;EAIA;;;;;EAIA;;;;;;EAKA;;;;EAGA;;;;;;;EAMA;;;;EAGA;;;;;;;EAMA;;;;EAGA;;;;EAGA;;;;;;EAKA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;;;;;;;EAUA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;IACE;;;;IAEE;MAAgD;;;;;;EAKpD;;;;EAGA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EA8KA;;;;;EAGE;IAAgD;;;;;EAIlD;;;;;;AAhbF;;AAgQA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgDA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2DA;;;;;;;;;;;;AAUA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;;AAMA"}}]}