{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/js/web3/packages/chario/src/components/md/not-authenticated.jsx"], "sourcesContent": ["'use client'\r\n\r\nimport React from 'react'\r\nimport { Button } from '@/components/ui/button'\r\nimport Link from 'next/link'\r\nimport { authClient, signOut, useSession } from '@/lib/auth-client'\r\nimport { useTranslations } from '@/contexts/LanguageContext'\r\nimport { useRouter } from 'next/navigation'\r\n\r\nfunction NotAuthenticated() {\r\n  const t = useTranslations();\r\n  const router = useRouter()\r\n  const sessionData = useSession();\r\n  return (\r\n    <div className='h-full w-full flex flex-col items-center justify-center p-8 text-center'>\r\n      <h1 className='font-bold text-2xl'>{t('auth.notAuthorized')}</h1>\r\n      <p className='text-muted-foreground text-sm mb-8'>{t('auth.notAuthorizedDescription')}</p>\r\n      <Button\r\n        className={'cursor-pointer'}\r\n        onClick={async () => {\r\n          router.push('/signin')\t\r\n          await signOut({\r\n            fetchOptions: {\r\n              onSuccess: () => {\r\n                // router.push(\"/signin\")\r\n                router.push('/signin')\r\n                // toast.success('You have been logged out')\r\n              }\r\n            }\r\n          })\r\n        }}\r\n      >\r\n        {t('auth.signIn')}\r\n      </Button>\r\n    </div >\r\n  )\r\n}\r\n\r\nexport default NotAuthenticated"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AASA,SAAS;IACP,MAAM,IAAI,CAAA,GAAA,mIAAA,CAAA,kBAAe,AAAD;IACxB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,cAAc,CAAA,GAAA,4HAAA,CAAA,aAAU,AAAD;IAC7B,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAAsB,EAAE;;;;;;0BACtC,8OAAC;gBAAE,WAAU;0BAAsC,EAAE;;;;;;0BACrD,8OAAC,kIAAA,CAAA,SAAM;gBACL,WAAW;gBACX,SAAS;oBACP,OAAO,IAAI,CAAC;oBACZ,MAAM,CAAA,GAAA,4HAAA,CAAA,UAAO,AAAD,EAAE;wBACZ,cAAc;4BACZ,WAAW;gCACT,yBAAyB;gCACzB,OAAO,IAAI,CAAC;4BACZ,4CAA4C;4BAC9C;wBACF;oBACF;gBACF;0BAEC,EAAE;;;;;;;;;;;;AAIX;uCAEe", "debugId": null}}]}