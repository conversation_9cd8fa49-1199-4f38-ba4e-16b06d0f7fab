(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/@reown/appkit/node_modules/@noble/curves/esm/secp256k1.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/7e465_@noble_392c73fc._.js",
  "static/chunks/7e465_@noble_curves_esm_secp256k1_8fc7386c.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit/node_modules/@noble/curves/esm/secp256k1.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-controllers/node_modules/@noble/curves/esm/secp256k1.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/104f9_@noble_d050d202._.js",
  "static/chunks/104f9_@noble_curves_esm_secp256k1_8fc7386c.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-controllers/node_modules/@noble/curves/esm/secp256k1.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/exports/basic.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_19429eda._.js",
  "static/chunks/node_modules_f5ec5f0a._.js",
  "static/chunks/node_modules_@reown_appkit-scaffold-ui_dist_esm_exports_basic_7c25403c.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/exports/basic.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/exports/w3m-modal.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_56578fd4._.js",
  "static/chunks/node_modules_5b20d2bf._.js",
  "static/chunks/node_modules_@reown_appkit-scaffold-ui_dist_esm_exports_w3m-modal_7c25403c.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/exports/w3m-modal.js [app-client] (ecmascript)");
    });
});
}}),
}]);