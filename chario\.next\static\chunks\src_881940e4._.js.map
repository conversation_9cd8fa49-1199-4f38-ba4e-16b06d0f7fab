{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/js/web3/packages/chario/src/providers/web3.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { QueryClient, QueryClientProvider } from \"@tanstack/react-query\";\r\nimport { WagmiProvider } from \"wagmi\";\r\nimport { getDefaultConfig, RainbowKitProvider } from \"@rainbow-me/rainbowkit\";\r\nimport { hardhat, sepolia } from \"wagmi/chains\";\r\n\r\nconst queryClient = new QueryClient();\r\n\r\nconst config = getDefaultConfig({\r\n    appName: 'Solidity Next.js Starter',\r\n    projectId: process.env.NEXT_PUBLIC_RAINBOWKIT_PROJECT_ID ?? \"\",\r\n    chains: [hardhat, sepolia],\r\n    ssr: true,\r\n});\r\n\r\nconst Providers = ({ children }) => (\r\n    <WagmiProvider config={config}>\r\n        <QueryClientProvider client={queryClient}>\r\n            <RainbowKitProvider>{children}</RainbowKitProvider>\r\n        </QueryClientProvider>\r\n    </WagmiProvider>\r\n);\r\n\r\nexport { Providers };\r\n"], "names": [], "mappings": ";;;AAWe;;AATf;AAAA;AACA;AACA;AACA;AAAA;AALA;;;;;;AAOA,MAAM,cAAc,IAAI,gLAAA,CAAA,cAAW;AAEnC,MAAM,SAAS,CAAA,GAAA,iLAAA,CAAA,mBAAgB,AAAD,EAAE;IAC5B,SAAS;IACT,WAAW,wEAAiD;IAC5D,QAAQ;QAAC,mKAAA,CAAA,UAAO;QAAE,mKAAA,CAAA,UAAO;KAAC;IAC1B,KAAK;AACT;AAEA,MAAM,YAAY,CAAC,EAAE,QAAQ,EAAE,iBAC3B,6LAAC,kJAAA,CAAA,gBAAa;QAAC,QAAQ;kBACnB,cAAA,6LAAC,yLAAA,CAAA,sBAAmB;YAAC,QAAQ;sBACzB,cAAA,6LAAC,iLAAA,CAAA,qBAAkB;0BAAE;;;;;;;;;;;;;;;;KAH3B", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/js/web3/packages/chario/src/providers/theme-provider.jsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport { ThemeProvider as NextThemesProvider } from \"next-themes\"\r\n\r\nexport function ThemeProvider({\r\n    children,\r\n    ...props\r\n}) {\r\n    return <NextThemesProvider {...props}>{children}</NextThemesProvider>\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKO,SAAS,cAAc,EAC1B,QAAQ,EACR,GAAG,OACN;IACG,qBAAO,6LAAC,mJAAA,CAAA,gBAAkB;QAAE,GAAG,KAAK;kBAAG;;;;;;AAC3C;KALgB", "debugId": null}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/js/web3/packages/chario/src/lib/utils.js"], "sourcesContent": ["import { clsx } from \"clsx\";\r\nimport { ethers } from \"ethers\";\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n\r\nexport function sleep(ms) {\r\n  return new Promise((resolve) => setTimeout(resolve, ms));\r\n}\r\n\r\nexport const daysLeft = (deadline) => {\r\n  const difference = new Date(deadline).getTime() - Date.now();\r\n  const remainingDays = difference / (1000 * 3600 * 24);\r\n\r\n  return remainingDays.toFixed(0);\r\n};\r\n\r\nexport const calculateBarPercentage = (goal, raisedAmount) => {\r\n  const percentage = Math.round((raisedAmount * 100) / goal);\r\n\r\n  return percentage;\r\n};\r\n\r\nexport const checkIfImage = (url, callback) => {\r\n  const img = new Image();\r\n  img.src = url;\r\n\r\n  if (img.complete) callback(true);\r\n\r\n  img.onload = () => callback(true);\r\n  img.onerror = () => callback(false);\r\n};\r\n\r\n/**\r\n * Adds two ETH amounts represented as strings.\r\n * \r\n * @param {string} ethAmount1 - First ETH amount as string (e.g. \"0.037\")\r\n * @param {string} ethAmount2 - Second ETH amount as string (e.g. \"0.005\")\r\n * @returns {string} - Sum of the two amounts as ETH string (e.g. \"0.042\")\r\n */\r\nexport function addEthAmounts(ethAmount1, ethAmount2) {\r\n  // Convert ETH strings to bigint wei values\r\n  const wei1 = ethers.parseEther(ethAmount1);\r\n  const wei2 = ethers.parseEther(ethAmount2);\r\n\r\n  // Add the bigint wei amounts using native +\r\n  const sumWei = wei1 + wei2;\r\n\r\n  // Convert back to ETH string\r\n  return ethers.formatEther(sumWei);\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;AACA;;;;AAEO,SAAS,GAAG,GAAG,MAAM;IAC1B,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,MAAM,EAAE;IACtB,OAAO,IAAI,QAAQ,CAAC,UAAY,WAAW,SAAS;AACtD;AAEO,MAAM,WAAW,CAAC;IACvB,MAAM,aAAa,IAAI,KAAK,UAAU,OAAO,KAAK,KAAK,GAAG;IAC1D,MAAM,gBAAgB,aAAa,CAAC,OAAO,OAAO,EAAE;IAEpD,OAAO,cAAc,OAAO,CAAC;AAC/B;AAEO,MAAM,yBAAyB,CAAC,MAAM;IAC3C,MAAM,aAAa,KAAK,KAAK,CAAC,AAAC,eAAe,MAAO;IAErD,OAAO;AACT;AAEO,MAAM,eAAe,CAAC,KAAK;IAChC,MAAM,MAAM,IAAI;IAChB,IAAI,GAAG,GAAG;IAEV,IAAI,IAAI,QAAQ,EAAE,SAAS;IAE3B,IAAI,MAAM,GAAG,IAAM,SAAS;IAC5B,IAAI,OAAO,GAAG,IAAM,SAAS;AAC/B;AASO,SAAS,cAAc,UAAU,EAAE,UAAU;IAClD,2CAA2C;IAC3C,MAAM,OAAO,mLAAA,CAAA,SAAM,CAAC,UAAU,CAAC;IAC/B,MAAM,OAAO,mLAAA,CAAA,SAAM,CAAC,UAAU,CAAC;IAE/B,4CAA4C;IAC5C,MAAM,SAAS,OAAO;IAEtB,6BAA6B;IAC7B,OAAO,mLAAA,CAAA,SAAM,CAAC,WAAW,CAAC;AAC5B", "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/js/web3/packages/chario/src/components/ui/dialog.jsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\r\nimport { XIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Dialog({\r\n  ...props\r\n}) {\r\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />;\r\n}\r\n\r\nfunction DialogTrigger({\r\n  ...props\r\n}) {\r\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />;\r\n}\r\n\r\nfunction DialogPortal({\r\n  ...props\r\n}) {\r\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />;\r\n}\r\n\r\nfunction DialogClose({\r\n  ...props\r\n}) {\r\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />;\r\n}\r\n\r\nfunction DialogOverlay({\r\n  className,\r\n  ...props\r\n}) {\r\n  return (\r\n    (<DialogPrimitive.Overlay\r\n      data-slot=\"dialog-overlay\"\r\n      className={cn(\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\r\n        className\r\n      )}\r\n      {...props} />)\r\n  );\r\n}\r\n\r\nfunction DialogContent({\r\n  className,\r\n  children,\r\n  showCloseButton = true,\r\n  ...props\r\n}) {\r\n  return (\r\n    (<DialogPortal data-slot=\"dialog-portal\">\r\n      <DialogOverlay />\r\n      <DialogPrimitive.Content\r\n        data-slot=\"dialog-content\"\r\n        className={cn(\r\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\r\n          className\r\n        )}\r\n        {...props}>\r\n        {children}\r\n        {showCloseButton && (\r\n          <DialogPrimitive.Close\r\n            data-slot=\"dialog-close\"\r\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\">\r\n            <XIcon />\r\n            <span className=\"sr-only\">Close</span>\r\n          </DialogPrimitive.Close>\r\n        )}\r\n      </DialogPrimitive.Content>\r\n    </DialogPortal>)\r\n  );\r\n}\r\n\r\nfunction DialogHeader({\r\n  className,\r\n  ...props\r\n}) {\r\n  return (\r\n    (<div\r\n      data-slot=\"dialog-header\"\r\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\r\n      {...props} />)\r\n  );\r\n}\r\n\r\nfunction DialogFooter({\r\n  className,\r\n  ...props\r\n}) {\r\n  return (\r\n    (<div\r\n      data-slot=\"dialog-footer\"\r\n      className={cn(\"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\", className)}\r\n      {...props} />)\r\n  );\r\n}\r\n\r\nfunction DialogTitle({\r\n  className,\r\n  ...props\r\n}) {\r\n  return (\r\n    (<DialogPrimitive.Title\r\n      data-slot=\"dialog-title\"\r\n      className={cn(\"text-lg leading-none font-semibold\", className)}\r\n      {...props} />)\r\n  );\r\n}\r\n\r\nfunction DialogDescription({\r\n  className,\r\n  ...props\r\n}) {\r\n  return (\r\n    (<DialogPrimitive.Description\r\n      data-slot=\"dialog-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props} />)\r\n  );\r\n}\r\n\r\nexport {\r\n  Dialog,\r\n  DialogClose,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogOverlay,\r\n  DialogPortal,\r\n  DialogTitle,\r\n  DialogTrigger,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OACJ;IACC,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,cAAc,EACrB,GAAG,OACJ;IACC,qBAAO,6LAAC,qKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS;AAMT,SAAS,aAAa,EACpB,GAAG,OACJ;IACC,qBAAO,6LAAC,qKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACJ;IACC,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACJ;IACC,qBACG,6LAAC,qKAAA,CAAA,UAAuB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAEf;MAbS;AAeT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OACJ;IACC,qBACG,6LAAC;QAAa,aAAU;;0BACvB,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBACR;oBACA,iCACC,6LAAC,qKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CACV,6LAAC,mMAAA,CAAA,QAAK;;;;;0CACN,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;MA5BS;AA8BT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACJ;IACC,qBACG,6LAAC;QACA,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAEf;MAVS;AAYT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACJ;IACC,qBACG,6LAAC;QACA,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0DAA0D;QACvE,GAAG,KAAK;;;;;;AAEf;MAVS;AAYT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACJ;IACC,qBACG,6LAAC,qKAAA,CAAA,QAAqB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAEf;MAVS;AAYT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACJ;IACC,qBACG,6LAAC,qKAAA,CAAA,cAA2B;QAC3B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAEf;MAVS", "debugId": null}}, {"offset": {"line": 354, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/js/web3/packages/chario/src/components/ui/label.jsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Label({\r\n  className,\r\n  ...props\r\n}) {\r\n  return (\r\n    (<LabelPrimitive.Root\r\n      data-slot=\"label\"\r\n      className={cn(\r\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\r\n        className\r\n      )}\r\n      {...props} />)\r\n  );\r\n}\r\n\r\nexport { Label }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OACJ;IACC,qBACG,6LAAC,oKAAA,CAAA,OAAmB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAEf;KAbS", "debugId": null}}, {"offset": {"line": 390, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/js/web3/packages/chario/src/components/ui/form.jsx"], "sourcesContent": ["\"use client\";\r\nimport * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { Controller, FormProvider, useFormContext, useFormState } from \"react-hook-form\";\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { Label } from \"@/components/ui/label\"\r\n\r\nconst Form = FormProvider\r\n\r\nconst FormFieldContext = React.createContext({})\r\n\r\nconst FormField = (\r\n  {\r\n    ...props\r\n  }\r\n) => {\r\n  return (\r\n    (<FormFieldContext.Provider value={{ name: props.name }}>\r\n      <Controller {...props} />\r\n    </FormFieldContext.Provider>)\r\n  );\r\n}\r\n\r\nconst useFormField = () => {\r\n  const fieldContext = React.useContext(FormFieldContext)\r\n  const itemContext = React.useContext(FormItemContext)\r\n  const { getFieldState } = useFormContext()\r\n  const formState = useFormState({ name: fieldContext.name })\r\n  const fieldState = getFieldState(fieldContext.name, formState)\r\n\r\n  if (!fieldContext) {\r\n    throw new Error(\"useFormField should be used within <FormField>\")\r\n  }\r\n\r\n  const { id } = itemContext\r\n\r\n  return {\r\n    id,\r\n    name: fieldContext.name,\r\n    formItemId: `${id}-form-item`,\r\n    formDescriptionId: `${id}-form-item-description`,\r\n    formMessageId: `${id}-form-item-message`,\r\n    ...fieldState,\r\n  }\r\n}\r\n\r\nconst FormItemContext = React.createContext({})\r\n\r\nfunction FormItem({\r\n  className,\r\n  ...props\r\n}) {\r\n  const id = React.useId()\r\n\r\n  return (\r\n    (<FormItemContext.Provider value={{ id }}>\r\n      <div data-slot=\"form-item\" className={cn(\"grid gap-2\", className)} {...props} />\r\n    </FormItemContext.Provider>)\r\n  );\r\n}\r\n\r\nfunction FormLabel({\r\n  className,\r\n  ...props\r\n}) {\r\n  const { error, formItemId } = useFormField()\r\n\r\n  return (\r\n    (<Label\r\n      data-slot=\"form-label\"\r\n      data-error={!!error}\r\n      className={cn(\"data-[error=true]:text-destructive\", className)}\r\n      htmlFor={formItemId}\r\n      {...props} />)\r\n  );\r\n}\r\n\r\nfunction FormControl({\r\n  ...props\r\n}) {\r\n  const { error, formItemId, formDescriptionId, formMessageId } = useFormField()\r\n\r\n  return (\r\n    (<Slot\r\n      data-slot=\"form-control\"\r\n      id={formItemId}\r\n      aria-describedby={\r\n        !error\r\n          ? `${formDescriptionId}`\r\n          : `${formDescriptionId} ${formMessageId}`\r\n      }\r\n      aria-invalid={!!error}\r\n      {...props} />)\r\n  );\r\n}\r\n\r\nfunction FormDescription({\r\n  className,\r\n  ...props\r\n}) {\r\n  const { formDescriptionId } = useFormField()\r\n\r\n  return (\r\n    (<p\r\n      data-slot=\"form-description\"\r\n      id={formDescriptionId}\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props} />)\r\n  );\r\n}\r\n\r\nfunction FormMessage({\r\n  className,\r\n  ...props\r\n}) {\r\n  const { error, formMessageId } = useFormField()\r\n  const body = error ? String(error?.message ?? \"\") : props.children\r\n\r\n  if (!body) {\r\n    return null\r\n  }\r\n\r\n  return (\r\n    (<p\r\n      data-slot=\"form-message\"\r\n      id={formMessageId}\r\n      className={cn(\"text-destructive text-sm\", className)}\r\n      {...props}>\r\n      {body}\r\n    </p>)\r\n  );\r\n}\r\n\r\nexport {\r\n  useFormField,\r\n  Form,\r\n  FormItem,\r\n  FormLabel,\r\n  FormControl,\r\n  FormDescription,\r\n  FormMessage,\r\n  FormField,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AACA;AACA;AACA;AAEA;AACA;;;AANA;;;;;;AAQA,MAAM,OAAO,iKAAA,CAAA,eAAY;AAEzB,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,CAAC;AAE9C,MAAM,YAAY,CAChB,EACE,GAAG,OACJ;IAED,qBACG,6LAAC,iBAAiB,QAAQ;QAAC,OAAO;YAAE,MAAM,MAAM,IAAI;QAAC;kBACpD,cAAA,6LAAC,iKAAA,CAAA,aAAU;YAAE,GAAG,KAAK;;;;;;;;;;;AAG3B;KAVM;AAYN,MAAM,eAAe;;IACnB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;IACtC,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;IACrC,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,iBAAc,AAAD;IACvC,MAAM,YAAY,CAAA,GAAA,iKAAA,CAAA,eAAY,AAAD,EAAE;QAAE,MAAM,aAAa,IAAI;IAAC;IACzD,MAAM,aAAa,cAAc,aAAa,IAAI,EAAE;IAEpD,IAAI,CAAC,cAAc;QACjB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,EAAE,EAAE,GAAG;IAEf,OAAO;QACL;QACA,MAAM,aAAa,IAAI;QACvB,YAAY,GAAG,GAAG,UAAU,CAAC;QAC7B,mBAAmB,GAAG,GAAG,sBAAsB,CAAC;QAChD,eAAe,GAAG,GAAG,kBAAkB,CAAC;QACxC,GAAG,UAAU;IACf;AACF;GArBM;;QAGsB,iKAAA,CAAA,iBAAc;QACtB,iKAAA,CAAA,eAAY;;;AAmBhC,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,CAAC;AAE7C,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACJ;;IACC,MAAM,KAAK,CAAA,GAAA,6JAAA,CAAA,QAAW,AAAD;IAErB,qBACG,6LAAC,gBAAgB,QAAQ;QAAC,OAAO;YAAE;QAAG;kBACrC,cAAA,6LAAC;YAAI,aAAU;YAAY,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAa,GAAG,KAAK;;;;;;;;;;;AAGlF;IAXS;MAAA;AAaT,SAAS,UAAU,EACjB,SAAS,EACT,GAAG,OACJ;;IACC,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG;IAE9B,qBACG,6LAAC,oIAAA,CAAA,QAAK;QACL,aAAU;QACV,cAAY,CAAC,CAAC;QACd,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACpD,SAAS;QACR,GAAG,KAAK;;;;;;AAEf;IAdS;;QAIuB;;;MAJvB;AAgBT,SAAS,YAAY,EACnB,GAAG,OACJ;;IACC,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,iBAAiB,EAAE,aAAa,EAAE,GAAG;IAEhE,qBACG,6LAAC,mKAAA,CAAA,OAAI;QACJ,aAAU;QACV,IAAI;QACJ,oBACE,CAAC,QACG,GAAG,mBAAmB,GACtB,GAAG,kBAAkB,CAAC,EAAE,eAAe;QAE7C,gBAAc,CAAC,CAAC;QACf,GAAG,KAAK;;;;;;AAEf;IAjBS;;QAGyD;;;MAHzD;AAmBT,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACJ;;IACC,MAAM,EAAE,iBAAiB,EAAE,GAAG;IAE9B,qBACG,6LAAC;QACA,aAAU;QACV,IAAI;QACJ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAEf;IAbS;;QAIuB;;;MAJvB;AAeT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACJ;;IACC,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG;IACjC,MAAM,OAAO,QAAQ,OAAO,OAAO,WAAW,MAAM,MAAM,QAAQ;IAElE,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACG,6LAAC;QACA,aAAU;QACV,IAAI;QACJ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;kBACR;;;;;;AAGP;IApBS;;QAI0B;;;MAJ1B", "debugId": null}}, {"offset": {"line": 592, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/js/web3/packages/chario/src/components/ui/input.jsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Input({\r\n  className,\r\n  type,\r\n  ...props\r\n}) {\r\n  return (\r\n    (<input\r\n      type={type}\r\n      data-slot=\"input\"\r\n      className={cn(\r\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[1px]\",\r\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n        className\r\n      )}\r\n      {...props} />)\r\n  );\r\n}\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,SAAS,MAAM,EACb,SAAS,EACT,IAAI,EACJ,GAAG,OACJ;IACC,qBACG,6LAAC;QACA,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAEf;KAjBS", "debugId": null}}, {"offset": {"line": 626, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/js/web3/packages/chario/src/components/ui/button.jsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva } from \"class-variance-authority\";\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\r\n        ghost:\r\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\r\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\r\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\r\n        icon: \"size-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n\r\n  return (\r\n    (<Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props} />)\r\n  );\r\n}\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OACJ;IACC,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACG,6LAAC;QACA,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAEf;KAfS", "debugId": null}}, {"offset": {"line": 691, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/js/web3/packages/chario/src/hooks/use-modal-store.js"], "sourcesContent": ["// 'use client'\r\nimport { create } from \"zustand\";\r\n\r\n// export type ModalType = \"createServer\" | \"invite\" | \"editServer\" | \"members\" | \"createChannel\" | \"editChannel\" | \"leaveServer\" | \"deleteServer\" | \"deleteChannel\" | \"editChannel\" | \"messageFile\" | \"deleteMessage\";\r\n\r\n// interface ModalData {\r\n//     server ?: Server;\r\n//     channel ?: Channel;\r\n//     channelType ?: ChannelType;\r\n//     apiUrl ?: string;|\r\n//     query ?: Record<string, any>;\r\n// }\r\n\r\n// interface ModalStore {\r\n//     type: string;\r\n// data: ModalData;\r\n//     isOpen: Boolean;\r\n//     onOpen: (type: ModalType, data?: ModalData) => void;\r\n//     onClose: () => void;\r\n// }\r\n\r\nexport const useModal = create((set) => ({\r\n    general: {},\r\n    type: null,\r\n    data: {},\r\n    isOpen: false,\r\n    onOpen: (type, data) => {\r\n        set({\r\n            type,\r\n            data,\r\n            isOpen: true,\r\n        })\r\n    },\r\n    onClose: () => {\r\n        set({\r\n            type: null,\r\n            isOpen: false,\r\n        })\r\n    },\r\n\r\n    setGeneral: (data) => {\r\n        set({\r\n            general: data,\r\n        })\r\n    },\r\n}));"], "names": [], "mappings": "AAAA,eAAe;;;;AACf;;AAoBO,MAAM,WAAW,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,EAAE,CAAC,MAAQ,CAAC;QACrC,SAAS,CAAC;QACV,MAAM;QACN,MAAM,CAAC;QACP,QAAQ;QACR,QAAQ,CAAC,MAAM;YACX,IAAI;gBACA;gBACA;gBACA,QAAQ;YACZ;QACJ;QACA,SAAS;YACL,IAAI;gBACA,MAAM;gBACN,QAAQ;YACZ;QACJ;QAEA,YAAY,CAAC;YACT,IAAI;gBACA,SAAS;YACb;QACJ;IACJ,CAAC", "debugId": null}}, {"offset": {"line": 730, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/js/web3/packages/chario/src/components/lg/modals/donation-action-modal.jsx"], "sourcesContent": ["'use client'\r\n\r\nimport React, { useEffect, useState } from 'react'\r\nimport { <PERSON><PERSON>, DialogClose, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'\r\nimport { useForm } from 'react-hook-form'\r\nimport { z } from 'zod'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'\r\nimport { Input } from '@/components/ui/input'\r\nimport { Button } from '@/components/ui/button'\r\nimport { useParams, useRouter } from 'next/navigation'\r\nimport { useModal } from '@/hooks/use-modal-store'\r\nimport { ArrowUpRight, Check, CheckIcon, Copy, CopyCheckIcon, UserCircleIcon } from 'lucide-react'\r\nimport { toast } from 'sonner'\r\nimport Link from 'next/link'\r\n\r\n// Import other necessary modules and components\r\n\r\nfunction useClipboard() {\r\n    const [copied, setCopied] = useState(false);\r\n\r\n    const copyToClipboard = async (text) => {\r\n        try {\r\n            await navigator.clipboard.writeText(text);\r\n            toast.success('Copied to clipboard');\r\n            setCopied(true);\r\n            setTimeout(() => {\r\n                setCopied(false);\r\n            }, 1000);\r\n        } catch (err) {\r\n            console.error('Failed to copy text: ', err);\r\n        }\r\n    };\r\n\r\n    return { copied, copyToClipboard };\r\n}\r\n\r\nfunction DonationActionModal() {\r\n    const { isOpen, onOpen, onClose, type, data } = useModal();\r\n    const { copied, copyToClipboard } = useClipboard();\r\n    const router = useRouter();\r\n    const params = useParams();\r\n    const isModalOpen = isOpen && type === 'donationAction';\r\n    const { donation } = data || {};\r\n\r\n    const handleClose = () => {\r\n        onClose();\r\n    };\r\n\r\n    const handleKeyDown = (event) => {\r\n        if (event.key === 'Enter') {\r\n            event.preventDefault(); // Prevent the default behavior of the Enter key\r\n        }\r\n    };\r\n\r\n    const handleCopy = (text) => {\r\n        copyToClipboard(text);\r\n    };\r\n\r\n    return (\r\n        <Dialog open={isModalOpen} onOpenChange={onClose}>\r\n            <DialogContent\r\n                className=\"overflow-hidden w-[30rem] p-4 bg-card ring-1 ring-muted\"\r\n                onKeyDown={handleKeyDown}\r\n            >\r\n                {/* <p className='text-muted-foreground text-lg'>\r\n                    Choose what you want to do with this donation\r\n                </p> */}\r\n                <div className='mb-4'>\r\n                    <div className='text-muted-foreground text-sm cursor-pointer' onClick={() => handleCopy(donation?.txHash)}>\r\n                        <span className='text-md text-accent-foreground font-semibold'>Transaction hash:</span>  {`${donation?.txHash.substring(0, 8)}...${donation?.txHash.substring(donation?.txHash.length - 6, donation?.txHash.length)}`}\r\n                        <button className='ml-2 text-xs text-muted-foreground cursor-pointer focus-visible:ring-0 focus-visible:outline-none outline-non focus:outline-none'>\r\n                            {copied ? (\r\n                                <CheckIcon size={14}/>\r\n                            ) : (\r\n                                <Copy size={14}/>\r\n                            )}\r\n                        </button>\r\n                    </div>\r\n                    <div className='text-muted-foreground text-sm'>\r\n                        <span className='text-md text-accent-foreground font-semibold'>Donators name:</span>  {donation?.donorUser?.name}\r\n                    </div>\r\n                </div>\r\n                <div className=\"flex gap-4 items-center w-full\">\r\n                    <Link\r\n                        href={`/home/<USER>/${donation?.donorUser?.id}`}\r\n                         className=\"flex-1 flex items-center cursor-pointer\"\r\n                    >\r\n                        <Button variant=\"outline\" className=\"flex-1 flex items-center focus-visible:ring-0 cursor-pointer\">\r\n                            <span className=\"text-muted-foreground text-sm font-semibold\">\r\n                                View profile\r\n                            </span>\r\n                            <UserCircleIcon />\r\n                        </Button>\r\n                    </Link>\r\n                    <Link\r\n                        href={`https://sepolia.etherscan.io/tx/${donation?.txHash}#internal`}\r\n                        target=\"_blank\"\r\n                        className=\"flex-1 flex items-center cursor-pointer\"\r\n                    >\r\n                        <Button className=\"flex-1 flex items-center cursor-pointer\">\r\n                            <span className=\" text-sm font-semibold cursor-pointer\">\r\n                                Go to Donation\r\n                            </span>\r\n                            <ArrowUpRight />\r\n                        </Button>\r\n                    </Link>\r\n                </div>\r\n            </DialogContent>\r\n        </Dialog>\r\n    );\r\n}\r\n\r\nexport default DonationActionModal;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;;;AAdA;;;;;;;;;;;;;;AAgBA,gDAAgD;AAEhD,SAAS;;IACL,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,kBAAkB,OAAO;QAC3B,IAAI;YACA,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,UAAU;YACV,WAAW;gBACP,UAAU;YACd,GAAG;QACP,EAAE,OAAO,KAAK;YACV,QAAQ,KAAK,CAAC,yBAAyB;QAC3C;IACJ;IAEA,OAAO;QAAE;QAAQ;IAAgB;AACrC;GAjBS;AAmBT,SAAS;;IACL,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD;IACvD,MAAM,EAAE,MAAM,EAAE,eAAe,EAAE,GAAG;IACpC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,cAAc,UAAU,SAAS;IACvC,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,CAAC;IAE9B,MAAM,cAAc;QAChB;IACJ;IAEA,MAAM,gBAAgB,CAAC;QACnB,IAAI,MAAM,GAAG,KAAK,SAAS;YACvB,MAAM,cAAc,IAAI,gDAAgD;QAC5E;IACJ;IAEA,MAAM,aAAa,CAAC;QAChB,gBAAgB;IACpB;IAEA,qBACI,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAa,cAAc;kBACrC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YACV,WAAU;YACV,WAAW;;8BAKX,6LAAC;oBAAI,WAAU;;sCACX,6LAAC;4BAAI,WAAU;4BAA+C,SAAS,IAAM,WAAW,UAAU;;8CAC9F,6LAAC;oCAAK,WAAU;8CAA+C;;;;;;gCAAwB;gCAAG,GAAG,UAAU,OAAO,UAAU,GAAG,GAAG,GAAG,EAAE,UAAU,OAAO,UAAU,UAAU,OAAO,SAAS,GAAG,UAAU,OAAO,SAAS;8CACrN,6LAAC;oCAAO,WAAU;8CACb,uBACG,6LAAC,2MAAA,CAAA,YAAS;wCAAC,MAAM;;;;;6DAEjB,6LAAC,qMAAA,CAAA,OAAI;wCAAC,MAAM;;;;;;;;;;;;;;;;;sCAIxB,6LAAC;4BAAI,WAAU;;8CACX,6LAAC;oCAAK,WAAU;8CAA+C;;;;;;gCAAqB;gCAAG,UAAU,WAAW;;;;;;;;;;;;;8BAGpH,6LAAC;oBAAI,WAAU;;sCACX,6LAAC,+JAAA,CAAA,UAAI;4BACD,MAAM,CAAC,YAAY,EAAE,UAAU,WAAW,IAAI;4BAC7C,WAAU;sCAEX,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,WAAU;;kDAChC,6LAAC;wCAAK,WAAU;kDAA8C;;;;;;kDAG9D,6LAAC,yNAAA,CAAA,iBAAc;;;;;;;;;;;;;;;;sCAGvB,6LAAC,+JAAA,CAAA,UAAI;4BACD,MAAM,CAAC,gCAAgC,EAAE,UAAU,OAAO,SAAS,CAAC;4BACpE,QAAO;4BACP,WAAU;sCAEV,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCAAC,WAAU;;kDACd,6LAAC;wCAAK,WAAU;kDAAwC;;;;;;kDAGxD,6LAAC,6NAAA,CAAA,eAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOzC;IA1ES;;QAC2C,wIAAA,CAAA,WAAQ;QACpB;QACrB,qIAAA,CAAA,YAAS;QACT,qIAAA,CAAA,YAAS;;;KAJnB;uCA4EM", "debugId": null}}, {"offset": {"line": 984, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/js/web3/packages/chario/src/providers/modal-provider.jsx"], "sourcesContent": ["'use client'\r\n\r\nimport React, { useEffect, useState } from 'react'\r\nimport DonationActionModal from '@/components/lg/modals/donation-action-modal'\r\n\r\n\r\nconst ModalProvider = () => {\r\n    const [isMounted, setIsMounted] = useState(false)\r\n\r\n    useEffect(() => {\r\n        setIsMounted(true)\r\n    }, [])\r\n\r\n    if (!isMounted) {\r\n        return null\r\n    }\r\n\r\n    return (\r\n        <>\r\n            <DonationActionModal />\r\n        </>\r\n    )\r\n}\r\n\r\nexport default ModalProvider"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAMA,MAAM,gBAAgB;;IAClB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACN,aAAa;QACjB;kCAAG,EAAE;IAEL,IAAI,CAAC,WAAW;QACZ,OAAO;IACX;IAEA,qBACI;kBACI,cAAA,6LAAC,oKAAA,CAAA,UAAmB;;;;;;AAGhC;GAhBM;KAAA;uCAkBS", "debugId": null}}, {"offset": {"line": 1038, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/js/web3/packages/chario/src/contexts/LanguageContext.jsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useState, useEffect } from 'react';\n\n// Import translation files\nimport ruTranslations from '../messages/ru.json';\nimport enTranslations from '../messages/en.json';\n\nconst translations = {\n  ru: ruTranslations,\n  en: enTranslations\n};\n\nconst LanguageContext = createContext();\n\nexport function LanguageProvider({ children }) {\n  const [language, setLanguage] = useState('ru'); // Default to Russian\n\n  // Load language from localStorage on mount\n  useEffect(() => {\n    const savedLanguage = localStorage.getItem('language');\n    if (savedLanguage && translations[savedLanguage]) {\n      setLanguage(savedLanguage);\n    }\n  }, []);\n\n  // Save language to localStorage when it changes\n  useEffect(() => {\n    localStorage.setItem('language', language);\n  }, [language]);\n\n  const changeLanguage = (newLanguage) => {\n    if (translations[newLanguage]) {\n      setLanguage(newLanguage);\n    }\n  };\n\n  const t = (key) => {\n    const keys = key.split('.');\n    let value = translations[language];\n    \n    for (const k of keys) {\n      if (value && typeof value === 'object') {\n        value = value[k];\n      } else {\n        return key; // Return key if translation not found\n      }\n    }\n    \n    return value || key;\n  };\n\n  const value = {\n    language,\n    changeLanguage,\n    t,\n    availableLanguages: Object.keys(translations)\n  };\n\n  return (\n    <LanguageContext.Provider value={value}>\n      {children}\n    </LanguageContext.Provider>\n  );\n}\n\nexport function useLanguage() {\n  const context = useContext(LanguageContext);\n  if (!context) {\n    throw new Error('useLanguage must be used within a LanguageProvider');\n  }\n  return context;\n}\n\n// Convenience hook for just the translation function\nexport function useTranslations() {\n  const { t } = useLanguage();\n  return t;\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AAEA,2BAA2B;AAC3B;AACA;;;AANA;;;;AAQA,MAAM,eAAe;IACnB,IAAI,6FAAA,CAAA,UAAc;IAClB,IAAI,6FAAA,CAAA,UAAc;AACpB;AAEA,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD;AAE7B,SAAS,iBAAiB,EAAE,QAAQ,EAAE;;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,qBAAqB;IAErE,2CAA2C;IAC3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM,gBAAgB,aAAa,OAAO,CAAC;YAC3C,IAAI,iBAAiB,YAAY,CAAC,cAAc,EAAE;gBAChD,YAAY;YACd;QACF;qCAAG,EAAE;IAEL,gDAAgD;IAChD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,aAAa,OAAO,CAAC,YAAY;QACnC;qCAAG;QAAC;KAAS;IAEb,MAAM,iBAAiB,CAAC;QACtB,IAAI,YAAY,CAAC,YAAY,EAAE;YAC7B,YAAY;QACd;IACF;IAEA,MAAM,IAAI,CAAC;QACT,MAAM,OAAO,IAAI,KAAK,CAAC;QACvB,IAAI,QAAQ,YAAY,CAAC,SAAS;QAElC,KAAK,MAAM,KAAK,KAAM;YACpB,IAAI,SAAS,OAAO,UAAU,UAAU;gBACtC,QAAQ,KAAK,CAAC,EAAE;YAClB,OAAO;gBACL,OAAO,KAAK,sCAAsC;YACpD;QACF;QAEA,OAAO,SAAS;IAClB;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA,oBAAoB,OAAO,IAAI,CAAC;IAClC;IAEA,qBACE,6LAAC,gBAAgB,QAAQ;QAAC,OAAO;kBAC9B;;;;;;AAGP;GAjDgB;KAAA;AAmDT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB;AAST,SAAS;;IACd,MAAM,EAAE,CAAC,EAAE,GAAG;IACd,OAAO;AACT;IAHgB;;QACA", "debugId": null}}]}