import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { Providers } from "@/providers/web3";
import { ThemeProvider } from "@/providers/theme-provider";
import { Toaster } from "sonner";
import ModalProvider from "@/providers/modal-provider";
import { LanguageProvider } from "@/contexts/LanguageContext";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata = {
  title: "Chario - Blockchain Charity Platform",
  description: "Platform for creating and supporting charity campaigns using blockchain technology",
};

export default function RootLayout({ children }) {
  return (
    <html lang="ru" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <LanguageProvider>
          <ThemeProvider
            attribute="class"
            defaultTheme="light"
            enableSystem
            disableTransitionOnChange
          >
            <Providers>
              {children}
              <Toaster />
              <ModalProvider />
            </Providers>
          </ThemeProvider>
        </LanguageProvider>
      </body>
    </html>
  );
}
