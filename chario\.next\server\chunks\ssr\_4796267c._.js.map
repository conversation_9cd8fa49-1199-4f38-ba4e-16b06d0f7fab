{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/js/web3/packages/chario/src/components/ui/badge.jsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst badgeVariants = cva(\r\n    \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\r\n    {\r\n        variants: {\r\n            variant: {\r\n                default:\r\n                    \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\r\n                secondary:\r\n                    \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\r\n                destructive:\r\n                    \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n                outline:\r\n                    \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\r\n            },\r\n        },\r\n        defaultVariants: {\r\n            variant: \"default\",\r\n        },\r\n    }\r\n)\r\n\r\nfunction Badge({\r\n    className,\r\n    variant,\r\n    asChild = false,\r\n    ...props\r\n}) {\r\n    const Comp = asChild ? Slot : \"span\"\r\n\r\n    return (\r\n        <Comp\r\n            data-slot=\"badge\"\r\n            className={cn(badgeVariants({ variant }), className)}\r\n            {...props}\r\n        />\r\n    )\r\n}\r\n\r\nexport { Badge, badgeVariants }\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACpB,kZACA;IACI,UAAU;QACN,SAAS;YACL,SACI;YACJ,WACI;YACJ,aACI;YACJ,SACI;QACR;IACJ;IACA,iBAAiB;QACb,SAAS;IACb;AACJ;AAGJ,SAAS,MAAM,EACX,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OACN;IACG,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACI,8OAAC;QACG,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGrB", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/js/web3/packages/chario/src/components/ui/progress.jsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Progress({\r\n  className,\r\n  value,\r\n  ...props\r\n}) {\r\n  return (\r\n    (<ProgressPrimitive.Root\r\n      data-slot=\"progress\"\r\n      className={cn(\r\n        \"bg-primary/20 relative h-1 max-h-1 w-full overflow-hidden rounded-full\",\r\n        className\r\n      )}\r\n      {...props}>\r\n      <ProgressPrimitive.Indicator\r\n        data-slot=\"progress-indicator\"\r\n        className=\"bg-emerald-500 dark:bg-emerald-600 h-full w-full flex-1 transition-all\"\r\n        style={{ transform: `translateX(-${100 - (value || 0)}%)` }} />\r\n    </ProgressPrimitive.Root>)\r\n  );\r\n}\r\n\r\nexport { Progress }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,SAAS,SAAS,EAChB,SAAS,EACT,KAAK,EACL,GAAG,OACJ;IACC,qBACG,8OAAC,oKAAA,CAAA,OAAsB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0EACA;QAED,GAAG,KAAK;kBACT,cAAA,8OAAC,oKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAGlE", "debugId": null}}, {"offset": {"line": 96, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/js/web3/packages/chario/src/components/md/charity-card-md.jsx"], "sourcesContent": ["'use client'\r\nimport React from 'react'\r\nimport { Badge } from '../ui/badge';\r\nimport { Progress } from '../ui/progress';\r\nimport { cn } from '@/lib/utils';\r\nimport Image from 'next/image';\r\nimport Link from 'next/link';\r\n\r\nfunction CharityCardMd({ charity, className }) {\r\n    const [daysLeft, setDaysLeft] = React.useState(0);\r\n\r\n    React.useEffect(() => {\r\n        const newDaysLeft = Math.floor((Number(charity.deadline) - Date.now() / 1000) / 86400);\r\n        setDaysLeft(newDaysLeft);\r\n        console.log(charity.deadline, newDaysLeft);\r\n    }, [charity.deadline]);\r\n\r\n    return (\r\n        <Link\r\n            href={charity?.link ? charity.link : `/home/<USER>/${charity.id}`}\r\n            className={cn(\r\n                'overflow-hidden relative min-w-96 max-w-96 min-h-[24rem] bg-muted dark:bg-card rounded-3xl ring-1 ring-card-foreground/10 transition-all duration-200 hover:ring-2 hover:ring-card-foreground/10 cursor-pointer flex flex-col',\r\n                className\r\n            )}\r\n        >\r\n            <div className=\"relative p-1\">\r\n                {charity?.image ? (\r\n                    <Image\r\n                        width={384}\r\n                        height={192}\r\n                        src={charity.image}\r\n                        alt={charity.title}\r\n                        className=\"w-full h-48 object-cover rounded-t-3xl rounded-b-4xl\"\r\n                    />\r\n                ) : (\r\n                    <div className=\"w-full h-48 bg-accent rounded-t-3xl rounded-b-4xl flex items-center justify-center\">\r\n                        <span className=\"text-zinc-400\">Image preview</span>\r\n                    </div>\r\n                )}\r\n                {daysLeft > 0 && (\r\n                    <Badge className=\"absolute top-4 right-4 bg-primary-foreground text-white font-semibold px-2 ring ring-primary/50 shadow-xl\">\r\n                        {daysLeft}d left\r\n                    </Badge>\r\n                )}\r\n            </div>\r\n            <div className=\"p-4 pt-1 flex flex-col flex-grow\">\r\n                <h3 className='text-xl font-semibold text-accent-foreground mb-2 line-clamp-1'>{charity.title}</h3>\r\n                <p className='text-muted-foreground mb-6 line-clamp-4 text-sm'>{charity.description}</p>\r\n                {/* {(charity.target !== '0' || !charity?.id) ? ( */}\r\n                {(charity.target !== '0' && charity.target) ? (\r\n                    <div className=\"flex flex-col gap-1 mt-auto\">\r\n                        <div className='flex justify-between text-xs'>\r\n                            <span className='text-zinc-400'>Target</span>\r\n                            <span className='text-primary font-sm'>\r\n                                {charity.target} ETH\r\n                            </span>\r\n                        </div>\r\n                        <Progress\r\n                            value={(charity.amountCollected / charity.target) * 100}\r\n                            className='h-2 bg-zinc-700'\r\n                        />\r\n                        <div className='flex justify-between text-xs'>\r\n                            <span className='text-zinc-400'>Raised</span>\r\n                            <span className=' font-sm text-emerald-400'>\r\n                                {charity.amountCollected} ETH\r\n                            </span>\r\n                        </div>\r\n                    </div>\r\n                ) : (\r\n                    <div className='flex justify-between text-xs mt-auto'>\r\n                        <span className='text-zinc-400'>Raised</span>\r\n                        <span className='text-emerald-400 font-sm'>\r\n                            {charity.amountCollected} ETH\r\n                        </span>\r\n                    </div>\r\n                )}\r\n            </div>\r\n        </Link>\r\n    )\r\n}\r\n\r\nexport default CharityCardMd;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AANA;;;;;;;;AAQA,SAAS,cAAc,EAAE,OAAO,EAAE,SAAS,EAAE;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAE/C,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACZ,MAAM,cAAc,KAAK,KAAK,CAAC,CAAC,OAAO,QAAQ,QAAQ,IAAI,KAAK,GAAG,KAAK,IAAI,IAAI;QAChF,YAAY;QACZ,QAAQ,GAAG,CAAC,QAAQ,QAAQ,EAAE;IAClC,GAAG;QAAC,QAAQ,QAAQ;KAAC;IAErB,qBACI,8OAAC,4JAAA,CAAA,UAAI;QACD,MAAM,SAAS,OAAO,QAAQ,IAAI,GAAG,CAAC,gBAAgB,EAAE,QAAQ,EAAE,EAAE;QACpE,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACR,iOACA;;0BAGJ,8OAAC;gBAAI,WAAU;;oBACV,SAAS,sBACN,8OAAC,6HAAA,CAAA,UAAK;wBACF,OAAO;wBACP,QAAQ;wBACR,KAAK,QAAQ,KAAK;wBAClB,KAAK,QAAQ,KAAK;wBAClB,WAAU;;;;;6CAGd,8OAAC;wBAAI,WAAU;kCACX,cAAA,8OAAC;4BAAK,WAAU;sCAAgB;;;;;;;;;;;oBAGvC,WAAW,mBACR,8OAAC,iIAAA,CAAA,QAAK;wBAAC,WAAU;;4BACZ;4BAAS;;;;;;;;;;;;;0BAItB,8OAAC;gBAAI,WAAU;;kCACX,8OAAC;wBAAG,WAAU;kCAAkE,QAAQ,KAAK;;;;;;kCAC7F,8OAAC;wBAAE,WAAU;kCAAmD,QAAQ,WAAW;;;;;;oBAEjF,QAAQ,MAAM,KAAK,OAAO,QAAQ,MAAM,iBACtC,8OAAC;wBAAI,WAAU;;0CACX,8OAAC;gCAAI,WAAU;;kDACX,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,8OAAC;wCAAK,WAAU;;4CACX,QAAQ,MAAM;4CAAC;;;;;;;;;;;;;0CAGxB,8OAAC,oIAAA,CAAA,WAAQ;gCACL,OAAO,AAAC,QAAQ,eAAe,GAAG,QAAQ,MAAM,GAAI;gCACpD,WAAU;;;;;;0CAEd,8OAAC;gCAAI,WAAU;;kDACX,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,8OAAC;wCAAK,WAAU;;4CACX,QAAQ,eAAe;4CAAC;;;;;;;;;;;;;;;;;;6CAKrC,8OAAC;wBAAI,WAAU;;0CACX,8OAAC;gCAAK,WAAU;0CAAgB;;;;;;0CAChC,8OAAC;gCAAK,WAAU;;oCACX,QAAQ,eAAe;oCAAC;;;;;;;;;;;;;;;;;;;;;;;;;AAOrD;uCAEe", "debugId": null}}, {"offset": {"line": 311, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/js/web3/packages/chario/src/components/md/search-bar.jsx"], "sourcesContent": ["'use client'\r\nimport React from 'react'\r\nimport { But<PERSON> } from '../ui/button'\r\nimport { SearchIcon } from 'lucide-react'\r\nimport { Input } from '../ui/input'\r\nimport { useTranslations } from '@/contexts/LanguageContext'\r\n\r\nfunction SearchBar({ search, setSearch, submitSearch }) {\r\n    const t = useTranslations();\r\n    const inputRef = React.useRef(null);\r\n    return (\r\n        <form\r\n            onSubmit={submitSearch}\r\n            onClick={() => inputRef.current.focus()}\r\n            className='cursor-text p-0.5 rounded-full w-full max-w-[600px] flex items-center justify-between  ring-1 ring-card-foreground/10 bg-card transition-all duration-200 hover:ring-2 hover:ring-card-foreground/10'\r\n        >\r\n            <input\r\n                ref={inputRef}\r\n                type='text'\r\n                className='bg-transparent outline-none pl-5 w-full'\r\n                placeholder='Search for a charity'\r\n                value={search}\r\n                onChange={(e) => setSearch(e.target.value)}\r\n            />\r\n            <button\r\n                className='bg-transparent text-primary rounded-full mr-1 ml-2 cursor-pointer cursor-pointer p-3 hover:bg-primary/30'\r\n                onClick={submitSearch}\r\n            >\r\n                <SearchIcon size={22} />\r\n            </button>\r\n        </form>\r\n    )\r\n}\r\n\r\nexport default SearchBar"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AALA;;;;;;;AAOA,SAAS,UAAU,EAAE,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE;IAClD,MAAM,IAAI,CAAA,GAAA,mIAAA,CAAA,kBAAe,AAAD;IACxB,MAAM,WAAW,qMAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC9B,qBACI,8OAAC;QACG,UAAU;QACV,SAAS,IAAM,SAAS,OAAO,CAAC,KAAK;QACrC,WAAU;;0BAEV,8OAAC;gBACG,KAAK;gBACL,MAAK;gBACL,WAAU;gBACV,aAAY;gBACZ,OAAO;gBACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;;;;;;0BAE7C,8OAAC;gBACG,WAAU;gBACV,SAAS;0BAET,cAAA,8OAAC,0MAAA,CAAA,aAAU;oBAAC,MAAM;;;;;;;;;;;;;;;;;AAIlC;uCAEe", "debugId": null}}, {"offset": {"line": 381, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/js/web3/packages/chario/src/hooks/use-chario.js"], "sourcesContent": ["// \"use client\";\r\n\r\n// import { useState, useCallback, useEffect } from 'react';\r\n// import {\r\n//     useAccount,\r\n//     useReadContract,\r\n//     useWriteContract,\r\n//     useWaitForTransactionReceipt,\r\n// } from \"wagmi\";\r\n// import abi from \"@/abi/chario.json\"; // Update to your contract's ABI\r\n// import { parseEther, formatEther } from 'viem';\r\n// import { toast } from \"sonner\";\r\n\r\n// const CHARIO_ABI = abi\r\n// const CONTRACT_ADDRESS = process.env.NEXT_PUBLIC_CONTRACT_ADDRESS\r\n\r\n// export const useChario = () => {\r\n//     const { address, isConnected } = useAccount();\r\n//     const [isLoading, setIsLoading] = useState(false);\r\n//     const [pendingTxHash, setPendingTxHash] = useState(null);\r\n\r\n//     // Write contract hook\r\n//     const { writeContract, data: txHash, error: writeError, isPending: isWritePending } = useWriteContract();\r\n\r\n//     // Wait for transaction receipt\r\n//     const { isLoading: isConfirming, isSuccess: isConfirmed } = useWaitForTransactionReceipt({\r\n//         hash: txHash,\r\n//     });\r\n\r\n//     // Handle transaction completion\r\n//     useEffect(() => {\r\n//         if (txHash) {\r\n//             console.log('Transaction Hash:', txHash);\r\n//         }\r\n//         if (isConfirmed && pendingTxHash) {\r\n//             toast.success('Transaction confirmed!');\r\n//             setIsLoading(false);\r\n//             setPendingTxHash(null);\r\n//         }\r\n//     }, [isConfirmed, pendingTxHash, txHash]); // Add txHash to dependency array\r\n\r\n//     useEffect(() => {\r\n//         if (writeError) {\r\n//             console.error('Wagmi Write Error:', writeError);\r\n//             toast.error('Transaction failed: ' + writeError.message);\r\n//             setIsLoading(false);\r\n//             setPendingTxHash(null);\r\n//         }\r\n//     }, [writeError]);\r\n\r\n//     // Read contract data\r\n//     const { data: numberOfCharities, refetch: refetchCharityCount } = useReadContract({\r\n//         address: CONTRACT_ADDRESS,\r\n//         abi: CHARIO_ABI,\r\n//         functionName: 'numberOfCharities',\r\n//     });\r\n\r\n//     const { data: userData, refetch: refetchUserData } = useReadContract({\r\n//         address: CONTRACT_ADDRESS,\r\n//         abi: CHARIO_ABI,\r\n//         functionName: 'getUserByWallet',\r\n//         args: [address],\r\n//         query: {\r\n//             enabled: !!address,\r\n//         },\r\n//     });\r\n\r\n//     // Helper function to execute contract writes\r\n//     const executeWrite = useCallback(async (functionName, args, value, successMessage) => {\r\n//         if (!isConnected) {\r\n//             toast.error('Please connect your wallet');\r\n//             return;\r\n//         }\r\n\r\n//         setIsLoading(true);\r\n\r\n//         try {\r\n//             const hash = await writeContract({\r\n//                 address: CONTRACT_ADDRESS,\r\n//                 abi: CHARIO_ABI,\r\n//                 functionName: functionName,\r\n//                 args: args,\r\n//                 value: value,\r\n//             });\r\n\r\n//             setPendingTxHash(hash);\r\n//             toast.success(successMessage || 'Transaction submitted');\r\n//         } catch (error) {\r\n//             console.error(`Error executing ${functionName}:`, error);\r\n//             toast.error(error.message || 'Transaction failed');\r\n//             setIsLoading(false);\r\n//         }\r\n//     }, [writeContract, isConnected]);\r\n\r\n//     // Create Charity\r\n//     const createCharity = useCallback(async (params) => {\r\n//         const { owner, title, description, target, deadline, image } = params;\r\n\r\n//         try {\r\n//             const targetInWei = parseEther(target.toString());\r\n//             await executeWrite(\r\n//                 'createCharity',\r\n//                 [owner, title, description, targetInWei, deadline, image],\r\n//                 undefined,\r\n//                 'Creating charity...'\r\n//             );\r\n//         } catch (error) {\r\n//             console.error('Error creating charity:', error);\r\n//             toast.error('Failed to create charity');\r\n//         }\r\n//     }, [executeWrite]);\r\n\r\n//     // Donate to Charity\r\n//     const donateToCharity = useCallback(async (charityId, amount) => {\r\n//         try {\r\n//             const amountInWei = parseEther(amount.toString());\r\n//             await executeWrite(\r\n//                 'donateToCharity',\r\n//                 [charityId],\r\n//                 amountInWei,\r\n//                 'Processing donation...'\r\n//             );\r\n//         } catch (error) {\r\n//             console.error('Error donating:', error);\r\n//             toast.error('Failed to donate');\r\n//         }\r\n//     }, [executeWrite]);\r\n\r\n//     // Withdraw Funds\r\n//     const withdrawFunds = useCallback(async (charityId) => {\r\n//         await executeWrite(\r\n//             'withdrawFunds',\r\n//             [charityId],\r\n//             undefined,\r\n//             'Withdrawing funds...'\r\n//         );\r\n//     }, [executeWrite]);\r\n\r\n//     // Refund Donation\r\n//     const refundDonation = useCallback(async (charityId) => {\r\n//         await executeWrite(\r\n//             'refundDonation',\r\n//             [charityId],\r\n//             undefined,\r\n//             'Processing refund...'\r\n//         );\r\n//     }, [executeWrite]);\r\n\r\n//     // Cancel Charity\r\n//     const cancelCharity = useCallback(async (charityId) => {\r\n//         await executeWrite(\r\n//             'cancelCharity',\r\n//             [charityId],\r\n//             undefined,\r\n//             'Cancelling charity...'\r\n//         );\r\n//     }, [executeWrite]);\r\n\r\n//     // Update Charity Status\r\n//     const updateCharityStatus = useCallback(async (charityId) => {\r\n//         await executeWrite(\r\n//             'updateCharityStatus',\r\n//             [charityId],\r\n//             undefined,\r\n//             'Updating status...'\r\n//         );\r\n//     }, [executeWrite]);\r\n\r\n//     const loading = isLoading || isWritePending || isConfirming;\r\n\r\n//     return {\r\n//         // Data\r\n//         numberOfCharities,\r\n//         userData,\r\n\r\n//         // Actions\r\n//         createCharity,\r\n//         donateToCharity,\r\n//         withdrawFunds,\r\n//         refundDonation,\r\n//         cancelCharity,\r\n//         updateCharityStatus,\r\n\r\n//         // Refetch functions\r\n//         refetchCharityCount,\r\n//         refetchUserData,\r\n\r\n//         // State\r\n//         loading,\r\n//         isConnected,\r\n//         address,\r\n//         txHash,\r\n\r\n//         // Utilities\r\n//         formatEther,\r\n//         parseEther,\r\n//     };\r\n// };\r\n\r\n// // Hook for fetching multiple charities\r\n// export const useCharities = (limit) => {\r\n//     const [charities, setCharities] = useState([]);\r\n//     const [isLoading, setIsLoading] = useState(false);\r\n\r\n//     const { data: numberOfCharities } = useReadContract({\r\n//         address: CONTRACT_ADDRESS,\r\n//         abi: CHARIO_ABI,\r\n//         functionName: 'numberOfCharities',\r\n//     });\r\n\r\n//     const totalCharities = numberOfCharities ? Number(numberOfCharities) : 0;\r\n//     const charityLimit = limit ? Math.min(limit, totalCharities) : totalCharities;\r\n\r\n//     // Fetch all charities\r\n//     useEffect(() => {\r\n//         if (totalCharities > 0) {\r\n//             setIsLoading(true);\r\n//             const fetchCharities = async () => {\r\n//                 const charityPromises = [];\r\n\r\n//                 for (let i = 0; i < charityLimit; i++) {\r\n//                     charityPromises.push(\r\n//                         fetch(`/api/v1/charity/${i}`).then(res => res.json()) // You'll need to implement this API endpoint\r\n//                     );\r\n//                 }\r\n\r\n//                 try {\r\n//                     const results = await Promise.all(charityPromises);\r\n//                     setCharities(results.map((charity, index) => ({ ...charity, id: index })));\r\n//                 } catch (error) {\r\n//                     console.error('Error fetching charities:', error);\r\n//                 } finally {\r\n//                     setIsLoading(false);\r\n//                 }\r\n//             };\r\n\r\n//             fetchCharities();\r\n//         }\r\n//     }, [totalCharities, charityLimit]);\r\n\r\n//     return {\r\n//         charities,\r\n//         isLoading,\r\n//         totalCharities,\r\n//     };\r\n// };\r\n\r\n// // Hook for a single charity\r\n// export const useCharity = (charityId) => {\r\n//     const { address } = useAccount();\r\n\r\n//     const { data: charity, isLoading: isLoadingCharity, refetch: refetchCharity } = useReadContract({\r\n//         address: CONTRACT_ADDRESS,\r\n//         abi: CHARIO_ABI,\r\n//         functionName: 'getCharity',\r\n//         args: [charityId],\r\n//     });\r\n\r\n//     const { data: donorContribution, isLoading: isLoadingContribution, refetch: refetchContribution } = useReadContract({\r\n//         address: CONTRACT_ADDRESS,\r\n//         abi: CHARIO_ABI,\r\n//         functionName: 'getDonorContribution',\r\n//         args: [charityId, address],\r\n//         query: {\r\n//             enabled: !!address,\r\n//         },\r\n//     });\r\n\r\n//     const { data: escrowBalance, isLoading: isLoadingEscrow, refetch: refetchEscrow } = useReadContract({\r\n//         address: CONTRACT_ADDRESS,\r\n//         abi: CHARIO_ABI,\r\n//         functionName: 'getEscrowBalance',\r\n//         args: [charityId],\r\n//     });\r\n\r\n//     const isLoading = isLoadingCharity || isLoadingContribution || isLoadingEscrow;\r\n//     const isOwner = (charity?.owner === address);\r\n//     const canRefund = donorContribution && donorContribution > 0n;\r\n\r\n//     // Refetch all charity data\r\n//     const refetchAll = useCallback(() => {\r\n//         refetchCharity();\r\n//         refetchContribution();\r\n//         refetchEscrow();\r\n//     }, [refetchCharity, refetchContribution, refetchEscrow]);\r\n\r\n//     return {\r\n//         charity,\r\n//         donorContribution,\r\n//         escrowBalance,\r\n//         isLoading,\r\n//         isOwner,\r\n//         canRefund,\r\n//         refetchAll,\r\n//     };\r\n// };\r\n\r\n// // Utility hook for charity status\r\n// export const useCharityStatus = (status) => {\r\n//     const statusMap = {\r\n//         0: 'Active',\r\n//         1: 'Inactive',\r\n//         2: 'Completed',\r\n//         3: 'Cancelled'\r\n//     };\r\n\r\n//     const statusColors = {\r\n//         0: 'green',\r\n//         1: 'yellow',\r\n//         2: 'blue',\r\n//         3: 'red'\r\n//     };\r\n\r\n//     return {\r\n//         statusText: statusMap[status] || 'Unknown',\r\n//         statusColor: statusColors[status] || 'gray',\r\n//         isActive: status === 0,\r\n//         isCompleted: status === 2,\r\n//         isCancelled: status === 3,\r\n//         isInactive: status === 1,\r\n//     };\r\n// };\r\n\r\n// // Utility hook for formatting dates\r\n// export const useCharityDates = (deadline) => {\r\n//     const deadlineDate = new Date(deadline * 1000);\r\n//     const now = new Date();\r\n//     const timeLeft = deadlineDate - now;\r\n\r\n//     const isExpired = timeLeft <= 0;\r\n//     const daysLeft = Math.ceil(timeLeft / (1000 * 60 * 60 * 24));\r\n\r\n//     return {\r\n//         deadlineDate,\r\n//         isExpired,\r\n//         daysLeft: isExpired ? 0 : daysLeft,\r\n//         formattedDeadline: deadlineDate.toLocaleDateString(),\r\n//         timeLeftText: isExpired ? 'Expired' : `${daysLeft} days left`,\r\n//     };\r\n// };\r\n\r\n\r\n\"use client\";\r\n\r\nimport { useState, useCallback, useEffect } from 'react';\r\nimport {\r\n    useAccount,\r\n    useReadContract,\r\n    useWriteContract,\r\n    useWaitForTransactionReceipt,\r\n} from \"wagmi\";\r\nimport abi from \"@/abi/chario.json\"; // Make sure this ABI is from your LATEST compiled contract\r\nimport { parseEther, formatEther } from 'viem';\r\nimport { toast } from \"sonner\";\r\n\r\n// --- Configuration ---\r\nconst CHARIO_ABI = abi;\r\nconst CONTRACT_ADDRESS = process.env.NEXT_PUBLIC_CONTRACT_ADDRESS;\r\n\r\n// --- Main Hook for Contract Interactions ---\r\nexport const useChario = () => {\r\n    const { address, isConnected } = useAccount();\r\n    const [isLoading, setIsLoading] = useState(false);\r\n\r\n    // Wagmi hooks for writing transactions\r\n    const { data: txHash, writeContractAsync, error: writeError, isPending: isWritePending } = useWriteContract();\r\n\r\n    // Wagmi hook to wait for transaction confirmation\r\n    const { isLoading: isConfirming, isSuccess: isConfirmed } = useWaitForTransactionReceipt({ hash: txHash });\r\n\r\n    // Effect for handling transaction success\r\n    useEffect(() => {\r\n        if (isConfirmed) {\r\n            toast.success('Transaction confirmed!');\r\n            setIsLoading(false);\r\n        }\r\n    }, [isConfirmed]);\r\n\r\n    // Effect for handling transaction errors\r\n    useEffect(() => {\r\n        if (writeError) {\r\n            toast.error(writeError.shortMessage || 'Transaction failed.');\r\n            console.error('Wagmi Write Error:', writeError);\r\n            setIsLoading(false);\r\n        }\r\n    }, [writeError]);\r\n\r\n    // Read contract data\r\n    const { data: numberOfCharities, refetch: refetchCharityCount } = useReadContract({\r\n        address: CONTRACT_ADDRESS,\r\n        abi: CHARIO_ABI,\r\n        functionName: 'numberOfCharities',\r\n    });\r\n\r\n    // Helper function to execute contract writes\r\n    const executeWrite = useCallback(async (functionName, args, value, loadingMessage) => {\r\n        if (!isConnected) {\r\n            return {\r\n                error: 'Please connect your wallet first.',\r\n                walletError: true\r\n            };\r\n        }\r\n        setIsLoading(true);\r\n        toast.info(loadingMessage || 'Submitting transaction...');\r\n\r\n        try {\r\n            await writeContractAsync({\r\n                address: CONTRACT_ADDRESS,\r\n                abi: CHARIO_ABI,\r\n                functionName,\r\n                args,\r\n                value,\r\n            });\r\n        } catch (error) {\r\n            // Error is handled by the useEffect hook for writeError\r\n            console.error(`Error submitting ${functionName}:`, error);\r\n            return {\r\n                error: error.message\r\n            };\r\n        }\r\n    }, [isConnected, writeContractAsync]);\r\n\r\n    /**\r\n     * Creates a new charity campaign.\r\n     */\r\n    const createCharity = useCallback(async (params) => {\r\n        const { owner, title, description, target, deadline, image, userId } = params;\r\n        // const targetInWei = parseEther(target.toString());\r\n        const result = await executeWrite(\r\n            'createCharity',\r\n            // [owner, title, description, targetInWei || 0, deadline || 0, image],\r\n            [owner, title, description, target, 0, image, userId],\r\n            undefined,\r\n            'Creating your charity...'\r\n        );\r\n\r\n        return result;\r\n    }, [executeWrite]);\r\n\r\n    /**\r\n     * Donates a specified amount to a charity.\r\n     */\r\n    const donateToCharity = useCallback(async (charityId, amount, userId) => {\r\n        const amountInWei = parseEther(amount.toString());\r\n        const result = await executeWrite(\r\n            'donateToCharity',\r\n            [charityId, userId],\r\n            amountInWei,\r\n            'Processing your donation...'\r\n        );\r\n        return result\r\n    }, [executeWrite]);\r\n\r\n    /**\r\n     * Sets the status of a charity (callable only by the charity owner).\r\n     * @param {number} charityId The ID of the charity.\r\n     * @param {number} newStatus The new status (0 for ACTIVE, 1 for INACTIVE).\r\n     */\r\n    const setCharityStatus = useCallback(async (charityId, newStatus) => {\r\n        await executeWrite(\r\n            'setCharityStatus',\r\n            [charityId, newStatus],\r\n            undefined,\r\n            'Updating charity status...'\r\n        );\r\n\r\n        return result\r\n    }, [executeWrite]);\r\n\r\n    return {\r\n        // Data\r\n        numberOfCharities,\r\n\r\n        // Actions\r\n        createCharity,\r\n        donateToCharity,\r\n        setCharityStatus, // <-- New function\r\n\r\n        // Refetch functions\r\n        refetchCharityCount,\r\n\r\n        // State\r\n        loading: isLoading || isWritePending || isConfirming,\r\n        isConnected,\r\n        address,\r\n        txHash,\r\n\r\n        // Utilities\r\n        formatEther,\r\n    };\r\n};\r\n\r\n// Hook for fetching multiple charities\r\nexport const useCharities = (limit) => {\r\n    const [charities, setCharities] = useState([]);\r\n    const [isLoading, setIsLoading] = useState(false);\r\n\r\n    const { data: numberOfCharities } = useReadContract({\r\n        address: CONTRACT_ADDRESS,\r\n        abi: CHARIO_ABI,\r\n        functionName: 'numberOfCharities',\r\n    });\r\n\r\n    const totalCharities = numberOfCharities ? Number(numberOfCharities) : 0;\r\n    const charityLimit = limit ? Math.min(limit, totalCharities) : totalCharities;\r\n\r\n    // Fetch all charities\r\n    useEffect(() => {\r\n        if (totalCharities > 0) {\r\n            setIsLoading(true);\r\n            const fetchCharities = async () => {\r\n                const charityPromises = [];\r\n\r\n                for (let i = 0; i < charityLimit; i++) {\r\n                    charityPromises.push(\r\n                        fetch(`/api/v1/charity/${i}`).then(res => res.json()) // You'll need to implement this API endpoint\r\n                    );\r\n                }\r\n\r\n                try {\r\n                    const results = await Promise.all(charityPromises);\r\n                    setCharities(results.map((charity, index) => ({ ...charity, id: index })));\r\n                } catch (error) {\r\n                    console.error('Error fetching charities:', error);\r\n                } finally {\r\n                    setIsLoading(false);\r\n                }\r\n            };\r\n\r\n            fetchCharities();\r\n        }\r\n    }, [totalCharities, charityLimit]);\r\n\r\n    return {\r\n        charities,\r\n        isLoading,\r\n        totalCharities,\r\n    };\r\n};\r\n\r\n\r\n// --- Hook for a Single Charity's Data ---\r\nexport const useCharity = (charityId) => {\r\n    const { address } = useAccount();\r\n\r\n    const { data: charity, isLoading: isLoadingCharity, refetch: refetchCharity } = useReadContract({\r\n        address: CONTRACT_ADDRESS,\r\n        abi: CHARIO_ABI,\r\n        functionName: 'getCharity',\r\n        args: [charityId],\r\n    });\r\n\r\n    const { data: donorContribution, isLoading: isLoadingContribution, refetch: refetchContribution } = useReadContract({\r\n        address: CONTRACT_ADDRESS,\r\n        abi: CHARIO_ABI,\r\n        functionName: 'getDonorContribution',\r\n        args: [charityId, address],\r\n        query: { enabled: !!address },\r\n    });\r\n\r\n    // Refetch all charity data\r\n    const refetchAll = useCallback(() => {\r\n        refetchCharity();\r\n        refetchContribution();\r\n    }, [refetchCharity, refetchContribution]);\r\n\r\n    return {\r\n        charity,\r\n        donorContribution,\r\n        isLoading: isLoadingCharity || isLoadingContribution,\r\n        isOwner: charity?.owner === address,\r\n        refetchAll,\r\n    };\r\n};\r\n\r\n// --- Utility Hook for Displaying Charity Status ---\r\nexport const useCharityStatus = (status) => {\r\n    const statusMap = {\r\n        0: 'Active',\r\n        1: 'Inactive',\r\n    };\r\n\r\n    const statusColors = {\r\n        0: 'green',\r\n        1: 'gray',\r\n    };\r\n\r\n    return {\r\n        statusText: statusMap[status] ?? 'Unknown',\r\n        statusColor: statusColors[status] ?? 'gray',\r\n        isActive: status === 0,\r\n        isInactive: status === 1,\r\n    };\r\n};"], "names": [], "mappings": "AAAA,gBAAgB;AAEhB,4DAA4D;AAC5D,WAAW;AACX,kBAAkB;AAClB,uBAAuB;AACvB,wBAAwB;AACxB,oCAAoC;AACpC,kBAAkB;AAClB,wEAAwE;AACxE,kDAAkD;AAClD,kCAAkC;AAElC,yBAAyB;AACzB,oEAAoE;AAEpE,mCAAmC;AACnC,qDAAqD;AACrD,yDAAyD;AACzD,gEAAgE;AAEhE,6BAA6B;AAC7B,gHAAgH;AAEhH,sCAAsC;AACtC,iGAAiG;AACjG,wBAAwB;AACxB,UAAU;AAEV,uCAAuC;AACvC,wBAAwB;AACxB,wBAAwB;AACxB,wDAAwD;AACxD,YAAY;AACZ,8CAA8C;AAC9C,uDAAuD;AACvD,mCAAmC;AACnC,sCAAsC;AACtC,YAAY;AACZ,kFAAkF;AAElF,wBAAwB;AACxB,4BAA4B;AAC5B,+DAA+D;AAC/D,wEAAwE;AACxE,mCAAmC;AACnC,sCAAsC;AACtC,YAAY;AACZ,wBAAwB;AAExB,4BAA4B;AAC5B,0FAA0F;AAC1F,qCAAqC;AACrC,2BAA2B;AAC3B,6CAA6C;AAC7C,UAAU;AAEV,6EAA6E;AAC7E,qCAAqC;AACrC,2BAA2B;AAC3B,2CAA2C;AAC3C,2BAA2B;AAC3B,mBAAmB;AACnB,kCAAkC;AAClC,aAAa;AACb,UAAU;AAEV,oDAAoD;AACpD,8FAA8F;AAC9F,8BAA8B;AAC9B,yDAAyD;AACzD,sBAAsB;AACtB,YAAY;AAEZ,8BAA8B;AAE9B,gBAAgB;AAChB,iDAAiD;AACjD,6CAA6C;AAC7C,mCAAmC;AACnC,8CAA8C;AAC9C,8BAA8B;AAC9B,gCAAgC;AAChC,kBAAkB;AAElB,sCAAsC;AACtC,wEAAwE;AACxE,4BAA4B;AAC5B,wEAAwE;AACxE,kEAAkE;AAClE,mCAAmC;AACnC,YAAY;AACZ,wCAAwC;AAExC,wBAAwB;AACxB,4DAA4D;AAC5D,iFAAiF;AAEjF,gBAAgB;AAChB,iEAAiE;AACjE,kCAAkC;AAClC,mCAAmC;AACnC,6EAA6E;AAC7E,6BAA6B;AAC7B,wCAAwC;AACxC,iBAAiB;AACjB,4BAA4B;AAC5B,+DAA+D;AAC/D,uDAAuD;AACvD,YAAY;AACZ,0BAA0B;AAE1B,2BAA2B;AAC3B,yEAAyE;AACzE,gBAAgB;AAChB,iEAAiE;AACjE,kCAAkC;AAClC,qCAAqC;AACrC,+BAA+B;AAC/B,+BAA+B;AAC/B,2CAA2C;AAC3C,iBAAiB;AACjB,4BAA4B;AAC5B,uDAAuD;AACvD,+CAA+C;AAC/C,YAAY;AACZ,0BAA0B;AAE1B,wBAAwB;AACxB,+DAA+D;AAC/D,8BAA8B;AAC9B,+BAA+B;AAC/B,2BAA2B;AAC3B,yBAAyB;AACzB,qCAAqC;AACrC,aAAa;AACb,0BAA0B;AAE1B,yBAAyB;AACzB,gEAAgE;AAChE,8BAA8B;AAC9B,gCAAgC;AAChC,2BAA2B;AAC3B,yBAAyB;AACzB,qCAAqC;AACrC,aAAa;AACb,0BAA0B;AAE1B,wBAAwB;AACxB,+DAA+D;AAC/D,8BAA8B;AAC9B,+BAA+B;AAC/B,2BAA2B;AAC3B,yBAAyB;AACzB,sCAAsC;AACtC,aAAa;AACb,0BAA0B;AAE1B,+BAA+B;AAC/B,qEAAqE;AACrE,8BAA8B;AAC9B,qCAAqC;AACrC,2BAA2B;AAC3B,yBAAyB;AACzB,mCAAmC;AACnC,aAAa;AACb,0BAA0B;AAE1B,mEAAmE;AAEnE,eAAe;AACf,kBAAkB;AAClB,6BAA6B;AAC7B,oBAAoB;AAEpB,qBAAqB;AACrB,yBAAyB;AACzB,2BAA2B;AAC3B,yBAAyB;AACzB,0BAA0B;AAC1B,yBAAyB;AACzB,+BAA+B;AAE/B,+BAA+B;AAC/B,+BAA+B;AAC/B,2BAA2B;AAE3B,mBAAmB;AACnB,mBAAmB;AACnB,uBAAuB;AACvB,mBAAmB;AACnB,kBAAkB;AAElB,uBAAuB;AACvB,uBAAuB;AACvB,sBAAsB;AACtB,SAAS;AACT,KAAK;AAEL,0CAA0C;AAC1C,2CAA2C;AAC3C,sDAAsD;AACtD,yDAAyD;AAEzD,4DAA4D;AAC5D,qCAAqC;AACrC,2BAA2B;AAC3B,6CAA6C;AAC7C,UAAU;AAEV,gFAAgF;AAChF,qFAAqF;AAErF,6BAA6B;AAC7B,wBAAwB;AACxB,oCAAoC;AACpC,kCAAkC;AAClC,mDAAmD;AACnD,8CAA8C;AAE9C,2DAA2D;AAC3D,4CAA4C;AAC5C,8HAA8H;AAC9H,yBAAyB;AACzB,oBAAoB;AAEpB,wBAAwB;AACxB,0EAA0E;AAC1E,kGAAkG;AAClG,oCAAoC;AACpC,yEAAyE;AACzE,8BAA8B;AAC9B,2CAA2C;AAC3C,oBAAoB;AACpB,iBAAiB;AAEjB,gCAAgC;AAChC,YAAY;AACZ,0CAA0C;AAE1C,eAAe;AACf,qBAAqB;AACrB,qBAAqB;AACrB,0BAA0B;AAC1B,SAAS;AACT,KAAK;AAEL,+BAA+B;AAC/B,6CAA6C;AAC7C,wCAAwC;AAExC,wGAAwG;AACxG,qCAAqC;AACrC,2BAA2B;AAC3B,sCAAsC;AACtC,6BAA6B;AAC7B,UAAU;AAEV,4HAA4H;AAC5H,qCAAqC;AACrC,2BAA2B;AAC3B,gDAAgD;AAChD,sCAAsC;AACtC,mBAAmB;AACnB,kCAAkC;AAClC,aAAa;AACb,UAAU;AAEV,4GAA4G;AAC5G,qCAAqC;AACrC,2BAA2B;AAC3B,4CAA4C;AAC5C,6BAA6B;AAC7B,UAAU;AAEV,sFAAsF;AACtF,oDAAoD;AACpD,qEAAqE;AAErE,kCAAkC;AAClC,6CAA6C;AAC7C,4BAA4B;AAC5B,iCAAiC;AACjC,2BAA2B;AAC3B,gEAAgE;AAEhE,eAAe;AACf,mBAAmB;AACnB,6BAA6B;AAC7B,yBAAyB;AACzB,qBAAqB;AACrB,mBAAmB;AACnB,qBAAqB;AACrB,sBAAsB;AACtB,SAAS;AACT,KAAK;AAEL,qCAAqC;AACrC,gDAAgD;AAChD,0BAA0B;AAC1B,uBAAuB;AACvB,yBAAyB;AACzB,0BAA0B;AAC1B,yBAAyB;AACzB,SAAS;AAET,6BAA6B;AAC7B,sBAAsB;AACtB,uBAAuB;AACvB,qBAAqB;AACrB,mBAAmB;AACnB,SAAS;AAET,eAAe;AACf,sDAAsD;AACtD,uDAAuD;AACvD,kCAAkC;AAClC,qCAAqC;AACrC,qCAAqC;AACrC,oCAAoC;AACpC,SAAS;AACT,KAAK;AAEL,uCAAuC;AACvC,iDAAiD;AACjD,sDAAsD;AACtD,8BAA8B;AAC9B,2CAA2C;AAE3C,uCAAuC;AACvC,oEAAoE;AAEpE,eAAe;AACf,wBAAwB;AACxB,qBAAqB;AACrB,8CAA8C;AAC9C,gEAAgE;AAChE,yEAAyE;AACzE,SAAS;AACT,KAAK;;;;;;;AAKL;AACA;AAAA;AAAA;AAAA;AAMA,oKAAqC,2DAA2D;AAChG;AAAA;AACA;AAXA;;;;;;AAaA,wBAAwB;AACxB,MAAM,aAAa,4FAAA,CAAA,UAAG;AACtB,MAAM;AAGC,MAAM,YAAY;IACrB,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,2JAAA,CAAA,aAAU,AAAD;IAC1C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,uCAAuC;IACvC,MAAM,EAAE,MAAM,MAAM,EAAE,kBAAkB,EAAE,OAAO,UAAU,EAAE,WAAW,cAAc,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,mBAAgB,AAAD;IAE1G,kDAAkD;IAClD,MAAM,EAAE,WAAW,YAAY,EAAE,WAAW,WAAW,EAAE,GAAG,CAAA,GAAA,6KAAA,CAAA,+BAA4B,AAAD,EAAE;QAAE,MAAM;IAAO;IAExG,0CAA0C;IAC1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,aAAa;YACb,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,aAAa;QACjB;IACJ,GAAG;QAAC;KAAY;IAEhB,yCAAyC;IACzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,YAAY;YACZ,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,WAAW,YAAY,IAAI;YACvC,QAAQ,KAAK,CAAC,sBAAsB;YACpC,aAAa;QACjB;IACJ,GAAG;QAAC;KAAW;IAEf,qBAAqB;IACrB,MAAM,EAAE,MAAM,iBAAiB,EAAE,SAAS,mBAAmB,EAAE,GAAG,CAAA,GAAA,gKAAA,CAAA,kBAAe,AAAD,EAAE;QAC9E,SAAS;QACT,KAAK;QACL,cAAc;IAClB;IAEA,6CAA6C;IAC7C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,cAAc,MAAM,OAAO;QAC/D,IAAI,CAAC,aAAa;YACd,OAAO;gBACH,OAAO;gBACP,aAAa;YACjB;QACJ;QACA,aAAa;QACb,wIAAA,CAAA,QAAK,CAAC,IAAI,CAAC,kBAAkB;QAE7B,IAAI;YACA,MAAM,mBAAmB;gBACrB,SAAS;gBACT,KAAK;gBACL;gBACA;gBACA;YACJ;QACJ,EAAE,OAAO,OAAO;YACZ,wDAAwD;YACxD,QAAQ,KAAK,CAAC,CAAC,iBAAiB,EAAE,aAAa,CAAC,CAAC,EAAE;YACnD,OAAO;gBACH,OAAO,MAAM,OAAO;YACxB;QACJ;IACJ,GAAG;QAAC;QAAa;KAAmB;IAEpC;;KAEC,GACD,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACrC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG;QACvE,qDAAqD;QACrD,MAAM,UAAS,MAAM,aACjB,iBACA,uEAAuE;QACvE;YAAC;YAAO;YAAO;YAAa;YAAQ;YAAG;YAAO;SAAO,EACrD,WACA;QAGJ,OAAO;IACX,GAAG;QAAC;KAAa;IAEjB;;KAEC,GACD,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,WAAW,QAAQ;QAC1D,MAAM,cAAc,CAAA,GAAA,2JAAA,CAAA,aAAU,AAAD,EAAE,OAAO,QAAQ;QAC9C,MAAM,UAAS,MAAM,aACjB,mBACA;YAAC;YAAW;SAAO,EACnB,aACA;QAEJ,OAAO;IACX,GAAG;QAAC;KAAa;IAEjB;;;;KAIC,GACD,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,WAAW;QACnD,MAAM,aACF,oBACA;YAAC;YAAW;SAAU,EACtB,WACA;QAGJ,OAAO;IACX,GAAG;QAAC;KAAa;IAEjB,OAAO;QACH,OAAO;QACP;QAEA,UAAU;QACV;QACA;QACA;QAEA,oBAAoB;QACpB;QAEA,QAAQ;QACR,SAAS,aAAa,kBAAkB;QACxC;QACA;QACA;QAEA,YAAY;QACZ,aAAA,4JAAA,CAAA,cAAW;IACf;AACJ;AAGO,MAAM,eAAe,CAAC;IACzB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,EAAE,MAAM,iBAAiB,EAAE,GAAG,CAAA,GAAA,gKAAA,CAAA,kBAAe,AAAD,EAAE;QAChD,SAAS;QACT,KAAK;QACL,cAAc;IAClB;IAEA,MAAM,iBAAiB,oBAAoB,OAAO,qBAAqB;IACvE,MAAM,eAAe,QAAQ,KAAK,GAAG,CAAC,OAAO,kBAAkB;IAE/D,sBAAsB;IACtB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,iBAAiB,GAAG;YACpB,aAAa;YACb,MAAM,iBAAiB;gBACnB,MAAM,kBAAkB,EAAE;gBAE1B,IAAK,IAAI,IAAI,GAAG,IAAI,cAAc,IAAK;oBACnC,gBAAgB,IAAI,CAChB,MAAM,CAAC,gBAAgB,EAAE,GAAG,EAAE,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI,IAAI,6CAA6C;;gBAE3G;gBAEA,IAAI;oBACA,MAAM,UAAU,MAAM,QAAQ,GAAG,CAAC;oBAClC,aAAa,QAAQ,GAAG,CAAC,CAAC,SAAS,QAAU,CAAC;4BAAE,GAAG,OAAO;4BAAE,IAAI;wBAAM,CAAC;gBAC3E,EAAE,OAAO,OAAO;oBACZ,QAAQ,KAAK,CAAC,6BAA6B;gBAC/C,SAAU;oBACN,aAAa;gBACjB;YACJ;YAEA;QACJ;IACJ,GAAG;QAAC;QAAgB;KAAa;IAEjC,OAAO;QACH;QACA;QACA;IACJ;AACJ;AAIO,MAAM,aAAa,CAAC;IACvB,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,2JAAA,CAAA,aAAU,AAAD;IAE7B,MAAM,EAAE,MAAM,OAAO,EAAE,WAAW,gBAAgB,EAAE,SAAS,cAAc,EAAE,GAAG,CAAA,GAAA,gKAAA,CAAA,kBAAe,AAAD,EAAE;QAC5F,SAAS;QACT,KAAK;QACL,cAAc;QACd,MAAM;YAAC;SAAU;IACrB;IAEA,MAAM,EAAE,MAAM,iBAAiB,EAAE,WAAW,qBAAqB,EAAE,SAAS,mBAAmB,EAAE,GAAG,CAAA,GAAA,gKAAA,CAAA,kBAAe,AAAD,EAAE;QAChH,SAAS;QACT,KAAK;QACL,cAAc;QACd,MAAM;YAAC;YAAW;SAAQ;QAC1B,OAAO;YAAE,SAAS,CAAC,CAAC;QAAQ;IAChC;IAEA,2BAA2B;IAC3B,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC3B;QACA;IACJ,GAAG;QAAC;QAAgB;KAAoB;IAExC,OAAO;QACH;QACA;QACA,WAAW,oBAAoB;QAC/B,SAAS,SAAS,UAAU;QAC5B;IACJ;AACJ;AAGO,MAAM,mBAAmB,CAAC;IAC7B,MAAM,YAAY;QACd,GAAG;QACH,GAAG;IACP;IAEA,MAAM,eAAe;QACjB,GAAG;QACH,GAAG;IACP;IAEA,OAAO;QACH,YAAY,SAAS,CAAC,OAAO,IAAI;QACjC,aAAa,YAAY,CAAC,OAAO,IAAI;QACrC,UAAU,WAAW;QACrB,YAAY,WAAW;IAC3B;AACJ", "debugId": null}}, {"offset": {"line": 927, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/js/web3/packages/chario/src/hooks/use-sse.js"], "sourcesContent": ["export function createReconnectingEventSource(url, options = {}) {\r\n    let eventSource = null\r\n    let reconnectTimeout = null\r\n    let reconnectDelay = 1000\r\n    const maxReconnectDelay = 30000\r\n    const { onOpen, onMessage, onError, onEvent, onStatusChange } = options\r\n\r\n    function setStatus(status) {\r\n        if (onStatusChange) onStatusChange(status)\r\n    }\r\n\r\n    function connect() {\r\n        setStatus('connecting')\r\n        eventSource = new EventSource(url)\r\n\r\n        eventSource.onopen = (event) => {\r\n            reconnectDelay = 1000\r\n            setStatus(1)\r\n            if (onOpen) onOpen(event)\r\n            console.log('SSE connected')\r\n        }\r\n\r\n        eventSource.onmessage = (event) => {\r\n            if (onMessage) onMessage(event)\r\n        }\r\n\r\n        eventSource.onerror = (event) => {\r\n            if (onError) onError(event)\r\n            setStatus(0)\r\n            console.error('SSE error, attempting to reconnect...')\r\n            eventSource.close()\r\n\r\n            if (reconnectTimeout) clearTimeout(reconnectTimeout)\r\n\r\n            reconnectTimeout = setTimeout(() => {\r\n                reconnectDelay = Math.min(reconnectDelay * 2, maxReconnectDelay)\r\n                connect()\r\n            }, reconnectDelay)\r\n        }\r\n\r\n        if (onEvent && typeof onEvent === 'object') {\r\n            Object.entries(onEvent).forEach(([eventName, handler]) => {\r\n                eventSource.addEventListener(eventName, handler)\r\n            })\r\n        }\r\n    }\r\n\r\n    connect()\r\n\r\n    return {\r\n        close: () => {\r\n            if (reconnectTimeout) clearTimeout(reconnectTimeout)\r\n            if (eventSource) eventSource.close()\r\n            setStatus('disconnected')\r\n            console.log('SSE connection closed manually')\r\n        },\r\n        getEventSource: () => eventSource,\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AAAO,SAAS,8BAA8B,GAAG,EAAE,UAAU,CAAC,CAAC;IAC3D,IAAI,cAAc;IAClB,IAAI,mBAAmB;IACvB,IAAI,iBAAiB;IACrB,MAAM,oBAAoB;IAC1B,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,cAAc,EAAE,GAAG;IAEhE,SAAS,UAAU,MAAM;QACrB,IAAI,gBAAgB,eAAe;IACvC;IAEA,SAAS;QACL,UAAU;QACV,cAAc,IAAI,YAAY;QAE9B,YAAY,MAAM,GAAG,CAAC;YAClB,iBAAiB;YACjB,UAAU;YACV,IAAI,QAAQ,OAAO;YACnB,QAAQ,GAAG,CAAC;QAChB;QAEA,YAAY,SAAS,GAAG,CAAC;YACrB,IAAI,WAAW,UAAU;QAC7B;QAEA,YAAY,OAAO,GAAG,CAAC;YACnB,IAAI,SAAS,QAAQ;YACrB,UAAU;YACV,QAAQ,KAAK,CAAC;YACd,YAAY,KAAK;YAEjB,IAAI,kBAAkB,aAAa;YAEnC,mBAAmB,WAAW;gBAC1B,iBAAiB,KAAK,GAAG,CAAC,iBAAiB,GAAG;gBAC9C;YACJ,GAAG;QACP;QAEA,IAAI,WAAW,OAAO,YAAY,UAAU;YACxC,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,WAAW,QAAQ;gBACjD,YAAY,gBAAgB,CAAC,WAAW;YAC5C;QACJ;IACJ;IAEA;IAEA,OAAO;QACH,OAAO;YACH,IAAI,kBAAkB,aAAa;YACnC,IAAI,aAAa,YAAY,KAAK;YAClC,UAAU;YACV,QAAQ,GAAG,CAAC;QAChB;QACA,gBAAgB,IAAM;IAC1B;AACJ", "debugId": null}}, {"offset": {"line": 985, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/js/web3/packages/chario/src/lib/hashing.js"], "sourcesContent": ["// Function to convert a Uint8Array to a Base64 URL encoded string\r\nfunction base64URLEncode(buffer) {\r\n    const base64 = btoa(String.fromCharCode.apply(null, buffer));\r\n    return base64.replace(/\\+/g, '-').replace(/\\//g, '_').replace(/=+$/, '');\r\n}\r\n\r\n// Function to convert a Base64 URL encoded string back to a Uint8Array\r\nfunction base64URLDecode(base64URL) {\r\n    let base64 = base64URL.replace(/-/g, '+').replace(/_/g, '/');\r\n    while (base64.length % 4) {\r\n        base64 += '=';\r\n    }\r\n    const binaryString = atob(base64);\r\n    return Uint8Array.from(binaryString, c => c.charCodeAt(0));\r\n}\r\n\r\n/**\r\n * Encodes an integer into a compact Base64 URL safe string.\r\n * This function handles integers up to 2^53 - 1 safely in JavaScript (Number.MAX_SAFE_INTEGER).\r\n * For larger integers, consider using BigInt and a more robust byte conversion.\r\n * @param {number} integer The integer to encode.\r\n * @returns {string} The Base64 URL encoded string.\r\n */\r\nexport function maskId(integer) {\r\n    if (!Number.isInteger(integer) || integer < 0) {\r\n        throw new Error(\"Input must be a non-negative integer.\");\r\n    }\r\n\r\n    const byteLength = Math.ceil(Math.log2(integer + 1) / 8) || 1; // Calculate minimum bytes needed, at least 1 for 0\r\n    const buffer = new Uint8Array(byteLength);\r\n\r\n    for (let i = 0; i < byteLength; i++) {\r\n        buffer[byteLength - 1 - i] = (integer >> (8 * i)) & 0xFF;\r\n    }\r\n\r\n    return base64URLEncode(buffer);\r\n}\r\n\r\n/**\r\n * Decodes a Base64 URL safe string back into an integer.\r\n * @param {string} encodedString The Base64 URL encoded string.\r\n * @returns {number} The decoded integer.\r\n */\r\nexport function unmaskId(encodedString) {\r\n    const buffer = base64URLDecode(encodedString);\r\n    let integer = 0;\r\n\r\n    for (let i = 0; i < buffer.length; i++) {\r\n        integer = (integer << 8) | buffer[i];\r\n    }\r\n\r\n    return integer;\r\n}"], "names": [], "mappings": "AAAA,kEAAkE;;;;;AAClE,SAAS,gBAAgB,MAAM;IAC3B,MAAM,SAAS,KAAK,OAAO,YAAY,CAAC,KAAK,CAAC,MAAM;IACpD,OAAO,OAAO,OAAO,CAAC,OAAO,KAAK,OAAO,CAAC,OAAO,KAAK,OAAO,CAAC,OAAO;AACzE;AAEA,uEAAuE;AACvE,SAAS,gBAAgB,SAAS;IAC9B,IAAI,SAAS,UAAU,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM;IACxD,MAAO,OAAO,MAAM,GAAG,EAAG;QACtB,UAAU;IACd;IACA,MAAM,eAAe,KAAK;IAC1B,OAAO,WAAW,IAAI,CAAC,cAAc,CAAA,IAAK,EAAE,UAAU,CAAC;AAC3D;AASO,SAAS,OAAO,OAAO;IAC1B,IAAI,CAAC,OAAO,SAAS,CAAC,YAAY,UAAU,GAAG;QAC3C,MAAM,IAAI,MAAM;IACpB;IAEA,MAAM,aAAa,KAAK,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,KAAK,MAAM,GAAG,mDAAmD;IAClH,MAAM,SAAS,IAAI,WAAW;IAE9B,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,IAAK;QACjC,MAAM,CAAC,aAAa,IAAI,EAAE,GAAG,AAAC,WAAY,IAAI,IAAM;IACxD;IAEA,OAAO,gBAAgB;AAC3B;AAOO,SAAS,SAAS,aAAa;IAClC,MAAM,SAAS,gBAAgB;IAC/B,IAAI,UAAU;IAEd,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;QACpC,UAAU,AAAC,WAAW,IAAK,MAAM,CAAC,EAAE;IACxC;IAEA,OAAO;AACX", "debugId": null}}, {"offset": {"line": 1028, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/js/web3/packages/chario/src/app/home/<USER>/_components/charities-page.jsx"], "sourcesContent": ["'use client'\r\n\r\nimport CharityCardMd from '@/components/md/charity-card-md';\r\nimport SearchBar from '@/components/md/search-bar';\r\nimport { useCharities, useCharity, useCharityStatus, useChario } from '@/hooks/use-chario';\r\nimport { createReconnectingEventSource } from '@/hooks/use-sse';\r\nimport { authClient, useSession } from '@/lib/auth-client';\r\nimport { maskId } from '@/lib/hashing';\r\nimport { cn } from '@/lib/utils';\r\nimport { useRouter, useSearchParams } from 'next/navigation';\r\nimport React, { useEffect, useState } from 'react'\r\n\r\nfunction CharitiesPage({ charities: initialCharities }) {\r\n    const searchParams = useSearchParams();\r\n    const router = useRouter()\r\n    const [charities, setCharities] = useState(initialCharities);\r\n    const [realtime, setRealtime] = useState(false);\r\n    const currentSearchQuery = searchParams.get('search') || '';\r\n    const [search, setSearch] = useState(currentSearchQuery);\r\n    const sessionData = useSession();\r\n\r\n    // if (!sessionData?.session && !sessionData?.user) {\r\n    //     createAnonymousUser()\r\n    // }\r\n\r\n    // async function createAnonymousUser() {\r\n    //     const data = await authClient.signIn.anonymous()\r\n    // }\r\n\r\n    useEffect(() => {\r\n        const sse = createReconnectingEventSource(`${process.env.NEXT_PUBLIC_SSE_URL}/sse/new-charities`, {\r\n            onOpen: () => console.log('Connected to SSE'),\r\n            onMessage: (e) => console.log('Message:', e.data),\r\n            onError: (e) => console.error('Error:', e),\r\n            onEvent: {\r\n                'new-charity': (e) => {\r\n                    if (currentSearchQuery.length > 0) {\r\n                        router.refresh()\r\n                    }\r\n                    const data = JSON.parse(e.data)\r\n                    console.log('New charity event:', data.charity);\r\n                    setCharities((prevCharities) => [data.charity, ...prevCharities])\r\n                }\r\n            },\r\n            onStatusChange: (status) => setRealtime(status === 1 ? true : false)\r\n        })\r\n\r\n        return () => {\r\n            sse.close()\r\n        }\r\n    }, [])\r\n\r\n    useEffect(() => {\r\n        setCharities(initialCharities);\r\n    }, [initialCharities]);\r\n\r\n    useEffect(() => {\r\n        setSearch(currentSearchQuery);\r\n    }, [currentSearchQuery]);\r\n\r\n    function handleSearch(e) {\r\n        e.preventDefault();\r\n\r\n        router.push(`/home/<USER>\n    }\r\n\r\n    return (\r\n        <div className='w-full h-full p-8 gap-8 flex flex-col items-center relative overflow-x-hidden '>\r\n            <SearchBar\r\n                search={search}\r\n                setSearch={setSearch}\r\n                submitSearch={handleSearch}\r\n            />\r\n            <div className='max-w-[1200px] w-full flex flex-col justify-center mx-auto'>\r\n                <div className='flex gap-2 mb-2'>\r\n                    <h2 className='text-muted-foreground'>{currentSearchQuery?.length > 0 ? `Search results for \"${currentSearchQuery}\"` : 'Active Charities'} ({charities?.length} total)</h2>\r\n                    <div className='relative mt-1'>\r\n                        <div\r\n                            className={cn(\r\n                                'absolute bg-black bg-opacity-50 z-10 h-2 w-2 rounded-full',\r\n                                realtime ? 'bg-emerald-500' : 'bg-yellow-500'\r\n                            )}\r\n                        />\r\n                        <div\r\n                            className={cn(\r\n                                'absolute bg-black bg-opacity-50 z-0 h-6 w-6 rounded-full',\r\n                                'animate-pulse-fade-out',\r\n                                '-top-2 -left-2', // This positioning might need slight adjustment based on your h-4/w-4\r\n                                realtime ? 'bg-emerald-500' : 'bg-yellow-500'\r\n                            )}\r\n                        />\r\n                    </div>\r\n                </div>\r\n                {/* <div>\r\n                    {isConnected ? \"SSE Connected\" : \"Connecting...\"}\r\n                </div> */}\r\n                <div className='flex flex-wrap justify-center gap-6 mt-4'>\r\n                    {charities?.map(charity => (\r\n                        <CharityCard key={charity.id} charityId={charity.id} charity={charity} />\r\n                    ))}\r\n                </div>\r\n            </div>\r\n            {/* <div className='min-h-[400px]' /> */}\r\n        </div>\r\n    );\r\n}\r\n\r\nexport default CharitiesPage\r\n\r\n\r\nfunction CharityCard({ charityId, charity }) {\r\n    // const { charity, donorContribution, isOwner, canRefund, refetchAll } = useCharity(charityId);\r\n    const { donateToCharity, withdrawFunds, refundDonation, formatEther, parseEther } = useChario();\r\n    const { statusText, statusColor, isActive } = useCharityStatus(charity?.status);\r\n\r\n    // const handleDonate = async () => {\r\n    //     await donateToCharity(charityId, \"0.1\");\r\n    //     refetchAll(); // Refresh data after donation\r\n    // };\r\n\r\n    return (\r\n        <CharityCardMd\r\n            className='self-start'\r\n            charity={{\r\n                id: charityId,\r\n                title: charity?.title,\r\n                description: charity?.description,\r\n                target: charity?.target || '0',\r\n                amountCollected: charity?.amountCollected,\r\n                image: charity?.image,\r\n            }}\r\n        />\r\n    );\r\n}\r\n\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAVA;;;;;;;;;;;AAYA,SAAS,cAAc,EAAE,WAAW,gBAAgB,EAAE;IAClD,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,qBAAqB,aAAa,GAAG,CAAC,aAAa;IACzD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,cAAc,CAAA,GAAA,4HAAA,CAAA,aAAU,AAAD;IAE7B,qDAAqD;IACrD,4BAA4B;IAC5B,IAAI;IAEJ,yCAAyC;IACzC,uDAAuD;IACvD,IAAI;IAEJ,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,MAAM,CAAA,GAAA,0HAAA,CAAA,gCAA6B,AAAD,EAAE,6DAAmC,kBAAkB,CAAC,EAAE;YAC9F,QAAQ,IAAM,QAAQ,GAAG,CAAC;YAC1B,WAAW,CAAC,IAAM,QAAQ,GAAG,CAAC,YAAY,EAAE,IAAI;YAChD,SAAS,CAAC,IAAM,QAAQ,KAAK,CAAC,UAAU;YACxC,SAAS;gBACL,eAAe,CAAC;oBACZ,IAAI,mBAAmB,MAAM,GAAG,GAAG;wBAC/B,OAAO,OAAO;oBAClB;oBACA,MAAM,OAAO,KAAK,KAAK,CAAC,EAAE,IAAI;oBAC9B,QAAQ,GAAG,CAAC,sBAAsB,KAAK,OAAO;oBAC9C,aAAa,CAAC,gBAAkB;4BAAC,KAAK,OAAO;+BAAK;yBAAc;gBACpE;YACJ;YACA,gBAAgB,CAAC,SAAW,YAAY,WAAW,IAAI,OAAO;QAClE;QAEA,OAAO;YACH,IAAI,KAAK;QACb;IACJ,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,aAAa;IACjB,GAAG;QAAC;KAAiB;IAErB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,UAAU;IACd,GAAG;QAAC;KAAmB;IAEvB,SAAS,aAAa,CAAC;QACnB,EAAE,cAAc;QAEhB,OAAO,IAAI,CAAC,CAAC,uBAAuB,EAAE,mBAAmB,SAAS;IACtE;IAEA,qBACI,8OAAC;QAAI,WAAU;;0BACX,8OAAC,yIAAA,CAAA,UAAS;gBACN,QAAQ;gBACR,WAAW;gBACX,cAAc;;;;;;0BAElB,8OAAC;gBAAI,WAAU;;kCACX,8OAAC;wBAAI,WAAU;;0CACX,8OAAC;gCAAG,WAAU;;oCAAyB,oBAAoB,SAAS,IAAI,CAAC,oBAAoB,EAAE,mBAAmB,CAAC,CAAC,GAAG;oCAAmB;oCAAG,WAAW;oCAAO;;;;;;;0CAC/J,8OAAC;gCAAI,WAAU;;kDACX,8OAAC;wCACG,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACR,6DACA,WAAW,mBAAmB;;;;;;kDAGtC,8OAAC;wCACG,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACR,4DACA,0BACA,kBACA,WAAW,mBAAmB;;;;;;;;;;;;;;;;;;kCAQ9C,8OAAC;wBAAI,WAAU;kCACV,WAAW,IAAI,CAAA,wBACZ,8OAAC;gCAA6B,WAAW,QAAQ,EAAE;gCAAE,SAAS;+BAA5C,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;AAOpD;uCAEe;AAGf,SAAS,YAAY,EAAE,SAAS,EAAE,OAAO,EAAE;IACvC,gGAAgG;IAChG,MAAM,EAAE,eAAe,EAAE,aAAa,EAAE,cAAc,EAAE,WAAW,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,YAAS,AAAD;IAC5F,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,mBAAgB,AAAD,EAAE,SAAS;IAExE,qCAAqC;IACrC,+CAA+C;IAC/C,mDAAmD;IACnD,KAAK;IAEL,qBACI,8OAAC,iJAAA,CAAA,UAAa;QACV,WAAU;QACV,SAAS;YACL,IAAI;YACJ,OAAO,SAAS;YAChB,aAAa,SAAS;YACtB,QAAQ,SAAS,UAAU;YAC3B,iBAAiB,SAAS;YAC1B,OAAO,SAAS;QACpB;;;;;;AAGZ", "debugId": null}}, {"offset": {"line": 1223, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/js/web3/packages/chario/node_modules/%40radix-ui/react-progress/src/progress.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { Primitive } from '@radix-ui/react-primitive';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Progress\n * -----------------------------------------------------------------------------------------------*/\n\nconst PROGRESS_NAME = 'Progress';\nconst DEFAULT_MAX = 100;\n\ntype ScopedProps<P> = P & { __scopeProgress?: Scope };\nconst [createProgressContext, createProgressScope] = createContextScope(PROGRESS_NAME);\n\ntype ProgressState = 'indeterminate' | 'complete' | 'loading';\ntype ProgressContextValue = { value: number | null; max: number };\nconst [ProgressProvider, useProgressContext] =\n  createProgressContext<ProgressContextValue>(PROGRESS_NAME);\n\ntype ProgressElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface ProgressProps extends PrimitiveDivProps {\n  value?: number | null | undefined;\n  max?: number;\n  getValueLabel?(value: number, max: number): string;\n}\n\nconst Progress = React.forwardRef<ProgressElement, ProgressProps>(\n  (props: ScopedProps<ProgressProps>, forwardedRef) => {\n    const {\n      __scopeProgress,\n      value: valueProp = null,\n      max: maxProp,\n      getValueLabel = defaultGetValueLabel,\n      ...progressProps\n    } = props;\n\n    if ((maxProp || maxProp === 0) && !isValidMaxNumber(maxProp)) {\n      console.error(getInvalidMaxError(`${maxProp}`, 'Progress'));\n    }\n\n    const max = isValidMaxNumber(maxProp) ? maxProp : DEFAULT_MAX;\n\n    if (valueProp !== null && !isValidValueNumber(valueProp, max)) {\n      console.error(getInvalidValueError(`${valueProp}`, 'Progress'));\n    }\n\n    const value = isValidValueNumber(valueProp, max) ? valueProp : null;\n    const valueLabel = isNumber(value) ? getValueLabel(value, max) : undefined;\n\n    return (\n      <ProgressProvider scope={__scopeProgress} value={value} max={max}>\n        <Primitive.div\n          aria-valuemax={max}\n          aria-valuemin={0}\n          aria-valuenow={isNumber(value) ? value : undefined}\n          aria-valuetext={valueLabel}\n          role=\"progressbar\"\n          data-state={getProgressState(value, max)}\n          data-value={value ?? undefined}\n          data-max={max}\n          {...progressProps}\n          ref={forwardedRef}\n        />\n      </ProgressProvider>\n    );\n  }\n);\n\nProgress.displayName = PROGRESS_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ProgressIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst INDICATOR_NAME = 'ProgressIndicator';\n\ntype ProgressIndicatorElement = React.ComponentRef<typeof Primitive.div>;\ninterface ProgressIndicatorProps extends PrimitiveDivProps {}\n\nconst ProgressIndicator = React.forwardRef<ProgressIndicatorElement, ProgressIndicatorProps>(\n  (props: ScopedProps<ProgressIndicatorProps>, forwardedRef) => {\n    const { __scopeProgress, ...indicatorProps } = props;\n    const context = useProgressContext(INDICATOR_NAME, __scopeProgress);\n    return (\n      <Primitive.div\n        data-state={getProgressState(context.value, context.max)}\n        data-value={context.value ?? undefined}\n        data-max={context.max}\n        {...indicatorProps}\n        ref={forwardedRef}\n      />\n    );\n  }\n);\n\nProgressIndicator.displayName = INDICATOR_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\nfunction defaultGetValueLabel(value: number, max: number) {\n  return `${Math.round((value / max) * 100)}%`;\n}\n\nfunction getProgressState(value: number | undefined | null, maxValue: number): ProgressState {\n  return value == null ? 'indeterminate' : value === maxValue ? 'complete' : 'loading';\n}\n\nfunction isNumber(value: any): value is number {\n  return typeof value === 'number';\n}\n\nfunction isValidMaxNumber(max: any): max is number {\n  // prettier-ignore\n  return (\n    isNumber(max) &&\n    !isNaN(max) &&\n    max > 0\n  );\n}\n\nfunction isValidValueNumber(value: any, max: number): value is number {\n  // prettier-ignore\n  return (\n    isNumber(value) &&\n    !isNaN(value) &&\n    value <= max &&\n    value >= 0\n  );\n}\n\n// Split this out for clearer readability of the error message.\nfunction getInvalidMaxError(propValue: string, componentName: string) {\n  return `Invalid prop \\`max\\` of value \\`${propValue}\\` supplied to \\`${componentName}\\`. Only numbers greater than 0 are valid max values. Defaulting to \\`${DEFAULT_MAX}\\`.`;\n}\n\nfunction getInvalidValueError(propValue: string, componentName: string) {\n  return `Invalid prop \\`value\\` of value \\`${propValue}\\` supplied to \\`${componentName}\\`. The \\`value\\` prop must be:\n  - a positive number\n  - less than the value passed to \\`max\\` (or ${DEFAULT_MAX} if no \\`max\\` prop is set)\n  - \\`null\\` or \\`undefined\\` if the progress is indeterminate.\n\nDefaulting to \\`null\\`.`;\n}\n\nconst Root = Progress;\nconst Indicator = ProgressIndicator;\n\nexport {\n  createProgressScope,\n  //\n  Progress,\n  ProgressIndicator,\n  //\n  Root,\n  Indicator,\n};\nexport type { ProgressProps, ProgressIndicatorProps };\n"], "names": [], "mappings": ";;;;;;;;AAAA,YAAY,WAAW;AACvB,SAAS,0BAA0B;AACnC,SAAS,iBAAiB;AAoDlB;;;;;;AA5CR,IAAM,gBAAgB;AACtB,IAAM,cAAc;AAGpB,IAAM,CAAC,uBAAuB,mBAAmB,CAAA,2KAAI,qBAAA,EAAmB,aAAa;AAIrF,IAAM,CAAC,kBAAkB,kBAAkB,CAAA,GACzC,sBAA4C,aAAa;AAU3D,IAAM,oNAAiB,cAAA,EACrB,CAAC,OAAmC,iBAAiB;IACnD,MAAM,EACJ,eAAA,EACA,OAAO,YAAY,IAAA,EACnB,KAAK,OAAA,EACL,gBAAgB,oBAAA,EAChB,GAAG,eACL,GAAI;IAEJ,IAAA,CAAK,WAAW,YAAY,CAAA,KAAM,CAAC,iBAAiB,OAAO,GAAG;QAC5D,QAAQ,KAAA,CAAM,mBAAmB,GAAG,OAAO,EAAA,EAAI,UAAU,CAAC;IAC5D;IAEA,MAAM,MAAM,iBAAiB,OAAO,IAAI,UAAU;IAElD,IAAI,cAAc,QAAQ,CAAC,mBAAmB,WAAW,GAAG,GAAG;QAC7D,QAAQ,KAAA,CAAM,qBAAqB,GAAG,SAAS,EAAA,EAAI,UAAU,CAAC;IAChE;IAEA,MAAM,QAAQ,mBAAmB,WAAW,GAAG,IAAI,YAAY;IAC/D,MAAM,aAAa,SAAS,KAAK,IAAI,cAAc,OAAO,GAAG,IAAI,KAAA;IAEjE,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,kBAAA;QAAiB,OAAO;QAAiB;QAAc;QACtD,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,GAAA,EAAV;YACC,iBAAe;YACf,iBAAe;YACf,iBAAe,SAAS,KAAK,IAAI,QAAQ,KAAA;YACzC,kBAAgB;YAChB,MAAK;YACL,cAAY,iBAAiB,OAAO,GAAG;YACvC,cAAY,SAAS,KAAA;YACrB,YAAU;YACT,GAAG,aAAA;YACJ,KAAK;QAAA;IACP,CACF;AAEJ;AAGF,SAAS,WAAA,GAAc;AAMvB,IAAM,iBAAiB;AAKvB,IAAM,8NAA0B,aAAA,EAC9B,CAAC,OAA4C,iBAAiB;IAC5D,MAAM,EAAE,eAAA,EAAiB,GAAG,eAAe,CAAA,GAAI;IAC/C,MAAM,UAAU,mBAAmB,gBAAgB,eAAe;IAClE,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,GAAA,EAAV;QACC,cAAY,iBAAiB,QAAQ,KAAA,EAAO,QAAQ,GAAG;QACvD,cAAY,QAAQ,KAAA,IAAS,KAAA;QAC7B,YAAU,QAAQ,GAAA;QACjB,GAAG,cAAA;QACJ,KAAK;IAAA;AAGX;AAGF,kBAAkB,WAAA,GAAc;AAIhC,SAAS,qBAAqB,KAAA,EAAe,GAAA,EAAa;IACxD,OAAO,GAAG,KAAK,KAAA,CAAO,QAAQ,MAAO,GAAG,CAAC,CAAA,CAAA,CAAA;AAC3C;AAEA,SAAS,iBAAiB,KAAA,EAAkC,QAAA,EAAiC;IAC3F,OAAO,SAAS,OAAO,kBAAkB,UAAU,WAAW,aAAa;AAC7E;AAEA,SAAS,SAAS,KAAA,EAA6B;IAC7C,OAAO,OAAO,UAAU;AAC1B;AAEA,SAAS,iBAAiB,GAAA,EAAyB;IAEjD,OACE,SAAS,GAAG,KACZ,CAAC,MAAM,GAAG,KACV,MAAM;AAEV;AAEA,SAAS,mBAAmB,KAAA,EAAY,GAAA,EAA8B;IAEpE,OACE,SAAS,KAAK,KACd,CAAC,MAAM,KAAK,KACZ,SAAS,OACT,SAAS;AAEb;AAGA,SAAS,mBAAmB,SAAA,EAAmB,aAAA,EAAuB;IACpE,OAAO,CAAA,gCAAA,EAAmC,SAAS,CAAA,iBAAA,EAAoB,aAAa,CAAA,sEAAA,EAAyE,WAAW,CAAA,GAAA,CAAA;AAC1K;AAEA,SAAS,qBAAqB,SAAA,EAAmB,aAAA,EAAuB;IACtE,OAAO,CAAA,kCAAA,EAAqC,SAAS,CAAA,iBAAA,EAAoB,aAAa,CAAA;;8CAAA,EAExC,WAAW,CAAA;;;uBAAA,CAAA;AAI3D;AAEA,IAAM,OAAO;AACb,IAAM,YAAY", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1323, "column": 0}, "map": {"version": 3, "file": "search.js", "sources": ["file:///C:/Users/<USER>/Desktop/test/js/web3/packages/chario/node_modules/lucide-react/src/icons/search.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm21 21-4.34-4.34', key: '14j7rj' }],\n  ['circle', { cx: '11', cy: '11', r: '8', key: '4ej97u' }],\n];\n\n/**\n * @component @name Search\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMjEgMjEtNC4zNC00LjM0IiAvPgogIDxjaXJjbGUgY3g9IjExIiBjeT0iMTEiIHI9IjgiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/search\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Search = createLucideIcon('search', __iconNode);\n\nexport default Search;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAoB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACjD;QAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;YAAE,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAK,CAAA,CAAA,CAAA,CAAA,QAAA;QAAU,CAAA;KAAA;CAC1D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAU,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1371, "column": 0}, "map": {"version": 3, "file": "readContract.js", "sourceRoot": "", "sources": ["../../../src/query/readContract.ts"], "names": [], "mappings": ";;;;AAGA,OAAO,EAIL,YAAY,GACb,MAAM,4BAA4B,CAAA;AAInC,OAAO,EAAE,kBAAkB,EAAE,MAAM,YAAY,CAAA;;;AAUzC,SAAU,wBAAwB,CAMtC,MAAc,EACd,UAAgE,CAAA,CAAS;IAEzE,OAAO;QACL,6DAA6D;QAC7D,qEAAqE;QACrE,KAAK,CAAC,OAAO,EAAC,EAAE,QAAQ,EAAE;YACxB,MAAM,GAAG,GAAG,OAAO,CAAC,GAAU,CAAA;YAC9B,IAAI,CAAC,GAAG,EAAE,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAA;YAE5C,MAAM,EAAE,YAAY,EAAE,QAAQ,EAAE,CAAC,EAAE,GAAG,UAAU,EAAE,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAA;YAChE,MAAM,mBAAmB,GAAG,CAAC,GAAG,EAAE;gBAChC,MAAM,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAsC,CAAA;gBAC/D,IAAI,MAAM,CAAC,OAAO,EAAE,OAAO;oBAAE,OAAO,EAAE,MAAM,CAAC,OAAO;gBAAA,CAAE,CAAA;gBACtD,IAAI,MAAM,CAAC,IAAI,EAAE,OAAO;oBAAE,IAAI,EAAE,MAAM,CAAC,IAAI;gBAAA,CAAE,CAAA;gBAC7C,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAA;YAChD,CAAC,CAAC,EAAE,CAAA;YAEJ,IAAI,CAAC,YAAY,EAAE,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAA;YAE9D,qLAAO,eAAA,AAAY,EAAC,MAAM,EAAE;gBAC1B,GAAG;gBACH,YAAY;gBACZ,IAAI,EAAE,UAAU,CAAC,IAA0B;gBAC3C,GAAG,mBAAmB;gBACtB,GAAG,UAAU;aACd,CAAuD,CAAA;QAC1D,CAAC;QACD,QAAQ,EAAE,oBAAoB,CAAC,OAAc,CAAQ;KAMtD,CAAA;AACH,CAAC;AAcK,SAAU,oBAAoB,CAKlC,UAAgE,CAAA,CAAS;IACzE,MAAM,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,IAAI,EAAE,GAAG,OAAO,CAAA;IACnC,OAAO;QAAC,cAAc;6KAAE,qBAAA,AAAkB,EAAC,IAAI,CAAC;KAAU,CAAA;AAC5D,CAAC", "debugId": null}}, {"offset": {"line": 1422, "column": 0}, "map": {"version": 3, "file": "useReadContract.js", "sourceRoot": "", "sources": ["../../../src/hooks/useReadContract.ts"], "names": [], "mappings": ";;;AAQA,OAAO,EAKL,wBAAwB,EACxB,iBAAiB,GAClB,MAAM,mBAAmB,CAAA;;AAI1B,OAAO,EAA2B,QAAQ,EAAE,MAAM,mBAAmB,CAAA;AACrE,OAAO,EAAE,UAAU,EAAE,MAAM,iBAAiB,CAAA;AAC5C,OAAO,EAAE,SAAS,EAAE,MAAM,gBAAgB,CAAA;AArB1C,YAAY,CAAA;;;;;AA8DN,SAAU,eAAe,CAO7B,aAMI,CAAA,CAAS;IAEb,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,YAAY,EAAE,KAAK,GAAG,CAAA,CAAE,EAAE,GAAG,UAAU,CAAA;IAC7D,aAAa;IACb,MAAM,IAAI,GAAG,UAAU,CAAC,IAAuB,CAAA;IAE/C,MAAM,MAAM,kKAAG,YAAA,AAAS,EAAC,UAAU,CAAC,CAAA;IACpC,MAAM,OAAO,mKAAG,aAAA,AAAU,EAAC;QAAE,MAAM;IAAA,CAAE,CAAC,CAAA;IAEtC,MAAM,OAAO,IAAG,sMAAA,AAAwB,EACtC,MAAM,EACN;QAAE,GAAI,UAAkB;QAAE,OAAO,EAAE,UAAU,CAAC,OAAO,IAAI,OAAO;IAAA,CAAE,CACnE,CAAA;IACD,MAAM,OAAO,GAAG,OAAO,CACrB,CAAC,OAAO,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,YAAY,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,IAAI,CAAC,CACpE,CAAA;IAED,kLAAO,WAAA,AAAQ,EAAC;QACd,GAAG,KAAK;QACR,GAAG,OAAO;QACV,OAAO;QACP,iBAAiB,EAAE,KAAK,CAAC,iBAAiB,qKAAI,oBAAiB;KAChE,CAAC,CAAA;AACJ,CAAC", "debugId": null}}, {"offset": {"line": 1461, "column": 0}, "map": {"version": 3, "file": "assertCurrentChain.js", "sourceRoot": "", "sources": ["../../../utils/chain/assertCurrentChain.ts"], "names": [], "mappings": ";;;AAAA,OAAO,EACL,kBAAkB,EAElB,kBAAkB,GAEnB,MAAM,uBAAuB,CAAA;;AAcxB,SAAU,kBAAkB,CAAC,EACjC,KAAK,EACL,cAAc,EACe;IAC7B,IAAI,CAAC,KAAK,EAAE,MAAM,oJAAI,qBAAkB,EAAE,CAAA;IAC1C,IAAI,cAAc,KAAK,KAAK,CAAC,EAAE,EAC7B,MAAM,oJAAI,qBAAkB,CAAC;QAAE,KAAK;QAAE,cAAc;IAAA,CAAE,CAAC,CAAA;AAC3D,CAAC", "debugId": null}}, {"offset": {"line": 1479, "column": 0}, "map": {"version": 3, "file": "getTransactionError.js", "sourceRoot": "", "sources": ["../../../utils/errors/getTransactionError.ts"], "names": [], "mappings": ";;;AAGA,OAAO,EAAE,gBAAgB,EAAE,MAAM,sBAAsB,CAAA;AACvD,OAAO,EACL,yBAAyB,GAE1B,MAAM,6BAA6B,CAAA;AAIpC,OAAO,EAGL,YAAY,GACb,MAAM,mBAAmB,CAAA;;;;AAgBpB,SAAU,mBAAmB,CACjC,GAAQ,EACR,EAAE,QAAQ,EAAE,GAAG,IAAI,EAAiC;IAEpD,MAAM,KAAK,GAAG,CAAC,GAAG,EAAE;QAClB,MAAM,KAAK,uKAAG,eAAA,AAAY,EACxB,GAAsB,EACtB,IAA8B,CAC/B,CAAA;QACD,IAAI,KAAK,2JAAY,mBAAgB,EAAE,OAAO,GAAsB,CAAA;QACpE,OAAO,KAAK,CAAA;IACd,CAAC,CAAC,EAAE,CAAA;IACJ,OAAO,0JAAI,4BAAyB,CAAC,KAAK,EAAE;QAC1C,QAAQ;QACR,GAAG,IAAI;KACR,CAAuC,CAAA;AAC1C,CAAC", "debugId": null}}, {"offset": {"line": 1505, "column": 0}, "map": {"version": 3, "file": "sendTransaction.js", "sourceRoot": "", "sources": ["../../../actions/wallet/sendTransaction.ts"], "names": [], "mappings": ";;;AAGA,OAAO,EAEL,YAAY,GACb,MAAM,sCAAsC,CAAA;AAI7C,OAAO,EACL,oBAAoB,EAEpB,4BAA4B,GAE7B,MAAM,yBAAyB,CAAA;AAChC,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAA;AAYhD,OAAO,EAEL,2BAA2B,GAC5B,MAAM,0DAA0D,CAAA;AAEjE,OAAO,EAEL,kBAAkB,GACnB,MAAM,yCAAyC,CAAA;AAChD,OAAO,EAEL,mBAAmB,GACpB,MAAM,2CAA2C,CAAA;AAClD,OAAO,EAAE,OAAO,EAAE,MAAM,mCAAmC,CAAA;AAC3D,OAAO,EAEL,wBAAwB,GACzB,MAAM,8CAA8C,CAAA;AACrD,OAAO,EAAE,SAAS,EAAE,MAAM,0BAA0B,CAAA;AACpD,OAAO,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAA;AAC3C,OAAO,EAGL,aAAa,GACd,MAAM,0CAA0C,CAAA;AACjD,OAAO,EAA4B,UAAU,EAAE,MAAM,yBAAyB,CAAA;AAC9E,OAAO,EAEL,iBAAiB,EACjB,yBAAyB,GAC1B,MAAM,gCAAgC,CAAA;AACvC,OAAO,EAEL,kBAAkB,GACnB,MAAM,yBAAyB,CAAA;;;;;;;;;;;;;;;AAEhC,MAAM,uBAAuB,GAAG,iJAAI,SAAM,CAAU,GAAG,CAAC,CAAA;AAsFjD,KAAK,UAAU,eAAe,CAMnC,MAAyC,EACzC,UAA6E;IAE7E,MAAM,EACJ,OAAO,EAAE,QAAQ,GAAG,MAAM,CAAC,OAAO,EAClC,KAAK,GAAG,MAAM,CAAC,KAAK,EACpB,UAAU,EACV,iBAAiB,EACjB,KAAK,EACL,IAAI,EACJ,GAAG,EACH,QAAQ,EACR,gBAAgB,EAChB,YAAY,EACZ,oBAAoB,EACpB,KAAK,EACL,IAAI,EACJ,KAAK,EACL,GAAG,IAAI,EACR,GAAG,UAAU,CAAA;IAEd,IAAI,OAAO,QAAQ,KAAK,WAAW,EACjC,MAAM,IAAI,yKAAoB,CAAC;QAC7B,QAAQ,EAAE,sCAAsC;KACjD,CAAC,CAAA;IACJ,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,uKAAC,eAAA,AAAY,EAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;IAExD,IAAI,CAAC;SACH,yLAAA,AAAa,EAAC,UAAqC,CAAC,CAAA;QAEpD,MAAM,EAAE,GAAG,MAAM,CAAC,KAAK,IAAI,EAAE;YAC3B,8CAA8C;YAC9C,IAAI,UAAU,CAAC,EAAE,EAAE,OAAO,UAAU,CAAC,EAAE,CAAA;YAEvC,4DAA4D;YAC5D,IAAI,UAAU,CAAC,EAAE,KAAK,IAAI,EAAE,OAAO,SAAS,CAAA;YAE5C,wEAAwE;YACxE,kDAAkD;YAClD,IAAI,iBAAiB,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC,EACnD,OAAO,MAAM,wNAAA,AAA2B,EAAC;gBACvC,aAAa,EAAE,iBAAiB,CAAC,CAAC,CAAC;aACpC,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE;gBACZ,MAAM,IAAI,2JAAS,CACjB,6DAA6D,CAC9D,CAAA;YACH,CAAC,CAAC,CAAA;YAEJ,sDAAsD;YACtD,OAAO,SAAS,CAAA;QAClB,CAAC,CAAC,EAAE,CAAA;QAEJ,IAAI,OAAO,EAAE,IAAI,KAAK,UAAU,IAAI,OAAO,KAAK,IAAI,EAAE,CAAC;YACrD,IAAI,OAA2B,CAAA;YAC/B,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;gBACnB,OAAO,GAAG,6JAAM,YAAA,AAAS,EAAC,MAAM,kKAAE,aAAU,EAAE,YAAY,CAAC,CAAC,CAAA,CAAE,CAAC,CAAA;gBAC/D,8LAAA,AAAkB,EAAC;oBACjB,cAAc,EAAE,OAAO;oBACvB,KAAK;iBACN,CAAC,CAAA;YACJ,CAAC;YAED,MAAM,WAAW,GAAG,MAAM,CAAC,KAAK,EAAE,UAAU,EAAE,kBAAkB,EAAE,MAAM,CAAA;YACxE,MAAM,MAAM,GAAG,WAAW,8KAAI,2BAAwB,CAAA;YAEtD,MAAM,OAAO,GAAG,MAAM,CAAC;gBACrB,gFAAgF;gBAChF,OAAG,yKAAA,AAAO,EAAC,IAAI,EAAE;oBAAE,MAAM,EAAE,WAAW;gBAAA,CAAE,CAAC;gBACzC,UAAU;gBACV,iBAAiB;gBACjB,KAAK;gBACL,OAAO;gBACP,IAAI;gBACJ,IAAI,EAAE,OAAO,EAAE,OAAO;gBACtB,GAAG;gBACH,QAAQ;gBACR,gBAAgB;gBAChB,YAAY;gBACZ,oBAAoB;gBACpB,KAAK;gBACL,EAAE;gBACF,IAAI;gBACJ,KAAK;aACgB,CAAC,CAAA;YAExB,MAAM,0BAA0B,GAAG,uBAAuB,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;YAC1E,MAAM,MAAM,GAAG,0BAA0B,GACrC,wBAAwB,GACxB,qBAAqB,CAAA;YAEzB,IAAI,CAAC;gBACH,OAAO,MAAM,MAAM,CAAC,OAAO,CACzB;oBACE,MAAM;oBACN,MAAM,EAAE;wBAAC,OAAO;qBAAC;iBAClB,EACD;oBAAE,UAAU,EAAE,CAAC;gBAAA,CAAE,CAClB,CAAA;YACH,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;gBACX,IAAI,0BAA0B,KAAK,KAAK,EAAE,MAAM,CAAC,CAAA;gBAEjD,MAAM,KAAK,GAAG,CAAc,CAAA;gBAC5B,4EAA4E;gBAC5E,mCAAmC;gBACnC,IACE,KAAK,CAAC,IAAI,KAAK,sBAAsB,IACrC,KAAK,CAAC,IAAI,KAAK,uBAAuB,IACtC,KAAK,CAAC,IAAI,KAAK,wBAAwB,IACvC,KAAK,CAAC,IAAI,KAAK,4BAA4B,EAC3C,CAAC;oBACD,OAAO,MAAM,MAAM,CAChB,OAAO,CACN;wBACE,MAAM,EAAE,wBAAwB;wBAChC,MAAM,EAAE;4BAAC,OAAO;yBAAC;qBAClB,EACD;wBAAE,UAAU,EAAE,CAAC;oBAAA,CAAE,CAClB,CACA,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE;wBACb,uBAAuB,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,CAAA;wBAC7C,OAAO,IAAI,CAAA;oBACb,CAAC,CAAC,CACD,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE;wBACX,MAAM,oBAAoB,GAAG,CAAc,CAAA;wBAC3C,IACE,oBAAoB,CAAC,IAAI,KAAK,wBAAwB,IACtD,oBAAoB,CAAC,IAAI,KAAK,4BAA4B,EAC1D,CAAC;4BACD,uBAAuB,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA;4BAC9C,MAAM,KAAK,CAAA;wBACb,CAAC;wBAED,MAAM,oBAAoB,CAAA;oBAC5B,CAAC,CAAC,CAAA;gBACN,CAAC;gBAED,MAAM,KAAK,CAAA;YACb,CAAC;QACH,CAAC;QAED,IAAI,OAAO,EAAE,IAAI,KAAK,OAAO,EAAE,CAAC;YAC9B,kEAAkE;YAClE,MAAM,OAAO,GAAG,6JAAM,YAAA,AAAS,EAC7B,MAAM,EACN,2MAAyB,EACzB,2BAA2B,CAC5B,CAAC;gBACA,OAAO;gBACP,UAAU;gBACV,iBAAiB;gBACjB,KAAK;gBACL,KAAK;gBACL,IAAI;gBACJ,GAAG;gBACH,QAAQ;gBACR,gBAAgB;gBAChB,YAAY;gBACZ,oBAAoB;gBACpB,KAAK;gBACL,YAAY,EAAE,OAAO,CAAC,YAAY;gBAClC,UAAU,EAAE,CAAC;sMAAG,oBAAiB;oBAAE,UAAU;iBAAC;gBAC9C,IAAI;gBACJ,KAAK;gBACL,GAAG,IAAI;gBACP,EAAE;aACI,CAAC,CAAA;YAET,MAAM,UAAU,GAAG,KAAK,EAAE,WAAW,EAAE,WAAW,CAAA;YAClD,MAAM,qBAAqB,GAAG,AAAC,MAAM,OAAO,CAAC,eAAe,CAAC,OAAO,EAAE;gBACpE,UAAU;aACX,CAAC,CAAS,CAAA;YACX,OAAO,6JAAM,YAAA,AAAS,EACpB,MAAM,0KACN,qBAAkB,EAClB,oBAAoB,CACrB,CAAC;gBACA,qBAAqB;aACtB,CAAC,CAAA;QACJ,CAAC;QAED,IAAI,OAAO,EAAE,IAAI,KAAK,OAAO,EAC3B,MAAM,sJAAI,+BAA4B,CAAC;YACrC,YAAY,EAAE;gBACZ,wDAAwD;aACzD;YACD,QAAQ,EAAE,yCAAyC;YACnD,IAAI,EAAE,OAAO;SACd,CAAC,CAAA;QAEJ,MAAM,sJAAI,+BAA4B,CAAC;YACrC,QAAQ,EAAE,sCAAsC;YAChD,IAAI,EAAG,OAAe,EAAE,IAAI;SAC7B,CAAC,CAAA;IACJ,CAAC,CAAC,OAAO,GAAG,EAAE,CAAC;QACb,IAAI,GAAG,YAAY,iLAA4B,EAAE,MAAM,GAAG,CAAA;QAC1D,iLAAM,sBAAA,AAAmB,EAAC,GAAgB,EAAE;YAC1C,GAAG,UAAU;YACb,OAAO;YACP,KAAK,EAAE,UAAU,CAAC,KAAK,IAAI,SAAS;SACrC,CAAC,CAAA;IACJ,CAAC;AACH,CAAC", "debugId": null}}, {"offset": {"line": 1690, "column": 0}, "map": {"version": 3, "file": "writeContract.js", "sourceRoot": "", "sources": ["../../../actions/wallet/writeContract.ts"], "names": [], "mappings": ";;;AAGA,OAAO,EAEL,YAAY,GACb,MAAM,sCAAsC,CAAA;AAG7C,OAAO,EACL,oBAAoB,GAErB,MAAM,yBAAyB,CAAA;AAgBhC,OAAO,EAGL,kBAAkB,GACnB,MAAM,uCAAuC,CAAA;AAC9C,OAAO,EAEL,gBAAgB,GACjB,MAAM,wCAAwC,CAAA;AAE/C,OAAO,EAAE,SAAS,EAAE,MAAM,0BAA0B,CAAA;AAEpD,OAAO,EAGL,eAAe,GAChB,MAAM,sBAAsB,CAAA;;;;;;;AA2GtB,KAAK,UAAU,aAAa,CAYjC,MAAyC,EACzC,UAOC;IAED,MAAM,EACJ,GAAG,EACH,OAAO,EAAE,QAAQ,GAAG,MAAM,CAAC,OAAO,EAClC,OAAO,EACP,IAAI,EACJ,UAAU,EACV,YAAY,EACZ,GAAG,OAAO,EACX,GAAG,UAAqC,CAAA;IAEzC,IAAI,OAAO,QAAQ,KAAK,WAAW,EACjC,MAAM,qJAAI,wBAAoB,CAAC;QAC7B,QAAQ,EAAE,8BAA8B;KACzC,CAAC,CAAA;IACJ,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,uKAAC,eAAA,AAAY,EAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;IAExD,MAAM,IAAI,0KAAG,qBAAA,AAAkB,EAAC;QAC9B,GAAG;QACH,IAAI;QACJ,YAAY;KACmB,CAAC,CAAA;IAElC,IAAI,CAAC;QACH,OAAO,MAAM,mKAAA,AAAS,EACpB,MAAM,uKACN,kBAAe,EACf,iBAAiB,CAClB,CAAC;YACA,IAAI,EAAE,GAAG,IAAI,GAAG,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;YAChE,EAAE,EAAE,OAAO;YACX,OAAO;YACP,GAAG,OAAO;SACX,CAAC,CAAA;IACJ,CAAC,CAAC,OAAO,KAAK,EAAE,CAAC;QACf,8KAAM,mBAAA,AAAgB,EAAC,KAAkB,EAAE;YACzC,GAAG;YACH,OAAO;YACP,IAAI;YACJ,QAAQ,EAAE,8BAA8B;YACxC,YAAY;YACZ,MAAM,EAAE,OAAO,EAAE,OAAO;SACzB,CAAC,CAAA;IACJ,CAAC;AACH,CAAC", "debugId": null}}, {"offset": {"line": 1740, "column": 0}, "map": {"version": 3, "file": "writeContract.js", "sourceRoot": "", "sources": ["../../../src/actions/writeContract.ts"], "names": [], "mappings": ";;;AAQA,OAAO,EAIL,aAAa,IAAI,kBAAkB,GACpC,MAAM,cAAc,CAAA;AAUrB,OAAO,EAAE,SAAS,EAAE,MAAM,uBAAuB,CAAA;AACjD,OAAO,EAEL,kBAAkB,GACnB,MAAM,yBAAyB,CAAA;;;;AAoDzB,KAAK,UAAU,aAAa,CAWjC,MAAc,EACd,UAA6E;IAE7E,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,GAAG,UAAU,CAAA;IAE9D,IAAI,MAAc,CAAA;IAClB,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,EAAE,IAAI,KAAK,OAAO,EAC1D,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC;QAAE,OAAO;IAAA,CAAE,CAAC,CAAA;SAEtC,MAAM,GAAG,0LAAM,qBAAA,AAAkB,EAAC,MAAM,EAAE;QACxC,OAAO,EAAE,OAAO,IAAI,SAAS;QAC7B,OAAO;QACP,SAAS;KACV,CAAC,CAAA;IAEJ,MAAM,MAAM,4KAAG,YAAA,AAAS,EAAC,MAAM,qKAAE,gBAAkB,EAAE,eAAe,CAAC,CAAA;IACrE,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC;QACxB,GAAI,OAAe;QACnB,GAAG,AAAC,OAAO,CAAC,CAAC,CAAC;YAAE,OAAO;QAAA,CAAE,CAAC,CAAC,CAAC,CAAA,CAAE,CAAC;QAC/B,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC;YAAE,EAAE,EAAE,OAAO;QAAA,CAAE,CAAC,CAAC,CAAC,IAAI;KACxC,CAAC,CAAA;IAEF,OAAO,IAAI,CAAA;AACb,CAAC", "debugId": null}}, {"offset": {"line": 1778, "column": 0}, "map": {"version": 3, "file": "writeContract.js", "sourceRoot": "", "sources": ["../../../src/query/writeContract.ts"], "names": [], "mappings": ";;;AAGA,OAAO,EAIL,aAAa,GACd,MAAM,6BAA6B,CAAA;;AAI9B,SAAU,4BAA4B,CAC1C,MAAc;IAEd,OAAO;QACL,UAAU,EAAC,SAAS;YAClB,sLAAO,gBAAA,AAAa,EAAC,MAAM,EAAE,SAAS,CAAC,CAAA;QACzC,CAAC;QACD,WAAW,EAAE;YAAC,eAAe;SAAC;KAW/B,CAAA;AACH,CAAC", "debugId": null}}, {"offset": {"line": 1799, "column": 0}, "map": {"version": 3, "file": "useWriteContract.js", "sourceRoot": "", "sources": ["../../../src/hooks/useWriteContract.ts"], "names": [], "mappings": ";;;AAEA,OAAO,EAAE,WAAW,EAAE,MAAM,uBAAuB,CAAA;AAMnD,OAAO,EAKL,4BAA4B,GAC7B,MAAM,mBAAmB,CAAA;AAQ1B,OAAO,EAAE,SAAS,EAAE,MAAM,gBAAgB,CAAA;AAtB1C,YAAY,CAAA;;;;AAgEN,SAAU,gBAAgB,CAI9B,aAA0D,CAAA,CAAE;IAE5D,MAAM,EAAE,QAAQ,EAAE,GAAG,UAAU,CAAA;IAE/B,MAAM,MAAM,IAAG,0KAAA,AAAS,EAAC,UAAU,CAAC,CAAA;IAEpC,MAAM,eAAe,gLAAG,+BAAA,AAA4B,EAAC,MAAM,CAAC,CAAA;IAC5D,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,GAAG,MAAM,EAAE,sLAAG,cAAA,AAAW,EAAC;QACrD,GAAG,QAAQ;QACX,GAAG,eAAe;KACnB,CAAC,CAAA;IAGF,OAAO;QACL,GAAG,MAAM;QACT,aAAa,EAAE,MAAiC;QAChD,kBAAkB,EAAE,WAA2C;KAChE,CAAA;AACH,CAAC", "debugId": null}}, {"offset": {"line": 1829, "column": 0}, "map": {"version": 3, "file": "waitForTransactionReceipt.js", "sourceRoot": "", "sources": ["../../../src/actions/waitForTransactionReceipt.ts"], "names": [], "mappings": ";;;AACA,OAAO,EAAE,WAAW,EAAE,MAAM,MAAM,CAAA;AAClC,OAAO,EACL,IAAI,EACJ,cAAc,EAId,yBAAyB,IAAI,8BAA8B,GAC5D,MAAM,cAAc,CAAA;;;AAMrB,OAAO,EAAE,SAAS,EAAE,MAAM,uBAAuB,CAAA;;;;AA2B1C,KAAK,UAAU,yBAAyB,CAI7C,MAAc,EACd,UAAgE;IAEhE,MAAM,EAAE,OAAO,EAAE,OAAO,GAAG,CAAC,EAAE,GAAG,IAAI,EAAE,GAAG,UAAU,CAAA;IAEpD,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC;QAAE,OAAO;IAAA,CAAE,CAAC,CAAA;IAC5C,MAAM,MAAM,4KAAG,YAAA,AAAS,EACtB,MAAM,iLACN,4BAA8B,EAC9B,2BAA2B,CAC5B,CAAA;IACD,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC;QAAE,GAAG,IAAI;QAAE,OAAO;IAAA,CAAE,CAAC,CAAA;IAElD,IAAI,OAAO,CAAC,MAAM,KAAK,UAAU,EAAE,CAAC;QAClC,MAAM,qBAAqB,4KAAG,YAAA,AAAS,EACrC,MAAM,sKACN,iBAAc,EACd,gBAAgB,CACjB,CAAA;QACD,MAAM,GAAG,GAAG,MAAM,qBAAqB,CAAC;YAAE,IAAI,EAAE,OAAO,CAAC,eAAe;QAAA,CAAE,CAAC,CAAA;QAC1E,MAAM,WAAW,4KAAG,YAAA,AAAS,EAAC,MAAM,2JAAE,QAAI,EAAE,MAAM,CAAC,CAAA;QACnD,MAAM,IAAI,GAAG,MAAM,WAAW,CAAC;YAC7B,GAAI,GAAW;YACf,IAAI,EAAE,GAAG,CAAC,KAAK;YACf,QAAQ,EAAE,GAAG,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS;YAC3D,YAAY,EAAE,GAAG,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,SAAS;YACnE,oBAAoB,EAClB,GAAG,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC,CAAC,SAAS;SAChE,CAAC,CAAA;QACF,MAAM,MAAM,GAAG,IAAI,EAAE,IAAI,oKACrB,cAAA,AAAW,EAAC,CAAA,EAAA,EAAK,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,GAC5C,gBAAgB,CAAA;QACpB,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC,CAAA;IACzB,CAAC;IAED,OAAO;QACL,GAAG,OAAO;QACV,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;KAC+B,CAAA;AAC3D,CAAC", "debugId": null}}, {"offset": {"line": 1877, "column": 0}, "map": {"version": 3, "file": "waitForTransactionReceipt.js", "sourceRoot": "", "sources": ["../../../src/query/waitForTransactionReceipt.ts"], "names": [], "mappings": ";;;;AAEA,OAAO,EAIL,yBAAyB,GAC1B,MAAM,yCAAyC,CAAA;AAIhD,OAAO,EAAE,kBAAkB,EAAE,MAAM,YAAY,CAAA;;;AAUzC,SAAU,qCAAqC,CAInD,MAAc,EACd,UAA6D,CAAA,CAAE;IAE/D,OAAO;QACL,KAAK,CAAC,OAAO,EAAC,EAAE,QAAQ,EAAE;YACxB,MAAM,EAAE,IAAI,EAAE,GAAG,UAAU,EAAE,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAA;YAC3C,IAAI,CAAC,IAAI,EAAE,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAA;YAC9C,kMAAO,4BAAA,AAAyB,EAAC,MAAM,EAAE;gBACvC,GAAG,UAAU;gBACb,UAAU,EAAE,OAAO,CAAC,UAAU;gBAC9B,IAAI;aACL,CAEA,CAAA;QACH,CAAC;QACD,QAAQ,EAAE,iCAAiC,CAAC,OAAO,CAAC;KAMrD,CAAA;AACH,CAAC;AAYK,SAAU,iCAAiC,CAG/C,UAA6D,CAAA,CAAE;IAC/D,MAAM,EAAE,UAAU,EAAE,CAAC,EAAE,GAAG,IAAI,EAAE,GAAG,OAAO,CAAA;IAC1C,OAAO;QAAC,2BAA2B;6KAAE,qBAAA,AAAkB,EAAC,IAAI,CAAC;KAAU,CAAA;AACzE,CAAC", "debugId": null}}, {"offset": {"line": 1912, "column": 0}, "map": {"version": 3, "file": "useWaitForTransactionReceipt.js", "sourceRoot": "", "sources": ["../../../src/hooks/useWaitForTransactionReceipt.ts"], "names": [], "mappings": ";;;AAQA,OAAO,EAKL,qCAAqC,GACtC,MAAM,mBAAmB,CAAA;AAG1B,OAAO,EAA2B,QAAQ,EAAE,MAAM,mBAAmB,CAAA;AACrE,OAAO,EAAE,UAAU,EAAE,MAAM,iBAAiB,CAAA;AAC5C,OAAO,EAAE,SAAS,EAAE,MAAM,gBAAgB,CAAA;AAnB1C,YAAY,CAAA;;;;;AA6CN,SAAU,4BAA4B,CAM1C,aAII,CAAA,CAAE;IAEN,MAAM,EAAE,IAAI,EAAE,KAAK,GAAG,CAAA,CAAE,EAAE,GAAG,UAAU,CAAA;IAEvC,MAAM,MAAM,kKAAG,YAAA,AAAS,EAAC,UAAU,CAAC,CAAA;IACpC,MAAM,OAAO,mKAAG,aAAA,AAAU,EAAC;QAAE,MAAM;IAAA,CAAE,CAAC,CAAA;IAEtC,MAAM,OAAO,IAAG,gOAAA,AAAqC,EAAC,MAAM,EAAE;QAC5D,GAAG,UAAU;QACb,OAAO,EAAE,UAAU,CAAC,OAAO,IAAI,OAAO;KACvC,CAAC,CAAA;IACF,MAAM,OAAO,GAAG,OAAO,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,IAAI,CAAC,CAAC,CAAA;IAExD,kLAAO,WAAA,AAAQ,EAAC;QACd,GAAI,KAAa;QACjB,GAAG,OAAO;QACV,OAAO;KACR,CAAwE,CAAA;AAC3E,CAAC", "debugId": null}}, {"offset": {"line": 1947, "column": 0}, "map": {"version": 3, "file": "unit.js", "sourceRoot": "", "sources": ["../../errors/unit.ts"], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,WAAW,CAAA;;AAK/B,MAAO,yBAA0B,wJAAQ,YAAS;IACtD,YAAY,EAAE,KAAK,EAAqB,CAAA;QACtC,KAAK,CAAC,CAAA,SAAA,EAAY,KAAK,CAAA,iCAAA,CAAmC,EAAE;YAC1D,IAAI,EAAE,2BAA2B;SAClC,CAAC,CAAA;IACJ,CAAC;CACF", "debugId": null}}, {"offset": {"line": 1965, "column": 0}, "map": {"version": 3, "file": "parseUnits.js", "sourceRoot": "", "sources": ["../../../utils/unit/parseUnits.ts"], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,yBAAyB,EAAE,MAAM,sBAAsB,CAAA;;AAgB1D,SAAU,UAAU,CAAC,KAAa,EAAE,QAAgB;IACxD,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,KAAK,CAAC,EAC1C,MAAM,mJAAI,4BAAyB,CAAC;QAAE,KAAK;IAAA,CAAE,CAAC,CAAA;IAEhD,IAAI,CAAC,OAAO,EAAE,QAAQ,GAAG,GAAG,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;IAEhD,MAAM,QAAQ,GAAG,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAA;IACxC,IAAI,QAAQ,EAAE,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;IAExC,uBAAuB;IACvB,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAA;IAExC,mEAAmE;IACnE,IAAI,QAAQ,KAAK,CAAC,EAAE,CAAC;QACnB,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA,CAAA,EAAI,QAAQ,EAAE,CAAC,CAAC,KAAK,CAAC,EAC1C,OAAO,GAAG,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAA;QACrC,QAAQ,GAAG,EAAE,CAAA;IACf,CAAC,MAAM,IAAI,QAAQ,CAAC,MAAM,GAAG,QAAQ,EAAE,CAAC;QACtC,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,GAAG;YAC1B,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,GAAG,CAAC,CAAC;YAC/B,QAAQ,CAAC,KAAK,CAAC,QAAQ,GAAG,CAAC,EAAE,QAAQ,CAAC;YACtC,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC;SACzB,CAAA;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,IAAI,CAAA,CAAA,EAAI,KAAK,EAAE,CAAC,CAAC,CAAA;QACtD,IAAI,OAAO,GAAG,CAAC,EACb,QAAQ,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAA,CAAA,CAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,GAAG,CAAC,CAAA;aACrE,QAAQ,GAAG,GAAG,IAAI,GAAG,OAAO,EAAE,CAAA;QAEnC,IAAI,QAAQ,CAAC,MAAM,GAAG,QAAQ,EAAE,CAAC;YAC/B,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;YAC5B,OAAO,GAAG,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAA;QACrC,CAAC;QAED,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAA;IACxC,CAAC,MAAM,CAAC;QACN,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAA;IAC3C,CAAC;IAED,OAAO,MAAM,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,OAAO,GAAG,QAAQ,EAAE,CAAC,CAAA;AAC9D,CAAC", "debugId": null}}, {"offset": {"line": 2008, "column": 0}, "map": {"version": 3, "file": "parseEther.js", "sourceRoot": "", "sources": ["../../../utils/unit/parseEther.ts"], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAA;AAGpD,OAAO,EAA4B,UAAU,EAAE,MAAM,iBAAiB,CAAA;;;AAehE,SAAU,UAAU,CAAC,KAAa,EAAE,OAAuB,KAAK;IACpE,uKAAO,aAAA,AAAU,EAAC,KAAK,oJAAE,aAAU,CAAC,IAAI,CAAC,CAAC,CAAA;AAC5C,CAAC", "debugId": null}}]}