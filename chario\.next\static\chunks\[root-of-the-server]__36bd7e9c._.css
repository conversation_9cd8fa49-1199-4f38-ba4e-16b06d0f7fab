/* [next]/internal/font/google/geist_7278d07b.module.css [app-client] (css) */
@font-face {
  font-family: Geist;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/gyByhwUxId8gMEwYGFWNOITddY4-s.b7d310ad.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Geist;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/gyByhwUxId8gMEwSGFWNOITddY4-s.81df3a5b.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Geist;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/gyByhwUxId8gMEwcGFWNOITd-s.p.da1ebef7.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Geist Fallback;
  src: local(Arial);
  ascent-override: 95.94%;
  descent-override: 28.16%;
  line-gap-override: 0.0%;
  size-adjust: 104.76%;
}

.geist_7278d07b-module__j7z1Ra__className {
  font-family: Geist, Geist Fallback;
  font-style: normal;
}

.geist_7278d07b-module__j7z1Ra__variable {
  --font-geist-sans: "Geist", "Geist Fallback";
}


/* [next]/internal/font/google/geist_mono_719fb246.module.css [app-client] (css) */
@font-face {
  font-family: Geist Mono;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/or3nQ6H_1_WfwkMZI_qYFrMdmhHkjkotbA-s.cb6bbcb1.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Geist Mono;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/or3nQ6H_1_WfwkMZI_qYFrkdmhHkjkotbA-s.e32db976.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Geist Mono;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/or3nQ6H_1_WfwkMZI_qYFrcdmhHkjko-s.p.be19f591.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Geist Mono Fallback;
  src: local(Arial);
  ascent-override: 74.67%;
  descent-override: 21.92%;
  line-gap-override: 0.0%;
  size-adjust: 134.59%;
}

.geist_mono_719fb246-module__quwlgG__className {
  font-family: Geist Mono, Geist Mono Fallback;
  font-style: normal;
}

.geist_mono_719fb246-module__quwlgG__variable {
  --font-geist-mono: "Geist Mono", "Geist Mono Fallback";
}


/* [project]/src/app/globals.css [app-client] (css) */
@layer properties {
  @supports (((-webkit-hyphens: none)) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color: rgb(from red r g b)))) {
    *, :before, :after, ::backdrop {
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-space-y-reverse: 0;
      --tw-space-x-reverse: 0;
      --tw-border-style: solid;
      --tw-gradient-position: initial;
      --tw-gradient-from: #0000;
      --tw-gradient-via: #0000;
      --tw-gradient-to: #0000;
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-tracking: initial;
      --tw-ordinal: initial;
      --tw-slashed-zero: initial;
      --tw-numeric-figure: initial;
      --tw-numeric-spacing: initial;
      --tw-numeric-fraction: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
      --tw-outline-style: solid;
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-backdrop-blur: initial;
      --tw-backdrop-brightness: initial;
      --tw-backdrop-contrast: initial;
      --tw-backdrop-grayscale: initial;
      --tw-backdrop-hue-rotate: initial;
      --tw-backdrop-invert: initial;
      --tw-backdrop-opacity: initial;
      --tw-backdrop-saturate: initial;
      --tw-backdrop-sepia: initial;
      --tw-duration: initial;
      --tw-ease: initial;
      --tw-content: "";
      --tw-animation-delay: 0s;
      --tw-animation-direction: normal;
      --tw-animation-duration: initial;
      --tw-animation-fill-mode: none;
      --tw-animation-iteration-count: 1;
      --tw-enter-opacity: 1;
      --tw-enter-rotate: 0;
      --tw-enter-scale: 1;
      --tw-enter-translate-x: 0;
      --tw-enter-translate-y: 0;
      --tw-exit-opacity: 1;
      --tw-exit-rotate: 0;
      --tw-exit-scale: 1;
      --tw-exit-translate-x: 0;
      --tw-exit-translate-y: 0;
    }
  }
}

.\@container\/card-header {
  container: card-header / inline-size;
}

.pointer-events-none {
  pointer-events: none;
}

.visible {
  visibility: visible;
}

.sr-only {
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  position: absolute;
  overflow: hidden;
}

.absolute {
  position: absolute;
}

.fixed {
  position: fixed;
}

.relative {
  position: relative;
}

.inset-0 {
  inset: calc(var(--spacing) * 0);
}

.inset-x-0 {
  inset-inline: calc(var(--spacing) * 0);
}

.inset-y-0 {
  inset-block: calc(var(--spacing) * 0);
}

.-top-1 {
  top: calc(var(--spacing) * -1);
}

.-top-2 {
  top: calc(var(--spacing) * -2);
}

.-top-4 {
  top: calc(var(--spacing) * -4);
}

.top-0 {
  top: calc(var(--spacing) * 0);
}

.top-0\.5 {
  top: calc(var(--spacing) * .5);
}

.top-1\.5 {
  top: calc(var(--spacing) * 1.5);
}

.top-1\/2 {
  top: 50%;
}

.top-2 {
  top: calc(var(--spacing) * 2);
}

.top-3\.5 {
  top: calc(var(--spacing) * 3.5);
}

.top-4 {
  top: calc(var(--spacing) * 4);
}

.top-\[5\%\] {
  top: 5%;
}

.top-\[10\%\] {
  top: 10%;
}

.top-\[15\%\] {
  top: 15%;
}

.top-\[50\%\] {
  top: 50%;
}

.top-\[70\%\] {
  top: 70%;
}

.-right-1 {
  right: calc(var(--spacing) * -1);
}

.right-0 {
  right: calc(var(--spacing) * 0);
}

.right-0\.5 {
  right: calc(var(--spacing) * .5);
}

.right-1 {
  right: calc(var(--spacing) * 1);
}

.right-2 {
  right: calc(var(--spacing) * 2);
}

.right-3 {
  right: calc(var(--spacing) * 3);
}

.right-4 {
  right: calc(var(--spacing) * 4);
}

.right-\[-5\%\] {
  right: -5%;
}

.right-\[15\%\] {
  right: 15%;
}

.bottom-0 {
  bottom: calc(var(--spacing) * 0);
}

.bottom-\[5\%\] {
  bottom: 5%;
}

.-left-2 {
  left: calc(var(--spacing) * -2);
}

.left-0 {
  left: calc(var(--spacing) * 0);
}

.left-1\/2 {
  left: 50%;
}

.left-2 {
  left: calc(var(--spacing) * 2);
}

.left-3 {
  left: calc(var(--spacing) * 3);
}

.left-\[-10\%\] {
  left: -10%;
}

.left-\[5\%\] {
  left: 5%;
}

.left-\[20\%\] {
  left: 20%;
}

.left-\[50\%\] {
  left: 50%;
}

.z-0 {
  z-index: 0;
}

.z-10 {
  z-index: 10;
}

.z-20 {
  z-index: 20;
}

.z-50 {
  z-index: 50;
}

.col-start-2 {
  grid-column-start: 2;
}

.row-span-2 {
  grid-row: span 2 / span 2;
}

.row-start-1 {
  grid-row-start: 1;
}

.container {
  width: 100%;
}

@media (width >= 40rem) {
  .container {
    max-width: 40rem;
  }
}

@media (width >= 48rem) {
  .container {
    max-width: 48rem;
  }
}

@media (width >= 64rem) {
  .container {
    max-width: 64rem;
  }
}

@media (width >= 80rem) {
  .container {
    max-width: 80rem;
  }
}

@media (width >= 96rem) {
  .container {
    max-width: 96rem;
  }
}

.m-3 {
  margin: calc(var(--spacing) * 3);
}

.-mx-1 {
  margin-inline: calc(var(--spacing) * -1);
}

.mx-2 {
  margin-inline: calc(var(--spacing) * 2);
}

.mx-3\.5 {
  margin-inline: calc(var(--spacing) * 3.5);
}

.mx-auto {
  margin-inline: auto;
}

.my-0\.5 {
  margin-block: calc(var(--spacing) * .5);
}

.my-1 {
  margin-block: calc(var(--spacing) * 1);
}

.-mt-2 {
  margin-top: calc(var(--spacing) * -2);
}

.mt-1 {
  margin-top: calc(var(--spacing) * 1);
}

.mt-2 {
  margin-top: calc(var(--spacing) * 2);
}

.mt-3 {
  margin-top: calc(var(--spacing) * 3);
}

.mt-4 {
  margin-top: calc(var(--spacing) * 4);
}

.mt-6 {
  margin-top: calc(var(--spacing) * 6);
}

.mt-8 {
  margin-top: calc(var(--spacing) * 8);
}

.mt-auto {
  margin-top: auto;
}

.-mr-2 {
  margin-right: calc(var(--spacing) * -2);
}

.mr-1 {
  margin-right: calc(var(--spacing) * 1);
}

.mb-0 {
  margin-bottom: calc(var(--spacing) * 0);
}

.mb-0\.5 {
  margin-bottom: calc(var(--spacing) * .5);
}

.mb-1 {
  margin-bottom: calc(var(--spacing) * 1);
}

.mb-2 {
  margin-bottom: calc(var(--spacing) * 2);
}

.mb-3 {
  margin-bottom: calc(var(--spacing) * 3);
}

.mb-4 {
  margin-bottom: calc(var(--spacing) * 4);
}

.mb-5 {
  margin-bottom: calc(var(--spacing) * 5);
}

.mb-6 {
  margin-bottom: calc(var(--spacing) * 6);
}

.mb-8 {
  margin-bottom: calc(var(--spacing) * 8);
}

.mb-16 {
  margin-bottom: calc(var(--spacing) * 16);
}

.-ml-1 {
  margin-left: calc(var(--spacing) * -1);
}

.-ml-2 {
  margin-left: calc(var(--spacing) * -2);
}

.ml-2 {
  margin-left: calc(var(--spacing) * 2);
}

.ml-3 {
  margin-left: calc(var(--spacing) * 3);
}

.ml-60 {
  margin-left: calc(var(--spacing) * 60);
}

.ml-auto {
  margin-left: auto;
}

.line-clamp-1 {
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  display: -webkit-box;
  overflow: hidden;
}

.line-clamp-2 {
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  display: -webkit-box;
  overflow: hidden;
}

.line-clamp-4 {
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  display: -webkit-box;
  overflow: hidden;
}

.block {
  display: block;
}

.flex {
  display: flex;
}

.grid {
  display: grid;
}

.hidden {
  display: none;
}

.inline-block {
  display: inline-block;
}

.inline-flex {
  display: inline-flex;
}

.table {
  display: table;
}

.table-caption {
  display: table-caption;
}

.table-cell {
  display: table-cell;
}

.table-row {
  display: table-row;
}

.field-sizing-content {
  field-sizing: content;
}

.aspect-auto {
  aspect-ratio: auto;
}

.aspect-square {
  aspect-ratio: 1;
}

.aspect-video {
  aspect-ratio: var(--aspect-video);
}

.size-2 {
  width: calc(var(--spacing) * 2);
  height: calc(var(--spacing) * 2);
}

.size-2\.5 {
  width: calc(var(--spacing) * 2.5);
  height: calc(var(--spacing) * 2.5);
}

.size-3\.5 {
  width: calc(var(--spacing) * 3.5);
  height: calc(var(--spacing) * 3.5);
}

.size-4 {
  width: calc(var(--spacing) * 4);
  height: calc(var(--spacing) * 4);
}

.size-7 {
  width: calc(var(--spacing) * 7);
  height: calc(var(--spacing) * 7);
}

.size-9 {
  width: calc(var(--spacing) * 9);
  height: calc(var(--spacing) * 9);
}

.size-full {
  width: 100%;
  height: 100%;
}

.h-1 {
  height: calc(var(--spacing) * 1);
}

.h-1\.5 {
  height: calc(var(--spacing) * 1.5);
}

.h-2 {
  height: calc(var(--spacing) * 2);
}

.h-2\.5 {
  height: calc(var(--spacing) * 2.5);
}

.h-4 {
  height: calc(var(--spacing) * 4);
}

.h-5 {
  height: calc(var(--spacing) * 5);
}

.h-6 {
  height: calc(var(--spacing) * 6);
}

.h-7 {
  height: calc(var(--spacing) * 7);
}

.h-8 {
  height: calc(var(--spacing) * 8);
}

.h-9 {
  height: calc(var(--spacing) * 9);
}

.h-10 {
  height: calc(var(--spacing) * 10);
}

.h-12 {
  height: calc(var(--spacing) * 12);
}

.h-16 {
  height: calc(var(--spacing) * 16);
}

.h-24 {
  height: calc(var(--spacing) * 24);
}

.h-48 {
  height: calc(var(--spacing) * 48);
}

.h-72 {
  height: calc(var(--spacing) * 72);
}

.h-80 {
  height: calc(var(--spacing) * 80);
}

.h-\[1\.15rem\] {
  height: 1.15rem;
}

.h-\[250px\] {
  height: 250px;
}

.h-\[var\(--radix-select-trigger-height\)\] {
  height: var(--radix-select-trigger-height);
}

.h-auto {
  height: auto;
}

.h-full {
  height: 100%;
}

.h-max {
  height: max-content;
}

.h-px {
  height: 1px;
}

.h-screen {
  height: 100vh;
}

.h-svh {
  height: 100svh;
}

.max-h-\(--radix-dropdown-menu-content-available-height\) {
  max-height: var(--radix-dropdown-menu-content-available-height);
}

.max-h-\(--radix-select-content-available-height\) {
  max-height: var(--radix-select-content-available-height);
}

.max-h-1 {
  max-height: calc(var(--spacing) * 1);
}

.max-h-8 {
  max-height: calc(var(--spacing) * 8);
}

.max-h-60 {
  max-height: calc(var(--spacing) * 60);
}

.max-h-\[300px\] {
  max-height: 300px;
}

.max-h-\[400px\] {
  max-height: 400px;
}

.min-h-0 {
  min-height: calc(var(--spacing) * 0);
}

.min-h-8 {
  min-height: calc(var(--spacing) * 8);
}

.min-h-16 {
  min-height: calc(var(--spacing) * 16);
}

.min-h-\[24rem\] {
  min-height: 24rem;
}

.min-h-\[120px\] {
  min-height: 120px;
}

.min-h-\[400px\] {
  min-height: 400px;
}

.min-h-screen {
  min-height: 100vh;
}

.min-h-svh {
  min-height: 100svh;
}

.w-\(--sidebar-width\) {
  width: var(--sidebar-width);
}

.w-0 {
  width: calc(var(--spacing) * 0);
}

.w-1 {
  width: calc(var(--spacing) * 1);
}

.w-1\/2 {
  width: 50%;
}

.w-2 {
  width: calc(var(--spacing) * 2);
}

.w-2\.5 {
  width: calc(var(--spacing) * 2.5);
}

.w-2\/3 {
  width: 66.6667%;
}

.w-3\/4 {
  width: 75%;
}

.w-4 {
  width: calc(var(--spacing) * 4);
}

.w-5 {
  width: calc(var(--spacing) * 5);
}

.w-6 {
  width: calc(var(--spacing) * 6);
}

.w-8 {
  width: calc(var(--spacing) * 8);
}

.w-10 {
  width: calc(var(--spacing) * 10);
}

.w-12 {
  width: calc(var(--spacing) * 12);
}

.w-16 {
  width: calc(var(--spacing) * 16);
}

.w-20 {
  width: calc(var(--spacing) * 20);
}

.w-60 {
  width: calc(var(--spacing) * 60);
}

.w-72 {
  width: calc(var(--spacing) * 72);
}

.w-\[30rem\] {
  width: 30rem;
}

.w-\[160px\] {
  width: 160px;
}

.w-\[180px\] {
  width: 180px;
}

.w-\[300px\] {
  width: 300px;
}

.w-\[400px\] {
  width: 400px;
}

.w-auto {
  width: auto;
}

.w-fit {
  width: fit-content;
}

.w-full {
  width: 100%;
}

.w-max {
  width: max-content;
}

.w-min {
  width: min-content;
}

.w-screen {
  width: 100vw;
}

.max-w-\(--skeleton-width\) {
  max-width: var(--skeleton-width);
}

.max-w-3xl {
  max-width: var(--container-3xl);
}

.max-w-6xl {
  max-width: var(--container-6xl);
}

.max-w-7xl {
  max-width: var(--container-7xl);
}

.max-w-8 {
  max-width: calc(var(--spacing) * 8);
}

.max-w-96 {
  max-width: calc(var(--spacing) * 96);
}

.max-w-\[600px\] {
  max-width: 600px;
}

.max-w-\[1200px\] {
  max-width: 1200px;
}

.max-w-\[calc\(100\%-2rem\)\] {
  max-width: calc(100% - 2rem);
}

.max-w-lg {
  max-width: var(--container-lg);
}

.max-w-md {
  max-width: var(--container-md);
}

.max-w-sm {
  max-width: var(--container-sm);
}

.max-w-xl {
  max-width: var(--container-xl);
}

.min-w-0 {
  min-width: calc(var(--spacing) * 0);
}

.min-w-5 {
  min-width: calc(var(--spacing) * 5);
}

.min-w-8 {
  min-width: calc(var(--spacing) * 8);
}

.min-w-96 {
  min-width: calc(var(--spacing) * 96);
}

.min-w-\[8rem\] {
  min-width: 8rem;
}

.min-w-\[100px\] {
  min-width: 100px;
}

.min-w-\[var\(--radix-select-trigger-width\)\] {
  min-width: var(--radix-select-trigger-width);
}

.flex-1 {
  flex: 1;
}

.flex-shrink-0, .shrink-0 {
  flex-shrink: 0;
}

.flex-grow {
  flex-grow: 1;
}

.caption-bottom {
  caption-side: bottom;
}

.origin-\(--radix-dropdown-menu-content-transform-origin\) {
  transform-origin: var(--radix-dropdown-menu-content-transform-origin);
}

.origin-\(--radix-popover-content-transform-origin\) {
  transform-origin: var(--radix-popover-content-transform-origin);
}

.origin-\(--radix-select-content-transform-origin\) {
  transform-origin: var(--radix-select-content-transform-origin);
}

.origin-\(--radix-tooltip-content-transform-origin\) {
  transform-origin: var(--radix-tooltip-content-transform-origin);
}

.-translate-x-1\/2 {
  --tw-translate-x: calc(calc(1 / 2 * 100%) * -1);
  translate: var(--tw-translate-x) var(--tw-translate-y);
}

.-translate-x-px {
  --tw-translate-x: -1px;
  translate: var(--tw-translate-x) var(--tw-translate-y);
}

.translate-x-\[-50\%\] {
  --tw-translate-x: -50%;
  translate: var(--tw-translate-x) var(--tw-translate-y);
}

.translate-x-px {
  --tw-translate-x: 1px;
  translate: var(--tw-translate-x) var(--tw-translate-y);
}

.-translate-y-1\/2 {
  --tw-translate-y: calc(calc(1 / 2 * 100%) * -1);
  translate: var(--tw-translate-x) var(--tw-translate-y);
}

.translate-y-\[-50\%\] {
  --tw-translate-y: -50%;
  translate: var(--tw-translate-x) var(--tw-translate-y);
}

.translate-y-\[calc\(-50\%_-_2px\)\] {
  --tw-translate-y: calc(-50% - 2px);
  translate: var(--tw-translate-x) var(--tw-translate-y);
}

.rotate-45 {
  rotate: 45deg;
}

.rotate-180 {
  rotate: 180deg;
}

.transform {
  transform: var(--tw-rotate-x, ) var(--tw-rotate-y, ) var(--tw-rotate-z, ) var(--tw-skew-x, ) var(--tw-skew-y, );
}

.animate-in {
  animation: enter var(--tw-animation-duration, var(--tw-duration, .15s)) var(--tw-ease, ease) var(--tw-animation-delay, 0s) var(--tw-animation-iteration-count, 1) var(--tw-animation-direction, normal) var(--tw-animation-fill-mode, none);
}

.animate-pulse {
  animation: var(--animate-pulse);
}

.animate-spin {
  animation: var(--animate-spin);
}

.cursor-default {
  cursor: default;
}

.cursor-pointer {
  cursor: pointer;
}

.cursor-text {
  cursor: text;
}

.touch-none {
  touch-action: none;
}

.scroll-my-1 {
  scroll-margin-block: calc(var(--spacing) * 1);
}

.scroll-py-1 {
  scroll-padding-block: calc(var(--spacing) * 1);
}

.list-disc {
  list-style-type: disc;
}

.auto-rows-min {
  grid-auto-rows: min-content;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-rows-\[auto_auto\] {
  grid-template-rows: auto auto;
}

.flex-col {
  flex-direction: column;
}

.flex-col-reverse {
  flex-direction: column-reverse;
}

.flex-row {
  flex-direction: row;
}

.flex-wrap {
  flex-wrap: wrap;
}

.items-center {
  align-items: center;
}

.items-end {
  align-items: flex-end;
}

.items-start {
  align-items: flex-start;
}

.items-stretch {
  align-items: stretch;
}

.justify-between {
  justify-content: space-between;
}

.justify-center {
  justify-content: center;
}

.justify-end {
  justify-content: flex-end;
}

.gap-1 {
  gap: calc(var(--spacing) * 1);
}

.gap-1\.5 {
  gap: calc(var(--spacing) * 1.5);
}

.gap-2 {
  gap: calc(var(--spacing) * 2);
}

.gap-3 {
  gap: calc(var(--spacing) * 3);
}

.gap-4 {
  gap: calc(var(--spacing) * 4);
}

.gap-6 {
  gap: calc(var(--spacing) * 6);
}

.gap-8 {
  gap: calc(var(--spacing) * 8);
}

:where(.space-y-0 > :not(:last-child)) {
  --tw-space-y-reverse: 0;
  margin-block-start: calc(calc(var(--spacing) * 0) * var(--tw-space-y-reverse));
  margin-block-end: calc(calc(var(--spacing) * 0) * calc(1 - var(--tw-space-y-reverse)));
}

:where(.space-y-1 > :not(:last-child)) {
  --tw-space-y-reverse: 0;
  margin-block-start: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));
  margin-block-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));
}

:where(.space-y-2 > :not(:last-child)) {
  --tw-space-y-reverse: 0;
  margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
  margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
}

:where(.space-y-3 > :not(:last-child)) {
  --tw-space-y-reverse: 0;
  margin-block-start: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse));
  margin-block-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse)));
}

:where(.space-y-4 > :not(:last-child)) {
  --tw-space-y-reverse: 0;
  margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));
  margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));
}

:where(.space-y-6 > :not(:last-child)) {
  --tw-space-y-reverse: 0;
  margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
  margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
}

.gap-x-1 {
  column-gap: calc(var(--spacing) * 1);
}

.gap-x-2 {
  column-gap: calc(var(--spacing) * 2);
}

:where(.space-x-2 > :not(:last-child)) {
  --tw-space-x-reverse: 0;
  margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
  margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
}

.self-start {
  align-self: flex-start;
}

.justify-self-end {
  justify-self: flex-end;
}

.overflow-auto {
  overflow: auto;
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-x-auto {
  overflow-x: auto;
}

.overflow-x-hidden {
  overflow-x: hidden;
}

.overflow-y-auto {
  overflow-y: auto;
}

.rounded {
  border-radius: .25rem;
}

.rounded-3xl {
  border-radius: var(--radius-3xl);
}

.rounded-\[2px\] {
  border-radius: 2px;
}

.rounded-\[inherit\] {
  border-radius: inherit;
}

.rounded-full {
  border-radius: 3.40282e38px;
}

.rounded-lg {
  border-radius: var(--radius);
}

.rounded-md {
  border-radius: calc(var(--radius)  - 2px);
}

.rounded-sm {
  border-radius: calc(var(--radius)  - 4px);
}

.rounded-xl {
  border-radius: calc(var(--radius)  + 4px);
}

.rounded-xs {
  border-radius: var(--radius-xs);
}

.rounded-s-lg {
  border-start-start-radius: var(--radius);
  border-end-start-radius: var(--radius);
}

.rounded-s-none {
  border-start-start-radius: 0;
  border-end-start-radius: 0;
}

.rounded-e-lg {
  border-start-end-radius: var(--radius);
  border-end-end-radius: var(--radius);
}

.rounded-e-none {
  border-start-end-radius: 0;
  border-end-end-radius: 0;
}

.rounded-t-3xl {
  border-top-left-radius: var(--radius-3xl);
  border-top-right-radius: var(--radius-3xl);
}

.rounded-t-lg {
  border-top-left-radius: var(--radius);
  border-top-right-radius: var(--radius);
}

.rounded-t-none {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.rounded-b-4xl {
  border-bottom-right-radius: var(--radius-4xl);
  border-bottom-left-radius: var(--radius-4xl);
}

.border {
  border-style: var(--tw-border-style);
  border-width: 1px;
}

.border-2 {
  border-style: var(--tw-border-style);
  border-width: 2px;
}

.border-4 {
  border-style: var(--tw-border-style);
  border-width: 4px;
}

.border-\[1\.5px\] {
  border-style: var(--tw-border-style);
  border-width: 1.5px;
}

.border-y {
  border-block-style: var(--tw-border-style);
  border-block-width: 1px;
}

.border-t {
  border-top-style: var(--tw-border-style);
  border-top-width: 1px;
}

.border-r {
  border-right-style: var(--tw-border-style);
  border-right-width: 1px;
}

.border-r-0 {
  border-right-style: var(--tw-border-style);
  border-right-width: 0;
}

.border-b {
  border-bottom-style: var(--tw-border-style);
  border-bottom-width: 1px;
}

.border-l {
  border-left-style: var(--tw-border-style);
  border-left-width: 1px;
}

.border-dashed {
  --tw-border-style: dashed;
  border-style: dashed;
}

.border-\(--color-border\) {
  border-color: var(--color-border);
}

.border-accent {
  border-color: var(--accent);
}

.border-border {
  border-color: var(--border);
}

.border-border\/50 {
  border-color: var(--border);
}

@supports (color: color-mix(in lab, red, red)) {
  .border-border\/50 {
    border-color: color-mix(in oklab, var(--border) 50%, transparent);
  }
}

.border-card\/40 {
  border-color: var(--card);
}

@supports (color: color-mix(in lab, red, red)) {
  .border-card\/40 {
    border-color: color-mix(in oklab, var(--card) 40%, transparent);
  }
}

.border-gray-300 {
  border-color: var(--color-gray-300);
}

.border-input {
  border-color: var(--input);
}

.border-primary {
  border-color: var(--primary);
}

.border-sidebar-border {
  border-color: var(--sidebar-border);
}

.border-transparent {
  border-color: #0000;
}

.border-white\/\[0\.08\] {
  border-color: #ffffff14;
}

@supports (color: color-mix(in lab, red, red)) {
  .border-white\/\[0\.08\] {
    border-color: color-mix(in oklab, var(--color-white) 8%, transparent);
  }
}

.border-white\/\[0\.15\] {
  border-color: #ffffff26;
}

@supports (color: color-mix(in lab, red, red)) {
  .border-white\/\[0\.15\] {
    border-color: color-mix(in oklab, var(--color-white) 15%, transparent);
  }
}

.border-zinc-600 {
  border-color: var(--color-zinc-600);
}

.border-zinc-700 {
  border-color: var(--color-zinc-700);
}

.border-t-transparent {
  border-top-color: #0000;
}

.border-l-transparent {
  border-left-color: #0000;
}

.bg-\(--color-bg\) {
  background-color: var(--color-bg);
}

.bg-accent {
  background-color: var(--accent);
}

.bg-background {
  background-color: var(--background);
}

.bg-black {
  background-color: var(--color-black);
}

.bg-black\/50 {
  background-color: #00000080;
}

@supports (color: color-mix(in lab, red, red)) {
  .bg-black\/50 {
    background-color: color-mix(in oklab, var(--color-black) 50%, transparent);
  }
}

.bg-blue-600 {
  background-color: var(--color-blue-600);
}

.bg-border {
  background-color: var(--border);
}

.bg-card {
  background-color: var(--card);
}

.bg-card\/50 {
  background-color: var(--card);
}

@supports (color: color-mix(in lab, red, red)) {
  .bg-card\/50 {
    background-color: color-mix(in oklab, var(--card) 50%, transparent);
  }
}

.bg-destructive {
  background-color: var(--destructive);
}

.bg-destructive\/15 {
  background-color: var(--destructive);
}

@supports (color: color-mix(in lab, red, red)) {
  .bg-destructive\/15 {
    background-color: color-mix(in oklab, var(--destructive) 15%, transparent);
  }
}

.bg-emerald-500 {
  background-color: var(--color-emerald-500);
}

.bg-emerald-800 {
  background-color: var(--color-emerald-800);
}

.bg-emerald-900 {
  background-color: var(--color-emerald-900);
}

.bg-foreground\/20 {
  background-color: var(--foreground);
}

@supports (color: color-mix(in lab, red, red)) {
  .bg-foreground\/20 {
    background-color: color-mix(in oklab, var(--foreground) 20%, transparent);
  }
}

.bg-gray-700 {
  background-color: var(--color-gray-700);
}

.bg-green-100 {
  background-color: var(--color-green-100);
}

.bg-input {
  background-color: var(--input);
}

.bg-muted {
  background-color: var(--muted);
}

.bg-muted-foreground {
  background-color: var(--muted-foreground);
}

.bg-muted\/50 {
  background-color: var(--muted);
}

@supports (color: color-mix(in lab, red, red)) {
  .bg-muted\/50 {
    background-color: color-mix(in oklab, var(--muted) 50%, transparent);
  }
}

.bg-neutral-900 {
  background-color: var(--color-neutral-900);
}

.bg-popover {
  background-color: var(--popover);
}

.bg-primary {
  background-color: var(--primary);
}

.bg-primary-foreground {
  background-color: var(--primary-foreground);
}

.bg-primary\/10 {
  background-color: var(--primary);
}

@supports (color: color-mix(in lab, red, red)) {
  .bg-primary\/10 {
    background-color: color-mix(in oklab, var(--primary) 10%, transparent);
  }
}

.bg-primary\/20 {
  background-color: var(--primary);
}

@supports (color: color-mix(in lab, red, red)) {
  .bg-primary\/20 {
    background-color: color-mix(in oklab, var(--primary) 20%, transparent);
  }
}

.bg-red-900\/50 {
  background-color: #82181a80;
}

@supports (color: color-mix(in lab, red, red)) {
  .bg-red-900\/50 {
    background-color: color-mix(in oklab, var(--color-red-900) 50%, transparent);
  }
}

.bg-secondary {
  background-color: var(--secondary);
}

.bg-sidebar {
  background-color: var(--sidebar);
}

.bg-sidebar-border {
  background-color: var(--sidebar-border);
}

.bg-transparent {
  background-color: #0000;
}

.bg-white\/\[0\.03\] {
  background-color: #ffffff08;
}

@supports (color: color-mix(in lab, red, red)) {
  .bg-white\/\[0\.03\] {
    background-color: color-mix(in oklab, var(--color-white) 3%, transparent);
  }
}

.bg-yellow-500 {
  background-color: var(--color-yellow-500);
}

.bg-zinc-600 {
  background-color: var(--color-zinc-600);
}

.bg-zinc-700 {
  background-color: var(--color-zinc-700);
}

.bg-zinc-800 {
  background-color: var(--color-zinc-800);
}

.bg-gradient-to-b {
  --tw-gradient-position: to bottom in oklab;
  background-image: linear-gradient(var(--tw-gradient-stops));
}

.bg-gradient-to-br {
  --tw-gradient-position: to bottom right in oklab;
  background-image: linear-gradient(var(--tw-gradient-stops));
}

.bg-gradient-to-r {
  --tw-gradient-position: to right in oklab;
  background-image: linear-gradient(var(--tw-gradient-stops));
}

.bg-gradient-to-t {
  --tw-gradient-position: to top in oklab;
  background-image: linear-gradient(var(--tw-gradient-stops));
}

.bg-gradient-to-tr {
  --tw-gradient-position: to top right in oklab;
  background-image: linear-gradient(var(--tw-gradient-stops));
}

.from-\[\#030303\] {
  --tw-gradient-from: #030303;
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}

.from-amber-400 {
  --tw-gradient-from: var(--color-amber-400);
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}

.from-amber-500\/\[0\.15\] {
  --tw-gradient-from: #f99c0026;
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}

@supports (color: color-mix(in lab, red, red)) {
  .from-amber-500\/\[0\.15\] {
    --tw-gradient-from: color-mix(in oklab, var(--color-amber-500) 15%, transparent);
  }
}

.from-background {
  --tw-gradient-from: var(--background);
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}

.from-cyan-500\/\[0\.15\] {
  --tw-gradient-from: #00b7d726;
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}

@supports (color: color-mix(in lab, red, red)) {
  .from-cyan-500\/\[0\.15\] {
    --tw-gradient-from: color-mix(in oklab, var(--color-cyan-500) 15%, transparent);
  }
}

.from-indigo-500\/\[0\.05\] {
  --tw-gradient-from: #625fff0d;
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}

@supports (color: color-mix(in lab, red, red)) {
  .from-indigo-500\/\[0\.05\] {
    --tw-gradient-from: color-mix(in oklab, var(--color-indigo-500) 5%, transparent);
  }
}

.from-indigo-500\/\[0\.15\] {
  --tw-gradient-from: #625fff26;
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}

@supports (color: color-mix(in lab, red, red)) {
  .from-indigo-500\/\[0\.15\] {
    --tw-gradient-from: color-mix(in oklab, var(--color-indigo-500) 15%, transparent);
  }
}

.from-primary {
  --tw-gradient-from: var(--primary);
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}

.from-rose-500\/\[0\.15\] {
  --tw-gradient-from: #ff235726;
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}

@supports (color: color-mix(in lab, red, red)) {
  .from-rose-500\/\[0\.15\] {
    --tw-gradient-from: color-mix(in oklab, var(--color-rose-500) 15%, transparent);
  }
}

.from-violet-500\/\[0\.15\] {
  --tw-gradient-from: #8d54ff26;
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}

@supports (color: color-mix(in lab, red, red)) {
  .from-violet-500\/\[0\.15\] {
    --tw-gradient-from: color-mix(in oklab, var(--color-violet-500) 15%, transparent);
  }
}

.from-white {
  --tw-gradient-from: var(--color-white);
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}

.from-white\/\[0\.08\] {
  --tw-gradient-from: #ffffff14;
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}

@supports (color: color-mix(in lab, red, red)) {
  .from-white\/\[0\.08\] {
    --tw-gradient-from: color-mix(in oklab, var(--color-white) 8%, transparent);
  }
}

.via-transparent {
  --tw-gradient-via: transparent;
  --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-via-stops);
}

.via-white\/90 {
  --tw-gradient-via: #ffffffe6;
  --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-via-stops);
}

@supports (color: color-mix(in lab, red, red)) {
  .via-white\/90 {
    --tw-gradient-via: color-mix(in oklab, var(--color-white) 90%, transparent);
  }
}

.to-\[\#030303\]\/80 {
  --tw-gradient-to: oklab(9.69258% 1.11759e-8 0 / .8);
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}

.to-amber-600 {
  --tw-gradient-to: var(--color-amber-600);
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}

.to-primary {
  --tw-gradient-to: var(--primary);
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}

.to-primary\/30 {
  --tw-gradient-to: var(--primary);
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}

@supports (color: color-mix(in lab, red, red)) {
  .to-primary\/30 {
    --tw-gradient-to: color-mix(in oklab, var(--primary) 30%, transparent);
  }
}

.to-rose-500\/\[0\.05\] {
  --tw-gradient-to: #ff23570d;
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}

@supports (color: color-mix(in lab, red, red)) {
  .to-rose-500\/\[0\.05\] {
    --tw-gradient-to: color-mix(in oklab, var(--color-rose-500) 5%, transparent);
  }
}

.to-transparent {
  --tw-gradient-to: transparent;
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}

.to-white\/80 {
  --tw-gradient-to: #fffc;
  --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
}

@supports (color: color-mix(in lab, red, red)) {
  .to-white\/80 {
    --tw-gradient-to: color-mix(in oklab, var(--color-white) 80%, transparent);
  }
}

.bg-\[length\:250\%_100\%\,auto\] {
  background-size: 250% 100%, auto;
}

.bg-clip-text {
  background-clip: text;
}

.\[background-repeat\:no-repeat\,padding-box\] {
  background-repeat: no-repeat, padding-box;
}

.fill-current {
  fill: currentColor;
}

.fill-primary {
  fill: var(--primary);
}

.object-cover {
  object-fit: cover;
}

.p-0 {
  padding: calc(var(--spacing) * 0);
}

.p-0\.5 {
  padding: calc(var(--spacing) * .5);
}

.p-1 {
  padding: calc(var(--spacing) * 1);
}

.p-2 {
  padding: calc(var(--spacing) * 2);
}

.p-3 {
  padding: calc(var(--spacing) * 3);
}

.p-4 {
  padding: calc(var(--spacing) * 4);
}

.p-6 {
  padding: calc(var(--spacing) * 6);
}

.p-8 {
  padding: calc(var(--spacing) * 8);
}

.p-\[4px\] {
  padding: 4px;
}

.p-px {
  padding: 1px;
}

.px-1 {
  padding-inline: calc(var(--spacing) * 1);
}

.px-2 {
  padding-inline: calc(var(--spacing) * 2);
}

.px-2\.5 {
  padding-inline: calc(var(--spacing) * 2.5);
}

.px-3 {
  padding-inline: calc(var(--spacing) * 3);
}

.px-4 {
  padding-inline: calc(var(--spacing) * 4);
}

.px-6 {
  padding-inline: calc(var(--spacing) * 6);
}

.py-0 {
  padding-block: calc(var(--spacing) * 0);
}

.py-0\.5 {
  padding-block: calc(var(--spacing) * .5);
}

.py-1 {
  padding-block: calc(var(--spacing) * 1);
}

.py-1\.5 {
  padding-block: calc(var(--spacing) * 1.5);
}

.py-2 {
  padding-block: calc(var(--spacing) * 2);
}

.py-3 {
  padding-block: calc(var(--spacing) * 3);
}

.py-4 {
  padding-block: calc(var(--spacing) * 4);
}

.py-5 {
  padding-block: calc(var(--spacing) * 5);
}

.py-6 {
  padding-block: calc(var(--spacing) * 6);
}

.py-16 {
  padding-block: calc(var(--spacing) * 16);
}

.pt-0 {
  padding-top: calc(var(--spacing) * 0);
}

.pt-1 {
  padding-top: calc(var(--spacing) * 1);
}

.pt-3 {
  padding-top: calc(var(--spacing) * 3);
}

.pt-4 {
  padding-top: calc(var(--spacing) * 4);
}

.pt-10 {
  padding-top: calc(var(--spacing) * 10);
}

.pr-2 {
  padding-right: calc(var(--spacing) * 2);
}

.pr-4 {
  padding-right: calc(var(--spacing) * 4);
}

.pr-8 {
  padding-right: calc(var(--spacing) * 8);
}

.pb-3 {
  padding-bottom: calc(var(--spacing) * 3);
}

.pl-2 {
  padding-left: calc(var(--spacing) * 2);
}

.pl-5 {
  padding-left: calc(var(--spacing) * 5);
}

.pl-8 {
  padding-left: calc(var(--spacing) * 8);
}

.pl-10 {
  padding-left: calc(var(--spacing) * 10);
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.align-middle {
  vertical-align: middle;
}

.font-mono {
  font-family: var(--font-mono);
}

.text-2xl {
  font-size: var(--text-2xl);
  line-height: var(--tw-leading, var(--text-2xl--line-height));
}

.text-3xl {
  font-size: var(--text-3xl);
  line-height: var(--tw-leading, var(--text-3xl--line-height));
}

.text-4xl {
  font-size: var(--text-4xl);
  line-height: var(--tw-leading, var(--text-4xl--line-height));
}

.text-base {
  font-size: var(--text-base);
  line-height: var(--tw-leading, var(--text-base--line-height));
}

.text-lg {
  font-size: var(--text-lg);
  line-height: var(--tw-leading, var(--text-lg--line-height));
}

.text-sm {
  font-size: var(--text-sm);
  line-height: var(--tw-leading, var(--text-sm--line-height));
}

.text-xl {
  font-size: var(--text-xl);
  line-height: var(--tw-leading, var(--text-xl--line-height));
}

.text-xs {
  font-size: var(--text-xs);
  line-height: var(--tw-leading, var(--text-xs--line-height));
}

.text-\[8px\] {
  font-size: 8px;
}

.leading-none {
  --tw-leading: 1;
  line-height: 1;
}

.leading-relaxed {
  --tw-leading: var(--leading-relaxed);
  line-height: var(--leading-relaxed);
}

.font-bold {
  --tw-font-weight: var(--font-weight-bold);
  font-weight: var(--font-weight-bold);
}

.font-light {
  --tw-font-weight: var(--font-weight-light);
  font-weight: var(--font-weight-light);
}

.font-medium {
  --tw-font-weight: var(--font-weight-medium);
  font-weight: var(--font-weight-medium);
}

.font-semibold {
  --tw-font-weight: var(--font-weight-semibold);
  font-weight: var(--font-weight-semibold);
}

.tracking-tight {
  --tw-tracking: var(--tracking-tight);
  letter-spacing: var(--tracking-tight);
}

.tracking-wide {
  --tw-tracking: var(--tracking-wide);
  letter-spacing: var(--tracking-wide);
}

.tracking-widest {
  --tw-tracking: var(--tracking-widest);
  letter-spacing: var(--tracking-widest);
}

.text-balance {
  text-wrap: balance;
}

.whitespace-nowrap {
  white-space: nowrap;
}

.text-accent-foreground {
  color: var(--accent-foreground);
}

.text-amber-700 {
  color: var(--color-amber-700);
}

.text-blue-400 {
  color: var(--color-blue-400);
}

.text-blue-600 {
  color: var(--color-blue-600);
}

.text-card-foreground {
  color: var(--card-foreground);
}

.text-destructive {
  color: var(--destructive);
}

.text-emerald-400 {
  color: var(--color-emerald-400);
}

.text-emerald-500 {
  color: var(--color-emerald-500);
}

.text-foreground {
  color: var(--foreground);
}

.text-foreground\/50 {
  color: var(--foreground);
}

@supports (color: color-mix(in lab, red, red)) {
  .text-foreground\/50 {
    color: color-mix(in oklab, var(--foreground) 50%, transparent);
  }
}

.text-green-500 {
  color: var(--color-green-500);
}

.text-green-600 {
  color: var(--color-green-600);
}

.text-muted-foreground {
  color: var(--muted-foreground);
}

.text-muted-foreground\/75 {
  color: var(--muted-foreground);
}

@supports (color: color-mix(in lab, red, red)) {
  .text-muted-foreground\/75 {
    color: color-mix(in oklab, var(--muted-foreground) 75%, transparent);
  }
}

.text-neutral-500 {
  color: var(--color-neutral-500);
}

.text-popover-foreground {
  color: var(--popover-foreground);
}

.text-primary {
  color: var(--primary);
}

.text-primary-foreground {
  color: var(--primary-foreground);
}

.text-red-200 {
  color: var(--color-red-200);
}

.text-red-300 {
  color: var(--color-red-300);
}

.text-red-400 {
  color: var(--color-red-400);
}

.text-red-500 {
  color: var(--color-red-500);
}

.text-secondary-foreground {
  color: var(--secondary-foreground);
}

.text-sidebar-foreground {
  color: var(--sidebar-foreground);
}

.text-sidebar-foreground\/70 {
  color: var(--sidebar-foreground);
}

@supports (color: color-mix(in lab, red, red)) {
  .text-sidebar-foreground\/70 {
    color: color-mix(in oklab, var(--sidebar-foreground) 70%, transparent);
  }
}

.text-stone-500 {
  color: var(--color-stone-500);
}

.text-transparent {
  color: #0000;
}

.text-white {
  color: var(--color-white);
}

.text-white\/60 {
  color: #fff9;
}

@supports (color: color-mix(in lab, red, red)) {
  .text-white\/60 {
    color: color-mix(in oklab, var(--color-white) 60%, transparent);
  }
}

.text-white\/70 {
  color: #ffffffb3;
}

@supports (color: color-mix(in lab, red, red)) {
  .text-white\/70 {
    color: color-mix(in oklab, var(--color-white) 70%, transparent);
  }
}

.text-yellow-600 {
  color: var(--color-yellow-600);
}

.text-zinc-300 {
  color: var(--color-zinc-300);
}

.text-zinc-400 {
  color: var(--color-zinc-400);
}

.text-zinc-500 {
  color: var(--color-zinc-500);
}

.capitalize {
  text-transform: capitalize;
}

.tabular-nums {
  --tw-numeric-spacing: tabular-nums;
  font-variant-numeric: var(--tw-ordinal, ) var(--tw-slashed-zero, ) var(--tw-numeric-figure, ) var(--tw-numeric-spacing, ) var(--tw-numeric-fraction, );
}

.underline {
  text-decoration-line: underline;
}

.underline-offset-4 {
  text-underline-offset: 4px;
}

.antialiased {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.opacity-0 {
  opacity: 0;
}

.opacity-50 {
  opacity: .5;
}

.opacity-70 {
  opacity: .7;
}

.opacity-75 {
  opacity: .75;
}

.opacity-100 {
  opacity: 1;
}

.shadow-\[0_0_0_1px_hsl\(var\(--sidebar-border\)\)\] {
  --tw-shadow: 0 0 0 1px var(--tw-shadow-color, hsl(var(--sidebar-border)));
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}

.shadow-\[0_8px_32px_0_rgba\(255\,255\,255\,0\.1\)\] {
  --tw-shadow: 0 8px 32px 0 var(--tw-shadow-color, #ffffff1a);
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}

.shadow-lg {
  --tw-shadow: var(--shadow-lg);
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}

.shadow-md {
  --tw-shadow: var(--shadow-md);
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}

.shadow-none {
  --tw-shadow: 0 0 #0000;
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}

.shadow-sm {
  --tw-shadow: var(--shadow-sm);
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}

.shadow-xl {
  --tw-shadow: var(--shadow-xl);
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}

.shadow-xs {
  --tw-shadow: var(--shadow-xs);
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}

.ring {
  --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}

.ring-0 {
  --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}

.ring-1 {
  --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}

.ring-blue-300 {
  --tw-ring-color: var(--color-blue-300);
}

.ring-border {
  --tw-ring-color: var(--border);
}

.ring-card-foreground\/10 {
  --tw-ring-color: var(--card-foreground);
}

@supports (color: color-mix(in lab, red, red)) {
  .ring-card-foreground\/10 {
    --tw-ring-color: color-mix(in oklab, var(--card-foreground) 10%, transparent);
  }
}

.ring-emerald-600 {
  --tw-ring-color: var(--color-emerald-600);
}

.ring-gray-300 {
  --tw-ring-color: var(--color-gray-300);
}

.ring-green-300 {
  --tw-ring-color: var(--color-green-300);
}

.ring-input {
  --tw-ring-color: var(--input);
}

.ring-muted {
  --tw-ring-color: var(--muted);
}

.ring-primary {
  --tw-ring-color: var(--primary);
}

.ring-primary\/40 {
  --tw-ring-color: var(--primary);
}

@supports (color: color-mix(in lab, red, red)) {
  .ring-primary\/40 {
    --tw-ring-color: color-mix(in oklab, var(--primary) 40%, transparent);
  }
}

.ring-primary\/50 {
  --tw-ring-color: var(--primary);
}

@supports (color: color-mix(in lab, red, red)) {
  .ring-primary\/50 {
    --tw-ring-color: color-mix(in oklab, var(--primary) 50%, transparent);
  }
}

.ring-sidebar-ring {
  --tw-ring-color: var(--sidebar-ring);
}

.ring-yellow-300 {
  --tw-ring-color: var(--color-yellow-300);
}

.ring-offset-background {
  --tw-ring-offset-color: var(--background);
}

.outline-hidden {
  --tw-outline-style: none;
  outline-style: none;
}

@media (forced-colors: active) {
  .outline-hidden {
    outline-offset: 2px;
    outline: 2px solid #0000;
  }
}

.outline {
  outline-style: var(--tw-outline-style);
  outline-width: 1px;
}

.blur-3xl {
  --tw-blur: blur(var(--blur-3xl));
  filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
}

.filter {
  filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
}

.backdrop-blur-\[2px\] {
  --tw-backdrop-blur: blur(2px);
  backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
}

.transition {
  transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;
  transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
  transition-duration: var(--tw-duration, var(--default-transition-duration));
}

.transition-\[color\,box-shadow\] {
  transition-property: color, box-shadow;
  transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
  transition-duration: var(--tw-duration, var(--default-transition-duration));
}

.transition-\[left\,right\,width\] {
  transition-property: left, right, width;
  transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
  transition-duration: var(--tw-duration, var(--default-transition-duration));
}

.transition-\[margin\,opacity\] {
  transition-property: margin, opacity;
  transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
  transition-duration: var(--tw-duration, var(--default-transition-duration));
}

.transition-\[width\,height\,padding\] {
  transition-property: width, height, padding;
  transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
  transition-duration: var(--tw-duration, var(--default-transition-duration));
}

.transition-\[width\] {
  transition-property: width;
  transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
  transition-duration: var(--tw-duration, var(--default-transition-duration));
}

.transition-all {
  transition-property: all;
  transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
  transition-duration: var(--tw-duration, var(--default-transition-duration));
}

.transition-colors {
  transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
  transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
  transition-duration: var(--tw-duration, var(--default-transition-duration));
}

.transition-opacity {
  transition-property: opacity;
  transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
  transition-duration: var(--tw-duration, var(--default-transition-duration));
}

.transition-transform {
  transition-property: transform, translate, scale, rotate;
  transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
  transition-duration: var(--tw-duration, var(--default-transition-duration));
}

.duration-200 {
  --tw-duration: .2s;
  transition-duration: .2s;
}

.duration-300 {
  --tw-duration: .3s;
  transition-duration: .3s;
}

.ease-in-out {
  --tw-ease: var(--ease-in-out);
  transition-timing-function: var(--ease-in-out);
}

.ease-linear {
  --tw-ease: linear;
  transition-timing-function: linear;
}

.fade-in-0 {
  --tw-enter-opacity: 0;
}

.outline-none {
  --tw-outline-style: none;
  outline-style: none;
}

.select-none {
  -webkit-user-select: none;
  user-select: none;
}

.zoom-in-95 {
  --tw-enter-scale: .95;
}

.\[--base-color\:\#a1a1aa\] {
  --base-color: #a1a1aa;
}

.\[--base-gradient-color\:\#000\] {
  --base-gradient-color: #000;
}

.\[--bg\:linear-gradient\(90deg\,\#0000_calc\(50\%-var\(--spread\)\)\,var\(--base-gradient-color\)\,\#0000_calc\(50\%\+var\(--spread\)\)\)\] {
  --bg: linear-gradient(90deg, #0000 calc(50% - var(--spread)), var(--base-gradient-color), #0000 calc(50% + var(--spread)));
}

.group-peer-data-\[state\=checked\]\:hidden:is(:where(.group):is(:where(.peer)[data-state="checked"] ~ *) *) {
  display: none;
}

.group-peer-data-\[state\=unchecked\]\:hidden:is(:where(.group):is(:where(.peer)[data-state="unchecked"] ~ *) *) {
  display: none;
}

.group-focus-within\/menu-item\:opacity-100:is(:where(.group\/menu-item):focus-within *) {
  opacity: 1;
}

@media (hover: hover) {
  .group-hover\/menu-item\:opacity-100:is(:where(.group\/menu-item):hover *) {
    opacity: 1;
  }
}

.group-has-data-\[sidebar\=menu-action\]\/menu-item\:pr-8:is(:where(.group\/menu-item):has([data-sidebar="menu-action"]) *) {
  padding-right: calc(var(--spacing) * 8);
}

.group-data-\[collapsible\=icon\]\:-mt-8:is(:where(.group)[data-collapsible="icon"] *) {
  margin-top: calc(var(--spacing) * -8);
}

.group-data-\[collapsible\=icon\]\:hidden:is(:where(.group)[data-collapsible="icon"] *) {
  display: none;
}

.group-data-\[collapsible\=icon\]\:size-8\!:is(:where(.group)[data-collapsible="icon"] *) {
  width: calc(var(--spacing) * 8) !important;
  height: calc(var(--spacing) * 8) !important;
}

.group-data-\[collapsible\=icon\]\:w-\(--sidebar-width-icon\):is(:where(.group)[data-collapsible="icon"] *) {
  width: var(--sidebar-width-icon);
}

.group-data-\[collapsible\=icon\]\:w-\[calc\(var\(--sidebar-width-icon\)\+\(--spacing\(4\)\)\)\]:is(:where(.group)[data-collapsible="icon"] *) {
  width: calc(var(--sidebar-width-icon)  + (calc(var(--spacing) * 4)));
}

.group-data-\[collapsible\=icon\]\:w-\[calc\(var\(--sidebar-width-icon\)\+\(--spacing\(4\)\)\+2px\)\]:is(:where(.group)[data-collapsible="icon"] *) {
  width: calc(var(--sidebar-width-icon)  + (calc(var(--spacing) * 4))  + 2px);
}

.group-data-\[collapsible\=icon\]\:overflow-hidden:is(:where(.group)[data-collapsible="icon"] *) {
  overflow: hidden;
}

.group-data-\[collapsible\=icon\]\:p-0\!:is(:where(.group)[data-collapsible="icon"] *) {
  padding: calc(var(--spacing) * 0) !important;
}

.group-data-\[collapsible\=icon\]\:p-2\!:is(:where(.group)[data-collapsible="icon"] *) {
  padding: calc(var(--spacing) * 2) !important;
}

.group-data-\[collapsible\=icon\]\:opacity-0:is(:where(.group)[data-collapsible="icon"] *) {
  opacity: 0;
}

.group-data-\[collapsible\=offcanvas\]\:right-\[calc\(var\(--sidebar-width\)\*-1\)\]:is(:where(.group)[data-collapsible="offcanvas"] *) {
  right: calc(var(--sidebar-width) * -1);
}

.group-data-\[collapsible\=offcanvas\]\:left-\[calc\(var\(--sidebar-width\)\*-1\)\]:is(:where(.group)[data-collapsible="offcanvas"] *) {
  left: calc(var(--sidebar-width) * -1);
}

.group-data-\[collapsible\=offcanvas\]\:w-0:is(:where(.group)[data-collapsible="offcanvas"] *) {
  width: calc(var(--spacing) * 0);
}

.group-data-\[collapsible\=offcanvas\]\:translate-x-0:is(:where(.group)[data-collapsible="offcanvas"] *) {
  --tw-translate-x: calc(var(--spacing) * 0);
  translate: var(--tw-translate-x) var(--tw-translate-y);
}

.group-data-\[disabled\=true\]\:pointer-events-none:is(:where(.group)[data-disabled="true"] *) {
  pointer-events: none;
}

.group-data-\[disabled\=true\]\:opacity-50:is(:where(.group)[data-disabled="true"] *) {
  opacity: .5;
}

.group-data-\[side\=left\]\:-right-4:is(:where(.group)[data-side="left"] *) {
  right: calc(var(--spacing) * -4);
}

.group-data-\[side\=left\]\:border-r:is(:where(.group)[data-side="left"] *) {
  border-right-style: var(--tw-border-style);
  border-right-width: 1px;
}

.group-data-\[side\=right\]\:left-0:is(:where(.group)[data-side="right"] *) {
  left: calc(var(--spacing) * 0);
}

.group-data-\[side\=right\]\:rotate-180:is(:where(.group)[data-side="right"] *) {
  rotate: 180deg;
}

.group-data-\[side\=right\]\:border-l:is(:where(.group)[data-side="right"] *) {
  border-left-style: var(--tw-border-style);
  border-left-width: 1px;
}

.group-data-\[variant\=floating\]\:rounded-lg:is(:where(.group)[data-variant="floating"] *) {
  border-radius: var(--radius);
}

.group-data-\[variant\=floating\]\:border:is(:where(.group)[data-variant="floating"] *) {
  border-style: var(--tw-border-style);
  border-width: 1px;
}

.group-data-\[variant\=floating\]\:border-sidebar-border:is(:where(.group)[data-variant="floating"] *) {
  border-color: var(--sidebar-border);
}

.group-data-\[variant\=floating\]\:shadow-sm:is(:where(.group)[data-variant="floating"] *) {
  --tw-shadow: var(--shadow-sm);
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}

@media (hover: hover) {
  .peer-hover\/menu-button\:text-sidebar-accent-foreground:is(:where(.peer\/menu-button):hover ~ *) {
    color: var(--sidebar-accent-foreground);
  }
}

.peer-focus-visible\:ring-\[3px\]:is(:where(.peer):focus-visible ~ *) {
  --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}

.peer-focus-visible\:ring-ring\/50:is(:where(.peer):focus-visible ~ *) {
  --tw-ring-color: var(--ring);
}

@supports (color: color-mix(in lab, red, red)) {
  .peer-focus-visible\:ring-ring\/50:is(:where(.peer):focus-visible ~ *) {
    --tw-ring-color: color-mix(in oklab, var(--ring) 50%, transparent);
  }
}

.peer-disabled\:cursor-not-allowed:is(:where(.peer):disabled ~ *) {
  cursor: not-allowed;
}

.peer-disabled\:opacity-50:is(:where(.peer):disabled ~ *) {
  opacity: .5;
}

.peer-data-disabled\:cursor-not-allowed:is(:where(.peer)[data-disabled] ~ *) {
  cursor: not-allowed;
}

.peer-data-disabled\:opacity-50:is(:where(.peer)[data-disabled] ~ *) {
  opacity: .5;
}

.peer-data-\[active\=true\]\/menu-button\:text-sidebar-accent-foreground:is(:where(.peer\/menu-button)[data-active="true"] ~ *) {
  color: var(--sidebar-accent-foreground);
}

.peer-data-\[size\=default\]\/menu-button\:top-1\.5:is(:where(.peer\/menu-button)[data-size="default"] ~ *) {
  top: calc(var(--spacing) * 1.5);
}

.peer-data-\[size\=lg\]\/menu-button\:top-2\.5:is(:where(.peer\/menu-button)[data-size="lg"] ~ *) {
  top: calc(var(--spacing) * 2.5);
}

.peer-data-\[size\=sm\]\/menu-button\:top-1:is(:where(.peer\/menu-button)[data-size="sm"] ~ *) {
  top: calc(var(--spacing) * 1);
}

.peer-data-\[state\=checked\]\:border-primary:is(:where(.peer)[data-state="checked"] ~ *) {
  border-color: var(--primary);
}

.peer-data-\[state\=checked\]\:bg-accent:is(:where(.peer)[data-state="checked"] ~ *) {
  background-color: var(--accent);
}

.peer-data-\[state\=unchecked\]\:text-muted-foreground\/70:is(:where(.peer)[data-state="unchecked"] ~ *) {
  color: var(--muted-foreground);
}

@supports (color: color-mix(in lab, red, red)) {
  .peer-data-\[state\=unchecked\]\:text-muted-foreground\/70:is(:where(.peer)[data-state="unchecked"] ~ *) {
    color: color-mix(in oklab, var(--muted-foreground) 70%, transparent);
  }
}

.selection\:bg-primary ::selection, .selection\:bg-primary::selection {
  background-color: var(--primary);
}

.selection\:text-primary-foreground ::selection, .selection\:text-primary-foreground::selection {
  color: var(--primary-foreground);
}

.file\:inline-flex::file-selector-button {
  display: inline-flex;
}

.file\:h-7::file-selector-button {
  height: calc(var(--spacing) * 7);
}

.file\:border-0::file-selector-button {
  border-style: var(--tw-border-style);
  border-width: 0;
}

.file\:bg-transparent::file-selector-button {
  background-color: #0000;
}

.file\:text-sm::file-selector-button {
  font-size: var(--text-sm);
  line-height: var(--tw-leading, var(--text-sm--line-height));
}

.file\:font-medium::file-selector-button {
  --tw-font-weight: var(--font-weight-medium);
  font-weight: var(--font-weight-medium);
}

.file\:text-foreground::file-selector-button {
  color: var(--foreground);
}

.placeholder\:text-muted-foreground::placeholder {
  color: var(--muted-foreground);
}

.after\:absolute:after {
  content: var(--tw-content);
  position: absolute;
}

.after\:-inset-2:after {
  content: var(--tw-content);
  inset: calc(var(--spacing) * -2);
}

.after\:inset-0:after {
  content: var(--tw-content);
  inset: calc(var(--spacing) * 0);
}

.after\:inset-y-0:after {
  content: var(--tw-content);
  inset-block: calc(var(--spacing) * 0);
}

.after\:left-1\/2:after {
  content: var(--tw-content);
  left: 50%;
}

.after\:w-\[2px\]:after {
  content: var(--tw-content);
  width: 2px;
}

.after\:rounded-full:after {
  content: var(--tw-content);
  border-radius: 3.40282e38px;
}

.after\:bg-\[radial-gradient\(circle_at_50\%_50\%\,rgba\(255\,255\,255\,0\.2\)\,transparent_70\%\)\]:after {
  content: var(--tw-content);
  background-image: radial-gradient(circle, #fff3, #0000 70%);
}

.group-data-\[collapsible\=offcanvas\]\:after\:left-full:is(:where(.group)[data-collapsible="offcanvas"] *):after {
  content: var(--tw-content);
  left: 100%;
}

@media (hover: hover) {
  .hover\:border-zinc-600:hover {
    border-color: var(--color-zinc-600);
  }
}

@media (hover: hover) {
  .hover\:bg-accent:hover {
    background-color: var(--accent);
  }
}

@media (hover: hover) {
  .hover\:bg-blue-700:hover {
    background-color: var(--color-blue-700);
  }
}

@media (hover: hover) {
  .hover\:bg-destructive\/90:hover {
    background-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .hover\:bg-destructive\/90:hover {
      background-color: color-mix(in oklab, var(--destructive) 90%, transparent);
    }
  }
}

@media (hover: hover) {
  .hover\:bg-emerald-700:hover {
    background-color: var(--color-emerald-700);
  }
}

@media (hover: hover) {
  .hover\:bg-emerald-800:hover {
    background-color: var(--color-emerald-800);
  }
}

@media (hover: hover) {
  .hover\:bg-muted\/50:hover {
    background-color: var(--muted);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .hover\:bg-muted\/50:hover {
      background-color: color-mix(in oklab, var(--muted) 50%, transparent);
    }
  }
}

@media (hover: hover) {
  .hover\:bg-primary\/10:hover {
    background-color: var(--primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .hover\:bg-primary\/10:hover {
      background-color: color-mix(in oklab, var(--primary) 10%, transparent);
    }
  }
}

@media (hover: hover) {
  .hover\:bg-primary\/30:hover {
    background-color: var(--primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .hover\:bg-primary\/30:hover {
      background-color: color-mix(in oklab, var(--primary) 30%, transparent);
    }
  }
}

@media (hover: hover) {
  .hover\:bg-primary\/90:hover {
    background-color: var(--primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .hover\:bg-primary\/90:hover {
      background-color: color-mix(in oklab, var(--primary) 90%, transparent);
    }
  }
}

@media (hover: hover) {
  .hover\:bg-secondary\/80:hover {
    background-color: var(--secondary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .hover\:bg-secondary\/80:hover {
      background-color: color-mix(in oklab, var(--secondary) 80%, transparent);
    }
  }
}

@media (hover: hover) {
  .hover\:bg-sidebar-accent:hover {
    background-color: var(--sidebar-accent);
  }
}

@media (hover: hover) {
  .hover\:text-accent-foreground:hover {
    color: var(--accent-foreground);
  }
}

@media (hover: hover) {
  .hover\:text-sidebar-accent-foreground:hover {
    color: var(--sidebar-accent-foreground);
  }
}

@media (hover: hover) {
  .hover\:text-white:hover {
    color: var(--color-white);
  }
}

@media (hover: hover) {
  .hover\:underline:hover {
    text-decoration-line: underline;
  }
}

@media (hover: hover) {
  .hover\:opacity-100:hover {
    opacity: 1;
  }
}

@media (hover: hover) {
  .hover\:shadow-\[0_0_0_1px_hsl\(var\(--sidebar-accent\)\)\]:hover {
    --tw-shadow: 0 0 0 1px var(--tw-shadow-color, hsl(var(--sidebar-accent)));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
}

@media (hover: hover) {
  .hover\:ring-2:hover {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
}

@media (hover: hover) {
  .hover\:ring-card-foreground\/10:hover {
    --tw-ring-color: var(--card-foreground);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .hover\:ring-card-foreground\/10:hover {
      --tw-ring-color: color-mix(in oklab, var(--card-foreground) 10%, transparent);
    }
  }
}

@media (hover: hover) {
  .hover\:ring-primary\/50:hover {
    --tw-ring-color: var(--primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .hover\:ring-primary\/50:hover {
      --tw-ring-color: color-mix(in oklab, var(--primary) 50%, transparent);
    }
  }
}

@media (hover: hover) {
  .hover\:group-data-\[collapsible\=offcanvas\]\:bg-sidebar:hover:is(:where(.group)[data-collapsible="offcanvas"] *) {
    background-color: var(--sidebar);
  }
}

@media (hover: hover) {
  .hover\:after\:bg-sidebar-border:hover:after {
    content: var(--tw-content);
    background-color: var(--sidebar-border);
  }
}

.focus\:z-10:focus {
  z-index: 10;
}

.focus\:border-blue-500:focus {
  border-color: var(--color-blue-500);
}

.focus\:bg-accent:focus {
  background-color: var(--accent);
}

.focus\:text-accent-foreground:focus {
  color: var(--accent-foreground);
}

.focus\:ring-1:focus {
  --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}

.focus\:ring-2:focus {
  --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}

.focus\:ring-blue-500:focus {
  --tw-ring-color: var(--color-blue-500);
}

.focus\:ring-ring:focus {
  --tw-ring-color: var(--ring);
}

.focus\:ring-offset-2:focus {
  --tw-ring-offset-width: 2px;
  --tw-ring-offset-shadow: var(--tw-ring-inset, ) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
}

.focus\:outline-hidden:focus {
  --tw-outline-style: none;
  outline-style: none;
}

@media (forced-colors: active) {
  .focus\:outline-hidden:focus {
    outline-offset: 2px;
    outline: 2px solid #0000;
  }
}

.focus\:outline-none:focus {
  --tw-outline-style: none;
  outline-style: none;
}

.focus-visible\:border-ring:focus-visible {
  border-color: var(--ring);
}

.focus-visible\:ring-0:focus-visible {
  --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}

.focus-visible\:ring-2:focus-visible {
  --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}

.focus-visible\:ring-\[1px\]:focus-visible {
  --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}

.focus-visible\:ring-\[3px\]:focus-visible {
  --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}

.focus-visible\:ring-destructive\/20:focus-visible {
  --tw-ring-color: var(--destructive);
}

@supports (color: color-mix(in lab, red, red)) {
  .focus-visible\:ring-destructive\/20:focus-visible {
    --tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent);
  }
}

.focus-visible\:ring-ring\/50:focus-visible {
  --tw-ring-color: var(--ring);
}

@supports (color: color-mix(in lab, red, red)) {
  .focus-visible\:ring-ring\/50:focus-visible {
    --tw-ring-color: color-mix(in oklab, var(--ring) 50%, transparent);
  }
}

.focus-visible\:outline-1:focus-visible {
  outline-style: var(--tw-outline-style);
  outline-width: 1px;
}

.focus-visible\:outline-none:focus-visible {
  --tw-outline-style: none;
  outline-style: none;
}

.active\:bg-sidebar-accent:active {
  background-color: var(--sidebar-accent);
}

.active\:text-sidebar-accent-foreground:active {
  color: var(--sidebar-accent-foreground);
}

.disabled\:pointer-events-none:disabled {
  pointer-events: none;
}

.disabled\:cursor-not-allowed:disabled {
  cursor: not-allowed;
}

.disabled\:opacity-50:disabled {
  opacity: .5;
}

:where([data-side="left"]) .in-data-\[side\=left\]\:cursor-w-resize {
  cursor: w-resize;
}

:where([data-side="right"]) .in-data-\[side\=right\]\:cursor-e-resize {
  cursor: e-resize;
}

.has-data-\[slot\=card-action\]\:grid-cols-\[1fr_auto\]:has([data-slot="card-action"]) {
  grid-template-columns: 1fr auto;
}

.has-data-\[variant\=inset\]\:bg-sidebar:has([data-variant="inset"]) {
  background-color: var(--sidebar);
}

.has-\[\>svg\]\:px-2\.5:has( > svg) {
  padding-inline: calc(var(--spacing) * 2.5);
}

.has-\[\>svg\]\:px-3:has( > svg) {
  padding-inline: calc(var(--spacing) * 3);
}

.has-\[\>svg\]\:px-4:has( > svg) {
  padding-inline: calc(var(--spacing) * 4);
}

.aria-disabled\:pointer-events-none[aria-disabled="true"] {
  pointer-events: none;
}

.aria-disabled\:opacity-50[aria-disabled="true"] {
  opacity: .5;
}

.aria-invalid\:border-destructive[aria-invalid="true"] {
  border-color: var(--destructive);
}

.aria-invalid\:ring-destructive\/20[aria-invalid="true"] {
  --tw-ring-color: var(--destructive);
}

@supports (color: color-mix(in lab, red, red)) {
  .aria-invalid\:ring-destructive\/20[aria-invalid="true"] {
    --tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent);
  }
}

.data-\[active\=true\]\:bg-sidebar-accent[data-active="true"] {
  background-color: var(--sidebar-accent);
}

.data-\[active\=true\]\:font-medium[data-active="true"] {
  --tw-font-weight: var(--font-weight-medium);
  font-weight: var(--font-weight-medium);
}

.data-\[active\=true\]\:text-sidebar-accent-foreground[data-active="true"] {
  color: var(--sidebar-accent-foreground);
}

.data-\[disabled\]\:pointer-events-none[data-disabled] {
  pointer-events: none;
}

.data-\[disabled\]\:opacity-50[data-disabled] {
  opacity: .5;
}

.data-\[disabled\=true\]\:pointer-events-none[data-disabled="true"] {
  pointer-events: none;
}

.data-\[disabled\=true\]\:opacity-50[data-disabled="true"] {
  opacity: .5;
}

.data-\[error\=true\]\:text-destructive[data-error="true"] {
  color: var(--destructive);
}

.data-\[inset\]\:pl-8[data-inset] {
  padding-left: calc(var(--spacing) * 8);
}

.data-\[orientation\=horizontal\]\:h-px[data-orientation="horizontal"] {
  height: 1px;
}

.data-\[orientation\=horizontal\]\:w-full[data-orientation="horizontal"] {
  width: 100%;
}

.data-\[orientation\=vertical\]\:h-full[data-orientation="vertical"] {
  height: 100%;
}

.data-\[orientation\=vertical\]\:w-px[data-orientation="vertical"] {
  width: 1px;
}

.data-\[placeholder\]\:text-muted-foreground[data-placeholder] {
  color: var(--muted-foreground);
}

.data-\[selected\=true\]\:bg-accent[data-selected="true"] {
  background-color: var(--accent);
}

.data-\[selected\=true\]\:text-accent-foreground[data-selected="true"] {
  color: var(--accent-foreground);
}

.data-\[side\=bottom\]\:translate-y-1[data-side="bottom"] {
  --tw-translate-y: calc(var(--spacing) * 1);
  translate: var(--tw-translate-x) var(--tw-translate-y);
}

.data-\[side\=bottom\]\:slide-in-from-top-2[data-side="bottom"] {
  --tw-enter-translate-y: calc(2 * var(--spacing) * -1);
}

.data-\[side\=left\]\:-translate-x-1[data-side="left"] {
  --tw-translate-x: calc(var(--spacing) * -1);
  translate: var(--tw-translate-x) var(--tw-translate-y);
}

.data-\[side\=left\]\:slide-in-from-right-2[data-side="left"] {
  --tw-enter-translate-x: calc(2 * var(--spacing));
}

.data-\[side\=right\]\:translate-x-1[data-side="right"] {
  --tw-translate-x: calc(var(--spacing) * 1);
  translate: var(--tw-translate-x) var(--tw-translate-y);
}

.data-\[side\=right\]\:slide-in-from-left-2[data-side="right"] {
  --tw-enter-translate-x: calc(2 * var(--spacing) * -1);
}

.data-\[side\=top\]\:-translate-y-1[data-side="top"] {
  --tw-translate-y: calc(var(--spacing) * -1);
  translate: var(--tw-translate-x) var(--tw-translate-y);
}

.data-\[side\=top\]\:slide-in-from-bottom-2[data-side="top"] {
  --tw-enter-translate-y: calc(2 * var(--spacing));
}

.data-\[size\=default\]\:h-9[data-size="default"] {
  height: calc(var(--spacing) * 9);
}

.data-\[size\=sm\]\:h-8[data-size="sm"] {
  height: calc(var(--spacing) * 8);
}

:is(.\*\*\:data-\[slot\=command-input-wrapper\]\:h-12 *)[data-slot="command-input-wrapper"] {
  height: calc(var(--spacing) * 12);
}

:is(.\*\:data-\[slot\=select-value\]\:line-clamp-1 > *)[data-slot="select-value"] {
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  display: -webkit-box;
  overflow: hidden;
}

:is(.\*\:data-\[slot\=select-value\]\:flex > *)[data-slot="select-value"] {
  display: flex;
}

:is(.\*\:data-\[slot\=select-value\]\:items-center > *)[data-slot="select-value"] {
  align-items: center;
}

:is(.\*\:data-\[slot\=select-value\]\:gap-2 > *)[data-slot="select-value"] {
  gap: calc(var(--spacing) * 2);
}

.data-\[state\=checked\]\:translate-x-\[calc\(100\%-2px\)\][data-state="checked"] {
  --tw-translate-x: calc(100% - 2px);
  translate: var(--tw-translate-x) var(--tw-translate-y);
}

.data-\[state\=checked\]\:bg-primary[data-state="checked"] {
  background-color: var(--primary);
}

.data-\[state\=closed\]\:animate-out[data-state="closed"] {
  animation: exit var(--tw-animation-duration, var(--tw-duration, .15s)) var(--tw-ease, ease) var(--tw-animation-delay, 0s) var(--tw-animation-iteration-count, 1) var(--tw-animation-direction, normal) var(--tw-animation-fill-mode, none);
}

.data-\[state\=closed\]\:duration-300[data-state="closed"] {
  --tw-duration: .3s;
  transition-duration: .3s;
}

.data-\[state\=closed\]\:fade-out-0[data-state="closed"] {
  --tw-exit-opacity: 0;
}

.data-\[state\=closed\]\:zoom-out-95[data-state="closed"] {
  --tw-exit-scale: .95;
}

.data-\[state\=closed\]\:slide-out-to-bottom[data-state="closed"] {
  --tw-exit-translate-y: 100%;
}

.data-\[state\=closed\]\:slide-out-to-left[data-state="closed"] {
  --tw-exit-translate-x: -100%;
}

.data-\[state\=closed\]\:slide-out-to-right[data-state="closed"] {
  --tw-exit-translate-x: 100%;
}

.data-\[state\=closed\]\:slide-out-to-top[data-state="closed"] {
  --tw-exit-translate-y: -100%;
}

.data-\[state\=open\]\:animate-in[data-state="open"] {
  animation: enter var(--tw-animation-duration, var(--tw-duration, .15s)) var(--tw-ease, ease) var(--tw-animation-delay, 0s) var(--tw-animation-iteration-count, 1) var(--tw-animation-direction, normal) var(--tw-animation-fill-mode, none);
}

.data-\[state\=open\]\:bg-accent[data-state="open"] {
  background-color: var(--accent);
}

.data-\[state\=open\]\:bg-secondary[data-state="open"] {
  background-color: var(--secondary);
}

.data-\[state\=open\]\:text-accent-foreground[data-state="open"] {
  color: var(--accent-foreground);
}

.data-\[state\=open\]\:text-muted-foreground[data-state="open"] {
  color: var(--muted-foreground);
}

.data-\[state\=open\]\:opacity-100[data-state="open"] {
  opacity: 1;
}

.data-\[state\=open\]\:duration-500[data-state="open"] {
  --tw-duration: .5s;
  transition-duration: .5s;
}

.data-\[state\=open\]\:fade-in-0[data-state="open"] {
  --tw-enter-opacity: 0;
}

.data-\[state\=open\]\:zoom-in-95[data-state="open"] {
  --tw-enter-scale: .95;
}

.data-\[state\=open\]\:slide-in-from-bottom[data-state="open"] {
  --tw-enter-translate-y: 100%;
}

.data-\[state\=open\]\:slide-in-from-left[data-state="open"] {
  --tw-enter-translate-x: -100%;
}

.data-\[state\=open\]\:slide-in-from-right[data-state="open"] {
  --tw-enter-translate-x: 100%;
}

.data-\[state\=open\]\:slide-in-from-top[data-state="open"] {
  --tw-enter-translate-y: -100%;
}

@media (hover: hover) {
  .data-\[state\=open\]\:hover\:bg-sidebar-accent[data-state="open"]:hover {
    background-color: var(--sidebar-accent);
  }
}

@media (hover: hover) {
  .data-\[state\=open\]\:hover\:text-sidebar-accent-foreground[data-state="open"]:hover {
    color: var(--sidebar-accent-foreground);
  }
}

.data-\[state\=selected\]\:bg-muted[data-state="selected"] {
  background-color: var(--muted);
}

.data-\[state\=unchecked\]\:translate-x-0[data-state="unchecked"] {
  --tw-translate-x: calc(var(--spacing) * 0);
  translate: var(--tw-translate-x) var(--tw-translate-y);
}

.data-\[state\=unchecked\]\:bg-input[data-state="unchecked"] {
  background-color: var(--input);
}

.data-\[variant\=destructive\]\:text-destructive[data-variant="destructive"] {
  color: var(--destructive);
}

.data-\[variant\=destructive\]\:focus\:bg-destructive\/10[data-variant="destructive"]:focus {
  background-color: var(--destructive);
}

@supports (color: color-mix(in lab, red, red)) {
  .data-\[variant\=destructive\]\:focus\:bg-destructive\/10[data-variant="destructive"]:focus {
    background-color: color-mix(in oklab, var(--destructive) 10%, transparent);
  }
}

.data-\[variant\=destructive\]\:focus\:text-destructive[data-variant="destructive"]:focus {
  color: var(--destructive);
}

@media (width >= 40rem) {
  .sm\:ml-auto {
    margin-left: auto;
  }
}

@media (width >= 40rem) {
  .sm\:flex {
    display: flex;
  }
}

@media (width >= 40rem) {
  .sm\:max-w-lg {
    max-width: var(--container-lg);
  }
}

@media (width >= 40rem) {
  .sm\:max-w-sm {
    max-width: var(--container-sm);
  }
}

@media (width >= 40rem) {
  .sm\:flex-row {
    flex-direction: row;
  }
}

@media (width >= 40rem) {
  .sm\:justify-end {
    justify-content: flex-end;
  }
}

@media (width >= 40rem) {
  .sm\:px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }
}

@media (width >= 40rem) {
  .sm\:pt-6 {
    padding-top: calc(var(--spacing) * 6);
  }
}

@media (width >= 40rem) {
  .sm\:text-left {
    text-align: left;
  }
}

@media (width >= 40rem) {
  .sm\:text-6xl {
    font-size: var(--text-6xl);
    line-height: var(--tw-leading, var(--text-6xl--line-height));
  }
}

@media (width >= 48rem) {
  .md\:top-\[10\%\] {
    top: 10%;
  }
}

@media (width >= 48rem) {
  .md\:top-\[15\%\] {
    top: 15%;
  }
}

@media (width >= 48rem) {
  .md\:top-\[20\%\] {
    top: 20%;
  }
}

@media (width >= 48rem) {
  .md\:top-\[75\%\] {
    top: 75%;
  }
}

@media (width >= 48rem) {
  .md\:right-\[0\%\] {
    right: 0%;
  }
}

@media (width >= 48rem) {
  .md\:right-\[20\%\] {
    right: 20%;
  }
}

@media (width >= 48rem) {
  .md\:bottom-\[10\%\] {
    bottom: 10%;
  }
}

@media (width >= 48rem) {
  .md\:left-\[-5\%\] {
    left: -5%;
  }
}

@media (width >= 48rem) {
  .md\:left-\[10\%\] {
    left: 10%;
  }
}

@media (width >= 48rem) {
  .md\:left-\[25\%\] {
    left: 25%;
  }
}

@media (width >= 48rem) {
  .md\:mb-8 {
    margin-bottom: calc(var(--spacing) * 8);
  }
}

@media (width >= 48rem) {
  .md\:mb-12 {
    margin-bottom: calc(var(--spacing) * 12);
  }
}

@media (width >= 48rem) {
  .md\:block {
    display: block;
  }
}

@media (width >= 48rem) {
  .md\:flex {
    display: flex;
  }
}

@media (width >= 48rem) {
  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@media (width >= 48rem) {
  .md\:flex-row {
    flex-direction: row;
  }
}

@media (width >= 48rem) {
  .md\:items-center {
    align-items: center;
  }
}

@media (width >= 48rem) {
  .md\:p-6 {
    padding: calc(var(--spacing) * 6);
  }
}

@media (width >= 48rem) {
  .md\:p-8 {
    padding: calc(var(--spacing) * 8);
  }
}

@media (width >= 48rem) {
  .md\:px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }
}

@media (width >= 48rem) {
  .md\:text-4xl {
    font-size: var(--text-4xl);
    line-height: var(--tw-leading, var(--text-4xl--line-height));
  }
}

@media (width >= 48rem) {
  .md\:text-8xl {
    font-size: var(--text-8xl);
    line-height: var(--tw-leading, var(--text-8xl--line-height));
  }
}

@media (width >= 48rem) {
  .md\:text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }
}

@media (width >= 48rem) {
  .md\:opacity-0 {
    opacity: 0;
  }
}

@media (width >= 48rem) {
  .md\:peer-data-\[variant\=inset\]\:m-2:is(:where(.peer)[data-variant="inset"] ~ *) {
    margin: calc(var(--spacing) * 2);
  }
}

@media (width >= 48rem) {
  .md\:peer-data-\[variant\=inset\]\:ml-0:is(:where(.peer)[data-variant="inset"] ~ *) {
    margin-left: calc(var(--spacing) * 0);
  }
}

@media (width >= 48rem) {
  .md\:peer-data-\[variant\=inset\]\:rounded-xl:is(:where(.peer)[data-variant="inset"] ~ *) {
    border-radius: calc(var(--radius)  + 4px);
  }
}

@media (width >= 48rem) {
  .md\:peer-data-\[variant\=inset\]\:shadow-sm:is(:where(.peer)[data-variant="inset"] ~ *) {
    --tw-shadow: var(--shadow-sm);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
}

@media (width >= 48rem) {
  .md\:peer-data-\[variant\=inset\]\:peer-data-\[state\=collapsed\]\:ml-2:is(:where(.peer)[data-variant="inset"] ~ *):is(:where(.peer)[data-state="collapsed"] ~ *) {
    margin-left: calc(var(--spacing) * 2);
  }
}

@media (width >= 48rem) {
  .md\:after\:hidden:after {
    content: var(--tw-content);
    display: none;
  }
}

@media (width >= 64rem) {
  .lg\:h-10 {
    height: calc(var(--spacing) * 10);
  }
}

@media (width >= 64rem) {
  .lg\:max-h-10 {
    max-height: calc(var(--spacing) * 10);
  }
}

@media (width >= 64rem) {
  .lg\:w-10 {
    width: calc(var(--spacing) * 10);
  }
}

@media (width >= 64rem) {
  .lg\:max-w-10 {
    max-width: calc(var(--spacing) * 10);
  }
}

@media (width >= 64rem) {
  .lg\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@media (width >= 64rem) {
  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

@media (width >= 64rem) {
  .lg\:p-8 {
    padding: calc(var(--spacing) * 8);
  }
}

@media (width >= 80rem) {
  .xl\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

.dark\:border-input:is(.dark *) {
  border-color: var(--input);
}

.dark\:bg-card:is(.dark *) {
  background-color: var(--card);
}

.dark\:bg-destructive\/60:is(.dark *) {
  background-color: var(--destructive);
}

@supports (color: color-mix(in lab, red, red)) {
  .dark\:bg-destructive\/60:is(.dark *) {
    background-color: color-mix(in oklab, var(--destructive) 60%, transparent);
  }
}

.dark\:bg-emerald-600:is(.dark *) {
  background-color: var(--color-emerald-600);
}

.dark\:bg-input\/30:is(.dark *) {
  background-color: var(--input);
}

@supports (color: color-mix(in lab, red, red)) {
  .dark\:bg-input\/30:is(.dark *) {
    background-color: color-mix(in oklab, var(--input) 30%, transparent);
  }
}

.dark\:text-emerald-300:is(.dark *) {
  color: var(--color-emerald-300);
}

.dark\:\[--base-color\:\#71717a\]:is(.dark *) {
  --base-color: #71717a;
}

.dark\:\[--base-gradient-color\:\#ffffff\]:is(.dark *) {
  --base-gradient-color: #fff;
}

.dark\:\[--bg\:linear-gradient\(90deg\,\#0000_calc\(50\%-var\(--spread\)\)\,var\(--base-gradient-color\)\,\#0000_calc\(50\%\+var\(--spread\)\)\)\]:is(.dark *) {
  --bg: linear-gradient(90deg, #0000 calc(50% - var(--spread)), var(--base-gradient-color), #0000 calc(50% + var(--spread)));
}

@media (hover: hover) {
  .dark\:hover\:bg-accent\/50:is(.dark *):hover {
    background-color: var(--accent);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:hover\:bg-accent\/50:is(.dark *):hover {
      background-color: color-mix(in oklab, var(--accent) 50%, transparent);
    }
  }
}

@media (hover: hover) {
  .dark\:hover\:bg-input\/50:is(.dark *):hover {
    background-color: var(--input);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:hover\:bg-input\/50:is(.dark *):hover {
      background-color: color-mix(in oklab, var(--input) 50%, transparent);
    }
  }
}

.dark\:focus-visible\:ring-destructive\/40:is(.dark *):focus-visible {
  --tw-ring-color: var(--destructive);
}

@supports (color: color-mix(in lab, red, red)) {
  .dark\:focus-visible\:ring-destructive\/40:is(.dark *):focus-visible {
    --tw-ring-color: color-mix(in oklab, var(--destructive) 40%, transparent);
  }
}

.dark\:aria-invalid\:ring-destructive\/40:is(.dark *)[aria-invalid="true"] {
  --tw-ring-color: var(--destructive);
}

@supports (color: color-mix(in lab, red, red)) {
  .dark\:aria-invalid\:ring-destructive\/40:is(.dark *)[aria-invalid="true"] {
    --tw-ring-color: color-mix(in oklab, var(--destructive) 40%, transparent);
  }
}

.dark\:data-\[state\=checked\]\:bg-primary-foreground:is(.dark *)[data-state="checked"] {
  background-color: var(--primary-foreground);
}

.dark\:data-\[state\=unchecked\]\:bg-foreground:is(.dark *)[data-state="unchecked"] {
  background-color: var(--foreground);
}

.dark\:data-\[state\=unchecked\]\:bg-input\/80:is(.dark *)[data-state="unchecked"] {
  background-color: var(--input);
}

@supports (color: color-mix(in lab, red, red)) {
  .dark\:data-\[state\=unchecked\]\:bg-input\/80:is(.dark *)[data-state="unchecked"] {
    background-color: color-mix(in oklab, var(--input) 80%, transparent);
  }
}

.dark\:data-\[variant\=destructive\]\:focus\:bg-destructive\/20:is(.dark *)[data-variant="destructive"]:focus {
  background-color: var(--destructive);
}

@supports (color: color-mix(in lab, red, red)) {
  .dark\:data-\[variant\=destructive\]\:focus\:bg-destructive\/20:is(.dark *)[data-variant="destructive"]:focus {
    background-color: color-mix(in oklab, var(--destructive) 20%, transparent);
  }
}

.\[\&_\.recharts-cartesian-axis-tick_text\]\:fill-muted-foreground .recharts-cartesian-axis-tick text {
  fill: var(--muted-foreground);
}

.\[\&_\.recharts-cartesian-grid_line\[stroke\=\'\#ccc\'\]\]\:stroke-border\/50 .recharts-cartesian-grid line[stroke="#ccc"] {
  stroke: var(--border);
}

@supports (color: color-mix(in lab, red, red)) {
  .\[\&_\.recharts-cartesian-grid_line\[stroke\=\'\#ccc\'\]\]\:stroke-border\/50 .recharts-cartesian-grid line[stroke="#ccc"] {
    stroke: color-mix(in oklab, var(--border) 50%, transparent);
  }
}

.\[\&_\.recharts-curve\.recharts-tooltip-cursor\]\:stroke-border .recharts-curve.recharts-tooltip-cursor {
  stroke: var(--border);
}

.\[\&_\.recharts-dot\[stroke\=\'\#fff\'\]\]\:stroke-transparent .recharts-dot[stroke="#fff"] {
  stroke: #0000;
}

.\[\&_\.recharts-layer\]\:outline-hidden .recharts-layer {
  --tw-outline-style: none;
  outline-style: none;
}

@media (forced-colors: active) {
  .\[\&_\.recharts-layer\]\:outline-hidden .recharts-layer {
    outline-offset: 2px;
    outline: 2px solid #0000;
  }
}

.\[\&_\.recharts-polar-grid_\[stroke\=\'\#ccc\'\]\]\:stroke-border .recharts-polar-grid [stroke="#ccc"] {
  stroke: var(--border);
}

.\[\&_\.recharts-radial-bar-background-sector\]\:fill-muted .recharts-radial-bar-background-sector {
  fill: var(--muted);
}

.\[\&_\.recharts-rectangle\.recharts-tooltip-cursor\]\:fill-muted .recharts-rectangle.recharts-tooltip-cursor {
  fill: var(--muted);
}

.\[\&_\.recharts-reference-line_\[stroke\=\'\#ccc\'\]\]\:stroke-border .recharts-reference-line [stroke="#ccc"] {
  stroke: var(--border);
}

.\[\&_\.recharts-sector\]\:outline-hidden .recharts-sector {
  --tw-outline-style: none;
  outline-style: none;
}

@media (forced-colors: active) {
  .\[\&_\.recharts-sector\]\:outline-hidden .recharts-sector {
    outline-offset: 2px;
    outline: 2px solid #0000;
  }
}

.\[\&_\.recharts-sector\[stroke\=\'\#fff\'\]\]\:stroke-transparent .recharts-sector[stroke="#fff"] {
  stroke: #0000;
}

.\[\&_\.recharts-surface\]\:outline-hidden .recharts-surface {
  --tw-outline-style: none;
  outline-style: none;
}

@media (forced-colors: active) {
  .\[\&_\.recharts-surface\]\:outline-hidden .recharts-surface {
    outline-offset: 2px;
    outline: 2px solid #0000;
  }
}

.\[\&_\[cmdk-group-heading\]\]\:px-2 [cmdk-group-heading] {
  padding-inline: calc(var(--spacing) * 2);
}

.\[\&_\[cmdk-group-heading\]\]\:py-1\.5 [cmdk-group-heading] {
  padding-block: calc(var(--spacing) * 1.5);
}

.\[\&_\[cmdk-group-heading\]\]\:text-xs [cmdk-group-heading] {
  font-size: var(--text-xs);
  line-height: var(--tw-leading, var(--text-xs--line-height));
}

.\[\&_\[cmdk-group-heading\]\]\:font-medium [cmdk-group-heading] {
  --tw-font-weight: var(--font-weight-medium);
  font-weight: var(--font-weight-medium);
}

.\[\&_\[cmdk-group-heading\]\]\:text-muted-foreground [cmdk-group-heading] {
  color: var(--muted-foreground);
}

.\[\&_\[cmdk-group\]\]\:px-2 [cmdk-group] {
  padding-inline: calc(var(--spacing) * 2);
}

.\[\&_\[cmdk-group\]\:not\(\[hidden\]\)_\~\[cmdk-group\]\]\:pt-0 [cmdk-group]:not([hidden]) ~ [cmdk-group] {
  padding-top: calc(var(--spacing) * 0);
}

.\[\&_\[cmdk-input-wrapper\]_svg\]\:h-5 [cmdk-input-wrapper] svg {
  height: calc(var(--spacing) * 5);
}

.\[\&_\[cmdk-input-wrapper\]_svg\]\:w-5 [cmdk-input-wrapper] svg {
  width: calc(var(--spacing) * 5);
}

.\[\&_\[cmdk-input\]\]\:h-12 [cmdk-input] {
  height: calc(var(--spacing) * 12);
}

.\[\&_\[cmdk-item\]\]\:px-2 [cmdk-item] {
  padding-inline: calc(var(--spacing) * 2);
}

.\[\&_\[cmdk-item\]\]\:py-3 [cmdk-item] {
  padding-block: calc(var(--spacing) * 3);
}

.\[\&_\[cmdk-item\]_svg\]\:h-5 [cmdk-item] svg {
  height: calc(var(--spacing) * 5);
}

.\[\&_\[cmdk-item\]_svg\]\:w-5 [cmdk-item] svg {
  width: calc(var(--spacing) * 5);
}

.\[\&_svg\]\:pointer-events-none svg {
  pointer-events: none;
}

.\[\&_svg\]\:shrink-0 svg {
  flex-shrink: 0;
}

.\[\&_svg\:not\(\[class\*\=\'size-\'\]\)\]\:size-4 svg:not([class*="size-"]) {
  width: calc(var(--spacing) * 4);
  height: calc(var(--spacing) * 4);
}

.\[\&_svg\:not\(\[class\*\=\'size-\'\]\)\]\:size-full svg:not([class*="size-"]) {
  width: 100%;
  height: 100%;
}

.\[\&_svg\:not\(\[class\*\=\'text-\'\]\)\]\:text-muted-foreground svg:not([class*="text-"]) {
  color: var(--muted-foreground);
}

.\[\&_tr\]\:border-b tr {
  border-bottom-style: var(--tw-border-style);
  border-bottom-width: 1px;
}

.\[\&_tr\:last-child\]\:border-0 tr:last-child {
  border-style: var(--tw-border-style);
  border-width: 0;
}

.\[\&\:has\(\[role\=checkbox\]\)\]\:pr-0:has([role="checkbox"]) {
  padding-right: calc(var(--spacing) * 0);
}

.\[\.border-b\]\:pb-6.border-b {
  padding-bottom: calc(var(--spacing) * 6);
}

.\[\.border-t\]\:pt-0.border-t {
  padding-top: calc(var(--spacing) * 0);
}

.\[\.border-t\]\:pt-6.border-t {
  padding-top: calc(var(--spacing) * 6);
}

:is(.\*\:\[span\]\:last\:flex > *):is(span):last-child {
  display: flex;
}

:is(.\*\:\[span\]\:last\:items-center > *):is(span):last-child {
  align-items: center;
}

:is(.\*\:\[span\]\:last\:gap-2 > *):is(span):last-child {
  gap: calc(var(--spacing) * 2);
}

:is(.data-\[variant\=destructive\]\:\*\:\[svg\]\:\!text-destructive[data-variant="destructive"] > *):is(svg) {
  color: var(--destructive) !important;
}

.\[\&\>\[role\=checkbox\]\]\:translate-y-\[2px\] > [role="checkbox"] {
  --tw-translate-y: 2px;
  translate: var(--tw-translate-x) var(--tw-translate-y);
}

.\[\&\>button\]\:hidden > button {
  display: none;
}

.\[\&\>span\:last-child\]\:truncate > span:last-child {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.\[\&\>svg\]\:pointer-events-none > svg {
  pointer-events: none;
}

.\[\&\>svg\]\:size-3 > svg {
  width: calc(var(--spacing) * 3);
  height: calc(var(--spacing) * 3);
}

.\[\&\>svg\]\:size-4 > svg {
  width: calc(var(--spacing) * 4);
  height: calc(var(--spacing) * 4);
}

.\[\&\>svg\]\:h-2\.5 > svg {
  height: calc(var(--spacing) * 2.5);
}

.\[\&\>svg\]\:h-3 > svg {
  height: calc(var(--spacing) * 3);
}

.\[\&\>svg\]\:w-2\.5 > svg {
  width: calc(var(--spacing) * 2.5);
}

.\[\&\>svg\]\:w-3 > svg {
  width: calc(var(--spacing) * 3);
}

.\[\&\>svg\]\:shrink-0 > svg {
  flex-shrink: 0;
}

.\[\&\>svg\]\:text-muted-foreground > svg {
  color: var(--muted-foreground);
}

.\[\&\>svg\]\:text-sidebar-accent-foreground > svg {
  color: var(--sidebar-accent-foreground);
}

.\[\&\>tr\]\:last\:border-b-0 > tr:last-child {
  border-bottom-style: var(--tw-border-style);
  border-bottom-width: 0;
}

[data-side="left"][data-collapsible="offcanvas"] .\[\[data-side\=left\]\[data-collapsible\=offcanvas\]_\&\]\:-right-2 {
  right: calc(var(--spacing) * -2);
}

[data-side="left"][data-state="collapsed"] .\[\[data-side\=left\]\[data-state\=collapsed\]_\&\]\:cursor-e-resize {
  cursor: e-resize;
}

[data-side="right"][data-collapsible="offcanvas"] .\[\[data-side\=right\]\[data-collapsible\=offcanvas\]_\&\]\:-left-2 {
  left: calc(var(--spacing) * -2);
}

[data-side="right"][data-state="collapsed"] .\[\[data-side\=right\]\[data-state\=collapsed\]_\&\]\:cursor-w-resize {
  cursor: w-resize;
}

@media (hover: hover) {
  a.\[a\&\]\:hover\:bg-accent:hover {
    background-color: var(--accent);
  }
}

@media (hover: hover) {
  a.\[a\&\]\:hover\:bg-destructive\/90:hover {
    background-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    a.\[a\&\]\:hover\:bg-destructive\/90:hover {
      background-color: color-mix(in oklab, var(--destructive) 90%, transparent);
    }
  }
}

@media (hover: hover) {
  a.\[a\&\]\:hover\:bg-primary\/90:hover {
    background-color: var(--primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    a.\[a\&\]\:hover\:bg-primary\/90:hover {
      background-color: color-mix(in oklab, var(--primary) 90%, transparent);
    }
  }
}

@media (hover: hover) {
  a.\[a\&\]\:hover\:bg-secondary\/90:hover {
    background-color: var(--secondary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    a.\[a\&\]\:hover\:bg-secondary\/90:hover {
      background-color: color-mix(in oklab, var(--secondary) 90%, transparent);
    }
  }
}

@media (hover: hover) {
  a.\[a\&\]\:hover\:text-accent-foreground:hover {
    color: var(--accent-foreground);
  }
}

@layer theme {
  :root, :host {
    --font-sans: var(--font-sans);
    --font-serif: var(--font-serif);
    --font-mono: var(--font-mono);
    --color-red-200: oklch(88.5% .062 18.334);
    --color-red-300: oklch(80.8% .114 19.571);
    --color-red-400: oklch(70.4% .191 22.216);
    --color-red-500: oklch(63.7% .237 25.331);
    --color-red-900: oklch(39.6% .141 25.723);
    --color-amber-400: oklch(82.8% .189 84.429);
    --color-amber-500: oklch(76.9% .188 70.08);
    --color-amber-600: oklch(66.6% .179 58.318);
    --color-amber-700: oklch(55.5% .163 48.998);
    --color-yellow-300: oklch(90.5% .182 98.111);
    --color-yellow-500: oklch(79.5% .184 86.047);
    --color-yellow-600: oklch(68.1% .162 75.834);
    --color-green-100: oklch(96.2% .044 156.743);
    --color-green-300: oklch(87.1% .15 154.449);
    --color-green-500: oklch(72.3% .219 149.579);
    --color-green-600: oklch(62.7% .194 149.214);
    --color-emerald-300: oklch(84.5% .143 164.978);
    --color-emerald-400: oklch(76.5% .177 163.223);
    --color-emerald-500: oklch(69.6% .17 162.48);
    --color-emerald-600: oklch(59.6% .145 163.225);
    --color-emerald-700: oklch(50.8% .118 165.612);
    --color-emerald-800: oklch(43.2% .095 166.913);
    --color-emerald-900: oklch(37.8% .077 168.94);
    --color-cyan-500: oklch(71.5% .143 215.221);
    --color-blue-300: oklch(80.9% .105 251.813);
    --color-blue-400: oklch(70.7% .165 254.624);
    --color-blue-500: oklch(62.3% .214 259.815);
    --color-blue-600: oklch(54.6% .245 262.881);
    --color-blue-700: oklch(48.8% .243 264.376);
    --color-indigo-500: oklch(58.5% .233 277.117);
    --color-violet-500: oklch(60.6% .25 292.717);
    --color-rose-500: oklch(64.5% .246 16.439);
    --color-gray-300: oklch(87.2% .01 258.338);
    --color-gray-700: oklch(37.3% .034 259.733);
    --color-zinc-300: oklch(87.1% .006 286.286);
    --color-zinc-400: oklch(70.5% .015 286.067);
    --color-zinc-500: oklch(55.2% .016 285.938);
    --color-zinc-600: oklch(44.2% .017 285.786);
    --color-zinc-700: oklch(37% .013 285.805);
    --color-zinc-800: oklch(27.4% .006 286.033);
    --color-neutral-500: oklch(55.6% 0 0);
    --color-neutral-900: oklch(20.5% 0 0);
    --color-stone-500: oklch(55.3% .013 58.071);
    --color-black: #000;
    --color-white: #fff;
    --spacing: .25rem;
    --container-sm: 24rem;
    --container-md: 28rem;
    --container-lg: 32rem;
    --container-xl: 36rem;
    --container-3xl: 48rem;
    --container-6xl: 72rem;
    --container-7xl: 80rem;
    --text-xs: .75rem;
    --text-xs--line-height: calc(1 / .75);
    --text-sm: .875rem;
    --text-sm--line-height: calc(1.25 / .875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-3xl: 1.875rem;
    --text-3xl--line-height: calc(2.25 / 1.875);
    --text-4xl: 2.25rem;
    --text-4xl--line-height: calc(2.5 / 2.25);
    --text-6xl: 3.75rem;
    --text-6xl--line-height: 1;
    --text-8xl: 6rem;
    --text-8xl--line-height: 1;
    --font-weight-light: 300;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --tracking-tight: -.025em;
    --tracking-wide: .025em;
    --tracking-widest: .1em;
    --leading-relaxed: 1.625;
    --radius-xs: .125rem;
    --radius-3xl: 1.5rem;
    --radius-4xl: 2rem;
    --shadow-2xs: var(--shadow-2xs);
    --shadow-xs: var(--shadow-xs);
    --shadow-sm: var(--shadow-sm);
    --shadow-md: var(--shadow-md);
    --shadow-lg: var(--shadow-lg);
    --shadow-xl: var(--shadow-xl);
    --shadow-2xl: var(--shadow-2xl);
    --ease-in-out: cubic-bezier(.4, 0, .2, 1);
    --animate-spin: spin 1s linear infinite;
    --animate-pulse: pulse 2s cubic-bezier(.4, 0, .6, 1) infinite;
    --blur-3xl: 64px;
    --aspect-video: 16 / 9;
    --default-transition-duration: .15s;
    --default-transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    --default-font-family: var(--font-sans);
    --default-mono-font-family: var(--font-mono);
    --shadow: var(--shadow);
    --color-border: var(--border);
  }
}

@layer base {
  *, :after, :before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  html, :host {
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    line-height: 1.5;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }

  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }

  abbr:where([title]) {
    text-decoration: underline dotted;
  }

  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }

  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }

  b, strong {
    font-weight: bolder;
  }

  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }

  small {
    font-size: 80%;
  }

  sub, sup {
    vertical-align: baseline;
    font-size: 75%;
    line-height: 0;
    position: relative;
  }

  sub {
    bottom: -.25em;
  }

  sup {
    top: -.5em;
  }

  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }

  :-moz-focusring {
    outline: auto;
  }

  progress {
    vertical-align: baseline;
  }

  summary {
    display: list-item;
  }

  ol, ul, menu {
    list-style: none;
  }

  img, svg, video, canvas, audio, iframe, embed, object {
    vertical-align: middle;
    display: block;
  }

  img, video {
    max-width: 100%;
    height: auto;
  }

  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: #0000;
    border-radius: 0;
  }

  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }

  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }

  ::file-selector-button {
    margin-inline-end: 4px;
  }

  ::placeholder {
    opacity: 1;
  }

  @supports (not ((-webkit-appearance: -apple-pay-button))) or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentColor;
    }

    @supports (color: color-mix(in lab, red, red)) {
      ::placeholder {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }

  textarea {
    resize: vertical;
  }

  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }

  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }

  ::-webkit-datetime-edit {
    display: inline-flex;
  }

  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }

  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }

  :-moz-ui-invalid {
    box-shadow: none;
  }

  button, input:where([type="button"], [type="reset"], [type="submit"]), ::file-selector-button {
    appearance: button;
  }

  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }

  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }

  * {
    border-color: var(--border);
    outline-color: var(--ring);
  }

  @supports (color: color-mix(in lab, red, red)) {
    * {
      outline-color: color-mix(in oklab, var(--ring) 50%, transparent);
    }
  }

  body {
    background-color: var(--background);
    color: var(--foreground);
  }
}

@layer components, utilities;

@property --tw-animation-delay {
  syntax: "*";
  inherits: false;
  initial-value: 0s;
}

@property --tw-animation-direction {
  syntax: "*";
  inherits: false;
  initial-value: normal;
}

@property --tw-animation-duration {
  syntax: "*";
  inherits: false
}

@property --tw-animation-fill-mode {
  syntax: "*";
  inherits: false;
  initial-value: none;
}

@property --tw-animation-iteration-count {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-enter-opacity {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-enter-rotate {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-enter-scale {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-enter-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-enter-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-exit-opacity {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-exit-rotate {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-exit-scale {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-exit-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-exit-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

:root {
  --background: oklch(.9821 0 0);
  --foreground: oklch(.2435 0 0);
  --card: oklch(.9911 0 0);
  --card-foreground: oklch(.2435 0 0);
  --popover: oklch(.9911 0 0);
  --popover-foreground: oklch(.2435 0 0);
  --primary: oklch(.4341 .0392 41.9938);
  --primary-foreground: oklch(1 0 0);
  --secondary: oklch(.92 .0651 74.3695);
  --secondary-foreground: oklch(.3499 .0685 40.8288);
  --muted: oklch(.9521 0 0);
  --muted-foreground: oklch(.5032 0 0);
  --accent: oklch(.931 0 0);
  --accent-foreground: oklch(.2435 0 0);
  --destructive: oklch(.6271 .1936 33.339);
  --destructive-foreground: oklch(1 0 0);
  --border: oklch(.8822 0 0);
  --input: oklch(.8822 0 0);
  --ring: oklch(.4341 .0392 41.9938);
  --chart-1: oklch(.4341 .0392 41.9938);
  --chart-2: oklch(.92 .0651 74.3695);
  --chart-3: oklch(.931 0 0);
  --chart-4: oklch(.9367 .0523 75.5009);
  --chart-5: oklch(.4338 .0437 41.6746);
  --sidebar: oklch(.9881 0 0);
  --sidebar-foreground: oklch(.2645 0 0);
  --sidebar-primary: oklch(.325 0 0);
  --sidebar-primary-foreground: oklch(.9881 0 0);
  --sidebar-accent: oklch(.9761 0 0);
  --sidebar-accent-foreground: oklch(.325 0 0);
  --sidebar-border: oklch(.9401 0 0);
  --sidebar-ring: oklch(.7731 0 0);
  --font-sans: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  --radius: .5rem;
  --shadow-2xs: 0 1px 3px 0px #0000000d;
  --shadow-xs: 0 1px 3px 0px #0000000d;
  --shadow-sm: 0 1px 3px 0px #0000001a, 0 1px 2px -1px #0000001a;
  --shadow: 0 1px 3px 0px #0000001a, 0 1px 2px -1px #0000001a;
  --shadow-md: 0 1px 3px 0px #0000001a, 0 2px 4px -1px #0000001a;
  --shadow-lg: 0 1px 3px 0px #0000001a, 0 4px 6px -1px #0000001a;
  --shadow-xl: 0 1px 3px 0px #0000001a, 0 8px 10px -1px #0000001a;
  --shadow-2xl: 0 1px 3px 0px #00000040;
  --tracking-normal: 0em;
  --spacing: .25rem;
}

.dark {
  --background: oklch(.1776 0 0);
  --foreground: oklch(.9491 0 0);
  --card: oklch(.2134 0 0);
  --card-foreground: oklch(.9491 0 0);
  --popover: oklch(.2134 0 0);
  --popover-foreground: oklch(.9491 0 0);
  --primary: oklch(.9247 .0524 66.1732);
  --primary-foreground: oklch(.2029 .024 200.196);
  --secondary: oklch(.3163 .019 63.6992);
  --secondary-foreground: oklch(.9247 .0524 66.1732);
  --muted: oklch(.252 0 0);
  --muted-foreground: oklch(.7699 0 0);
  --accent: oklch(.285 0 0);
  --accent-foreground: oklch(.9491 0 0);
  --destructive: oklch(.6271 .1936 33.339);
  --destructive-foreground: oklch(1 0 0);
  --border: oklch(.2351 .0115 91.7467);
  --input: oklch(.4017 0 0);
  --ring: oklch(.9247 .0524 66.1732);
  --chart-1: oklch(.9247 .0524 66.1732);
  --chart-2: oklch(.3163 .019 63.6992);
  --chart-3: oklch(.285 0 0);
  --chart-4: oklch(.3481 .0219 67.0001);
  --chart-5: oklch(.9245 .0533 67.0855);
  --sidebar: oklch(.2103 .0059 285.885);
  --sidebar-foreground: oklch(.9674 .0013 286.375);
  --sidebar-primary: oklch(.4882 .2172 264.376);
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(.2739 .0055 286.033);
  --sidebar-accent-foreground: oklch(.9674 .0013 286.375);
  --sidebar-border: oklch(.2739 .0055 286.033);
  --sidebar-ring: oklch(.8711 .0055 286.286);
  --font-sans: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  --radius: .5rem;
  --shadow-2xs: 0 1px 3px 0px #0000000d;
  --shadow-xs: 0 1px 3px 0px #0000000d;
  --shadow-sm: 0 1px 3px 0px #0000001a, 0 1px 2px -1px #0000001a;
  --shadow: 0 1px 3px 0px #0000001a, 0 1px 2px -1px #0000001a;
  --shadow-md: 0 1px 3px 0px #0000001a, 0 2px 4px -1px #0000001a;
  --shadow-lg: 0 1px 3px 0px #0000001a, 0 4px 6px -1px #0000001a;
  --shadow-xl: 0 1px 3px 0px #0000001a, 0 8px 10px -1px #0000001a;
  --shadow-2xl: 0 1px 3px 0px #00000040;
}

@keyframes pulse-fade {
  0% {
    opacity: 1;
    transform: scale(.25);
  }

  100% {
    opacity: 0;
    transform: scale(1);
  }
}

.animate-pulse-fade-out {
  animation: 2s cubic-bezier(.4, 0, .6, 1) infinite pulse-fade;
}

@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-rotate-x {
  syntax: "*";
  inherits: false
}

@property --tw-rotate-y {
  syntax: "*";
  inherits: false
}

@property --tw-rotate-z {
  syntax: "*";
  inherits: false
}

@property --tw-skew-x {
  syntax: "*";
  inherits: false
}

@property --tw-skew-y {
  syntax: "*";
  inherits: false
}

@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-gradient-position {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-from {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}

@property --tw-gradient-via {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}

@property --tw-gradient-to {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}

@property --tw-gradient-stops {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-via-stops {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-from-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 0%;
}

@property --tw-gradient-via-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 50%;
}

@property --tw-gradient-to-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-leading {
  syntax: "*";
  inherits: false
}

@property --tw-font-weight {
  syntax: "*";
  inherits: false
}

@property --tw-tracking {
  syntax: "*";
  inherits: false
}

@property --tw-ordinal {
  syntax: "*";
  inherits: false
}

@property --tw-slashed-zero {
  syntax: "*";
  inherits: false
}

@property --tw-numeric-figure {
  syntax: "*";
  inherits: false
}

@property --tw-numeric-spacing {
  syntax: "*";
  inherits: false
}

@property --tw-numeric-fraction {
  syntax: "*";
  inherits: false
}

@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-ring-inset {
  syntax: "*";
  inherits: false
}

@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0;
}

@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}

@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-outline-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-blur {
  syntax: "*";
  inherits: false
}

@property --tw-brightness {
  syntax: "*";
  inherits: false
}

@property --tw-contrast {
  syntax: "*";
  inherits: false
}

@property --tw-grayscale {
  syntax: "*";
  inherits: false
}

@property --tw-hue-rotate {
  syntax: "*";
  inherits: false
}

@property --tw-invert {
  syntax: "*";
  inherits: false
}

@property --tw-opacity {
  syntax: "*";
  inherits: false
}

@property --tw-saturate {
  syntax: "*";
  inherits: false
}

@property --tw-sepia {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-drop-shadow-size {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-blur {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-brightness {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-contrast {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-grayscale {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-hue-rotate {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-invert {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-opacity {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-saturate {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-sepia {
  syntax: "*";
  inherits: false
}

@property --tw-duration {
  syntax: "*";
  inherits: false
}

@property --tw-ease {
  syntax: "*";
  inherits: false
}

@property --tw-content {
  syntax: "*";
  inherits: false;
  initial-value: "";
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  50% {
    opacity: .5;
  }
}

@keyframes enter {
  from {
    opacity: var(--tw-enter-opacity, 1);
    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));
  }
}

@keyframes exit {
  to {
    opacity: var(--tw-exit-opacity, 1);
    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));
  }
}


/*# sourceMappingURL=%5Broot-of-the-server%5D__36bd7e9c._.css.map*/