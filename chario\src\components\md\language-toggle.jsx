'use client'

import React from 'react'
import { useId } from "react"
import { useRouter, usePathname } from 'next/navigation'
import { useLocale, useTranslations } from 'next-intl'

import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { CheckIcon, MinusIcon } from 'lucide-react'
import { cn } from '@/lib/utils'

const languages = [
    { value: "ru", label: "Русский" },
    { value: "en", label: "English" },
]

function LanguageToggle() {
    const t = useTranslations('settings');
    const locale = useLocale();
    const router = useRouter();
    const pathname = usePathname();

    const id = useId()

    const handleLanguageChange = (newLocale) => {
        // Remove the current locale from the pathname
        const pathWithoutLocale = pathname.replace(`/${locale}`, '');
        // Navigate to the new locale
        router.push(`/${newLocale}${pathWithoutLocale}`);
    };

    return (
        <fieldset className="space-y-4">
            <RadioGroup 
                className="flex gap-3" 
                value={locale} 
                onValueChange={handleLanguageChange}
            >
                {languages.map((language) => (
                    <label key={`${id}-${language.value}`}>
                        <RadioGroupItem
                            id={`${id}-${language.value}`}
                            value={language.value}
                            className="peer sr-only after:absolute after:inset-0"
                        />
                        <div className={cn(
                            "border-input peer-focus-visible:ring-ring/50 border-2 peer-data-[state=checked]:border-primary peer-data-[state=checked]:bg-accent relative cursor-pointer overflow-hidden rounded-md shadow-xs transition-[color,box-shadow] outline-none peer-focus-visible:ring-[3px] peer-data-disabled:cursor-not-allowed peer-data-disabled:opacity-50",
                            "px-4 py-3 min-w-[100px] text-center"
                        )}>
                            <span className="text-sm font-medium">{language.label}</span>
                        </div>
                        <span className="group peer-data-[state=unchecked]:text-muted-foreground/70 mt-2 flex items-center gap-1 justify-center">
                            <CheckIcon
                                size={16}
                                className="group-peer-data-[state=unchecked]:hidden"
                                aria-hidden="true"
                            />
                            <MinusIcon
                                size={16}
                                className="group-peer-data-[state=checked]:hidden"
                                aria-hidden="true"
                            />
                            <span className="text-xs font-medium">{t(language.value === 'ru' ? 'russian' : 'english')}</span>
                        </span>
                    </label>
                ))}
            </RadioGroup>
        </fieldset>
    )
}

export default LanguageToggle
