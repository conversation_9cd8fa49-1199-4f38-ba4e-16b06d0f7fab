'use client'

import React from 'react'
import { Button } from '@/components/ui/button'
import Link from 'next/link'
import { authClient, signOut, useSession } from '@/lib/auth-client'
import { useTranslations } from '@/contexts/LanguageContext'
import { useRouter } from 'next/navigation'

function NotAuthenticated() {
  const t = useTranslations();
  const router = useRouter()
  const sessionData = useSession();
  return (
    <div className='h-full w-full flex flex-col items-center justify-center p-8 text-center'>
      <h1 className='font-bold text-2xl'>{t('auth.notAuthorized')}</h1>
      <p className='text-muted-foreground text-sm mb-8'>{t('auth.notAuthorizedDescription')}</p>
      <Button
        className={'cursor-pointer'}
        onClick={async () => {
          router.push('/signin')	
          await signOut({
            fetchOptions: {
              onSuccess: () => {
                // router.push("/signin")
                router.push('/signin')
                // toast.success('You have been logged out')
              }
            }
          })
        }}
      >
        {t('auth.signIn')}
      </Button>
    </div >
  )
}

export default NotAuthenticated