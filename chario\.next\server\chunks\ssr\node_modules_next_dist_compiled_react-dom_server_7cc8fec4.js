module.exports = {

"[project]/node_modules/next/dist/compiled/react-dom/server.js [app-rsc] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_next_dist_compiled_c0df4ca7._.js",
  "server/chunks/ssr/[root-of-the-server]__6052af13._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/next/dist/compiled/react-dom/server.js [app-rsc] (ecmascript)");
    });
});
}}),

};